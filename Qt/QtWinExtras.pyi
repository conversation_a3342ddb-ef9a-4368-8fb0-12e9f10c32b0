# This Python file uses the following encoding: utf-8
#############################################################################
##
## Copyright (C) 2020 The Qt Company Ltd.
## Contact: https://www.qt.io/licensing/
##
## This file is part of Qt for Python.
##
## $QT_BEGIN_LICENSE:LGPL$
## Commercial License Usage
## Licensees holding valid commercial Qt licenses may use this file in
## accordance with the commercial license agreement provided with the
## Software or, alternatively, in accordance with the terms contained in
## a written agreement between you and The Qt Company. For licensing terms
## and conditions see https://www.qt.io/terms-conditions. For further
## information use the contact form at https://www.qt.io/contact-us.
##
## GNU Lesser General Public License Usage
## Alternatively, this file may be used under the terms of the GNU Lesser
## General Public License version 3 as published by the Free Software
## Foundation and appearing in the file LICENSE.LGPL3 included in the
## packaging of this file. Please review the following information to
## ensure the GNU Lesser General Public License version 3 requirements
## will be met: https://www.gnu.org/licenses/lgpl-3.0.html.
##
## GNU General Public License Usage
## Alternatively, this file may be used under the terms of the GNU
## General Public License version 2.0 or (at your option) the GNU General
## Public license version 3 or any later version approved by the KDE Free
## Qt Foundation. The licenses are as published by the Free Software
## Foundation and appearing in the file LICENSE.GPL2 and LICENSE.GPL3
## included in the packaging of this file. Please review the following
## information to ensure the GNU General Public License requirements will
## be met: https://www.gnu.org/licenses/gpl-2.0.html and
## https://www.gnu.org/licenses/gpl-3.0.html.
##
## $QT_END_LICENSE$
##
#############################################################################

"""
This file contains the exact signatures for all functions in module
PySide2.QtWinExtras, except for defaults which are replaced by "...".
"""

# Module PySide2.QtWinExtras
import PySide2
try:
    import typing
except ImportError:
    from PySide2.support.signature import typing
from PySide2.support.signature.mapping import (
    Virtual, Missing, Invalid, Default, Instance)

class Object(object): pass

import shiboken2 as Shiboken
Shiboken.Object = Object

import PySide2.QtCore
import PySide2.QtGui
import PySide2.QtWinExtras


class QWinColorizationChangeEvent(PySide2.QtWinExtras.QWinEvent):

    def __init__(self, color:int, opaque:bool): ...

    def color(self) -> int: ...
    def opaqueBlend(self) -> bool: ...


class QWinCompositionChangeEvent(PySide2.QtWinExtras.QWinEvent):

    def __init__(self, enabled:bool): ...

    def isCompositionEnabled(self) -> bool: ...


class QWinEvent(PySide2.QtCore.QEvent):

    def __init__(self, type:int): ...


class QWinJumpList(PySide2.QtCore.QObject):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    @typing.overload
    def addCategory(self, category:PySide2.QtWinExtras.QWinJumpListCategory): ...
    @typing.overload
    def addCategory(self, title:str, items:typing.Sequence=...) -> PySide2.QtWinExtras.QWinJumpListCategory: ...
    def categories(self) -> typing.List: ...
    def clear(self): ...
    def frequent(self) -> PySide2.QtWinExtras.QWinJumpListCategory: ...
    def identifier(self) -> str: ...
    def recent(self) -> PySide2.QtWinExtras.QWinJumpListCategory: ...
    def setIdentifier(self, identifier:str): ...
    def tasks(self) -> PySide2.QtWinExtras.QWinJumpListCategory: ...


class QWinJumpListCategory(Shiboken.Object):
    Custom                   : QWinJumpListCategory = ... # 0x0
    Recent                   : QWinJumpListCategory = ... # 0x1
    Frequent                 : QWinJumpListCategory = ... # 0x2
    Tasks                    : QWinJumpListCategory = ... # 0x3

    class Type(object):
        Custom                   : QWinJumpListCategory.Type = ... # 0x0
        Recent                   : QWinJumpListCategory.Type = ... # 0x1
        Frequent                 : QWinJumpListCategory.Type = ... # 0x2
        Tasks                    : QWinJumpListCategory.Type = ... # 0x3

    def __init__(self, title:str=...): ...

    def addDestination(self, filePath:str) -> PySide2.QtWinExtras.QWinJumpListItem: ...
    def addItem(self, item:PySide2.QtWinExtras.QWinJumpListItem): ...
    @typing.overload
    def addLink(self, icon:PySide2.QtGui.QIcon, title:str, executablePath:str, arguments:typing.Sequence=...) -> PySide2.QtWinExtras.QWinJumpListItem: ...
    @typing.overload
    def addLink(self, title:str, executablePath:str, arguments:typing.Sequence=...) -> PySide2.QtWinExtras.QWinJumpListItem: ...
    def addSeparator(self) -> PySide2.QtWinExtras.QWinJumpListItem: ...
    def clear(self): ...
    def count(self) -> int: ...
    def isEmpty(self) -> bool: ...
    def isVisible(self) -> bool: ...
    def items(self) -> typing.List: ...
    def setTitle(self, title:str): ...
    def setVisible(self, visible:bool): ...
    def title(self) -> str: ...
    def type(self) -> PySide2.QtWinExtras.QWinJumpListCategory.Type: ...


class QWinJumpListItem(Shiboken.Object):
    Destination              : QWinJumpListItem = ... # 0x0
    Link                     : QWinJumpListItem = ... # 0x1
    Separator                : QWinJumpListItem = ... # 0x2

    class Type(object):
        Destination              : QWinJumpListItem.Type = ... # 0x0
        Link                     : QWinJumpListItem.Type = ... # 0x1
        Separator                : QWinJumpListItem.Type = ... # 0x2

    def __init__(self, type:PySide2.QtWinExtras.QWinJumpListItem.Type): ...

    def arguments(self) -> typing.List: ...
    def description(self) -> str: ...
    def filePath(self) -> str: ...
    def icon(self) -> PySide2.QtGui.QIcon: ...
    def setArguments(self, arguments:typing.Sequence): ...
    def setDescription(self, description:str): ...
    def setFilePath(self, filePath:str): ...
    def setIcon(self, icon:PySide2.QtGui.QIcon): ...
    def setTitle(self, title:str): ...
    def setType(self, type:PySide2.QtWinExtras.QWinJumpListItem.Type): ...
    def setWorkingDirectory(self, workingDirectory:str): ...
    def title(self) -> str: ...
    def type(self) -> PySide2.QtWinExtras.QWinJumpListItem.Type: ...
    def workingDirectory(self) -> str: ...


class QWinTaskbarButton(PySide2.QtCore.QObject):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def clearOverlayIcon(self): ...
    def eventFilter(self, arg__1:PySide2.QtCore.QObject, arg__2:PySide2.QtCore.QEvent) -> bool: ...
    def overlayAccessibleDescription(self) -> str: ...
    def overlayIcon(self) -> PySide2.QtGui.QIcon: ...
    def progress(self) -> PySide2.QtWinExtras.QWinTaskbarProgress: ...
    def setOverlayAccessibleDescription(self, description:str): ...
    def setOverlayIcon(self, icon:PySide2.QtGui.QIcon): ...
    def setWindow(self, window:PySide2.QtGui.QWindow): ...
    def window(self) -> PySide2.QtGui.QWindow: ...


class QWinTaskbarProgress(PySide2.QtCore.QObject):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def hide(self): ...
    def isPaused(self) -> bool: ...
    def isStopped(self) -> bool: ...
    def isVisible(self) -> bool: ...
    def maximum(self) -> int: ...
    def minimum(self) -> int: ...
    def pause(self): ...
    def reset(self): ...
    def resume(self): ...
    def setMaximum(self, maximum:int): ...
    def setMinimum(self, minimum:int): ...
    def setPaused(self, paused:bool): ...
    def setRange(self, minimum:int, maximum:int): ...
    def setValue(self, value:int): ...
    def setVisible(self, visible:bool): ...
    def show(self): ...
    def stop(self): ...
    def value(self) -> int: ...


class QWinThumbnailToolBar(PySide2.QtCore.QObject):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def addButton(self, button:PySide2.QtWinExtras.QWinThumbnailToolButton): ...
    def buttons(self) -> typing.List: ...
    def clear(self): ...
    def count(self) -> int: ...
    def iconicLivePreviewPixmap(self) -> PySide2.QtGui.QPixmap: ...
    def iconicPixmapNotificationsEnabled(self) -> bool: ...
    def iconicThumbnailPixmap(self) -> PySide2.QtGui.QPixmap: ...
    def removeButton(self, button:PySide2.QtWinExtras.QWinThumbnailToolButton): ...
    def setButtons(self, buttons:typing.Sequence): ...
    def setIconicLivePreviewPixmap(self, arg__1:PySide2.QtGui.QPixmap): ...
    def setIconicPixmapNotificationsEnabled(self, enabled:bool): ...
    def setIconicThumbnailPixmap(self, arg__1:PySide2.QtGui.QPixmap): ...
    def setWindow(self, window:PySide2.QtGui.QWindow): ...
    def window(self) -> PySide2.QtGui.QWindow: ...


class QWinThumbnailToolButton(PySide2.QtCore.QObject):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def click(self): ...
    def dismissOnClick(self) -> bool: ...
    def icon(self) -> PySide2.QtGui.QIcon: ...
    def isEnabled(self) -> bool: ...
    def isFlat(self) -> bool: ...
    def isInteractive(self) -> bool: ...
    def isVisible(self) -> bool: ...
    def setDismissOnClick(self, dismiss:bool): ...
    def setEnabled(self, enabled:bool): ...
    def setFlat(self, flat:bool): ...
    def setIcon(self, icon:PySide2.QtGui.QIcon): ...
    def setInteractive(self, interactive:bool): ...
    def setToolTip(self, toolTip:str): ...
    def setVisible(self, visible:bool): ...
    def toolTip(self) -> str: ...


class QtWin(Shiboken.Object):
    FlipDefault              : QtWin = ... # 0x0
    HBitmapNoAlpha           : QtWin = ... # 0x0
    FlipExcludeBelow         : QtWin = ... # 0x1
    HBitmapPremultipliedAlpha: QtWin = ... # 0x1
    FlipExcludeAbove         : QtWin = ... # 0x2
    HBitmapAlpha             : QtWin = ... # 0x2

    class HBitmapFormat(object):
        HBitmapNoAlpha           : QtWin.HBitmapFormat = ... # 0x0
        HBitmapPremultipliedAlpha: QtWin.HBitmapFormat = ... # 0x1
        HBitmapAlpha             : QtWin.HBitmapFormat = ... # 0x2

    class WindowFlip3DPolicy(object):
        FlipDefault              : QtWin.WindowFlip3DPolicy = ... # 0x0
        FlipExcludeBelow         : QtWin.WindowFlip3DPolicy = ... # 0x1
        FlipExcludeAbove         : QtWin.WindowFlip3DPolicy = ... # 0x2
    @staticmethod
    def colorizationColor() -> typing.Tuple: ...
    @staticmethod
    def disableBlurBehindWindow(window:PySide2.QtGui.QWindow): ...
    @typing.overload
    @staticmethod
    def enableBlurBehindWindow(window:PySide2.QtGui.QWindow): ...
    @typing.overload
    @staticmethod
    def enableBlurBehindWindow(window:PySide2.QtGui.QWindow, region:PySide2.QtGui.QRegion): ...
    @staticmethod
    def errorStringFromHresult(hresult:int) -> str: ...
    @typing.overload
    @staticmethod
    def extendFrameIntoClientArea(window:PySide2.QtGui.QWindow, left:int, top:int, right:int, bottom:int): ...
    @typing.overload
    @staticmethod
    def extendFrameIntoClientArea(window:PySide2.QtGui.QWindow, margins:PySide2.QtCore.QMargins): ...
    @staticmethod
    def isCompositionEnabled() -> bool: ...
    @staticmethod
    def isCompositionOpaque() -> bool: ...
    @staticmethod
    def isWindowExcludedFromPeek(window:PySide2.QtGui.QWindow) -> bool: ...
    @staticmethod
    def isWindowPeekDisallowed(window:PySide2.QtGui.QWindow) -> bool: ...
    @staticmethod
    def markFullscreenWindow(arg__1:PySide2.QtGui.QWindow, fullscreen:bool=...): ...
    @staticmethod
    def realColorizationColor() -> PySide2.QtGui.QColor: ...
    @staticmethod
    def resetExtendedFrame(window:PySide2.QtGui.QWindow): ...
    @staticmethod
    def setCompositionEnabled(enabled:bool): ...
    @staticmethod
    def setCurrentProcessExplicitAppUserModelID(id:str): ...
    @staticmethod
    def setWindowDisallowPeek(window:PySide2.QtGui.QWindow, disallow:bool): ...
    @staticmethod
    def setWindowExcludedFromPeek(window:PySide2.QtGui.QWindow, exclude:bool): ...
    @staticmethod
    def setWindowFlip3DPolicy(window:PySide2.QtGui.QWindow, policy:PySide2.QtWinExtras.QtWin.WindowFlip3DPolicy): ...
    @staticmethod
    def stringFromHresult(hresult:int) -> str: ...
    @staticmethod
    def taskbarActivateTab(arg__1:PySide2.QtGui.QWindow): ...
    @staticmethod
    def taskbarActivateTabAlt(arg__1:PySide2.QtGui.QWindow): ...
    @staticmethod
    def taskbarAddTab(arg__1:PySide2.QtGui.QWindow): ...
    @staticmethod
    def taskbarDeleteTab(arg__1:PySide2.QtGui.QWindow): ...
    @staticmethod
    def windowFlip3DPolicy(arg__1:PySide2.QtGui.QWindow) -> PySide2.QtWinExtras.QtWin.WindowFlip3DPolicy: ...

# eof
