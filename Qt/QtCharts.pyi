# This Python file uses the following encoding: utf-8
#############################################################################
##
## Copyright (C) 2020 The Qt Company Ltd.
## Contact: https://www.qt.io/licensing/
##
## This file is part of Qt for Python.
##
## $QT_BEGIN_LICENSE:LGPL$
## Commercial License Usage
## Licensees holding valid commercial Qt licenses may use this file in
## accordance with the commercial license agreement provided with the
## Software or, alternatively, in accordance with the terms contained in
## a written agreement between you and The Qt Company. For licensing terms
## and conditions see https://www.qt.io/terms-conditions. For further
## information use the contact form at https://www.qt.io/contact-us.
##
## GNU Lesser General Public License Usage
## Alternatively, this file may be used under the terms of the GNU Lesser
## General Public License version 3 as published by the Free Software
## Foundation and appearing in the file LICENSE.LGPL3 included in the
## packaging of this file. Please review the following information to
## ensure the GNU Lesser General Public License version 3 requirements
## will be met: https://www.gnu.org/licenses/lgpl-3.0.html.
##
## GNU General Public License Usage
## Alternatively, this file may be used under the terms of the GNU
## General Public License version 2.0 or (at your option) the GNU General
## Public license version 3 or any later version approved by the KDE Free
## Qt Foundation. The licenses are as published by the Free Software
## Foundation and appearing in the file LICENSE.GPL2 and LICENSE.GPL3
## included in the packaging of this file. Please review the following
## information to ensure the GNU General Public License requirements will
## be met: https://www.gnu.org/licenses/gpl-2.0.html and
## https://www.gnu.org/licenses/gpl-3.0.html.
##
## $QT_END_LICENSE$
##
#############################################################################

"""
This file contains the exact signatures for all functions in module
PySide2.QtCharts, except for defaults which are replaced by "...".
"""

# Module PySide2.QtCharts
import PySide2
try:
    import typing
except ImportError:
    from PySide2.support.signature import typing
from PySide2.support.signature.mapping import (
    Virtual, Missing, Invalid, Default, Instance)

class Object(object): pass

import shiboken2 as Shiboken
Shiboken.Object = Object

import PySide2.QtCore
import PySide2.QtGui
import PySide2.QtWidgets
import PySide2.QtCharts


class QtCharts(Shiboken.Object):

    class QAbstractAxis(PySide2.QtCore.QObject):
        AxisTypeNoAxis           : QtCharts.QAbstractAxis = ... # 0x0
        AxisTypeValue            : QtCharts.QAbstractAxis = ... # 0x1
        AxisTypeBarCategory      : QtCharts.QAbstractAxis = ... # 0x2
        AxisTypeCategory         : QtCharts.QAbstractAxis = ... # 0x4
        AxisTypeDateTime         : QtCharts.QAbstractAxis = ... # 0x8
        AxisTypeLogValue         : QtCharts.QAbstractAxis = ... # 0x10

        class AxisType(object):
            AxisTypeNoAxis           : QtCharts.QAbstractAxis.AxisType = ... # 0x0
            AxisTypeValue            : QtCharts.QAbstractAxis.AxisType = ... # 0x1
            AxisTypeBarCategory      : QtCharts.QAbstractAxis.AxisType = ... # 0x2
            AxisTypeCategory         : QtCharts.QAbstractAxis.AxisType = ... # 0x4
            AxisTypeDateTime         : QtCharts.QAbstractAxis.AxisType = ... # 0x8
            AxisTypeLogValue         : QtCharts.QAbstractAxis.AxisType = ... # 0x10
        def alignment(self) -> PySide2.QtCore.Qt.Alignment: ...
        def gridLineColor(self) -> PySide2.QtGui.QColor: ...
        def gridLinePen(self) -> PySide2.QtGui.QPen: ...
        def hide(self): ...
        def isGridLineVisible(self) -> bool: ...
        def isLineVisible(self) -> bool: ...
        def isMinorGridLineVisible(self) -> bool: ...
        def isReverse(self) -> bool: ...
        def isTitleVisible(self) -> bool: ...
        def isVisible(self) -> bool: ...
        def labelsAngle(self) -> int: ...
        def labelsBrush(self) -> PySide2.QtGui.QBrush: ...
        def labelsColor(self) -> PySide2.QtGui.QColor: ...
        def labelsEditable(self) -> bool: ...
        def labelsFont(self) -> PySide2.QtGui.QFont: ...
        def labelsVisible(self) -> bool: ...
        def linePen(self) -> PySide2.QtGui.QPen: ...
        def linePenColor(self) -> PySide2.QtGui.QColor: ...
        def minorGridLineColor(self) -> PySide2.QtGui.QColor: ...
        def minorGridLinePen(self) -> PySide2.QtGui.QPen: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def setGridLineColor(self, color:PySide2.QtGui.QColor): ...
        def setGridLinePen(self, pen:PySide2.QtGui.QPen): ...
        def setGridLineVisible(self, visible:bool=...): ...
        def setLabelsAngle(self, angle:int): ...
        def setLabelsBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setLabelsColor(self, color:PySide2.QtGui.QColor): ...
        def setLabelsEditable(self, editable:bool=...): ...
        def setLabelsFont(self, font:PySide2.QtGui.QFont): ...
        def setLabelsVisible(self, visible:bool=...): ...
        def setLinePen(self, pen:PySide2.QtGui.QPen): ...
        def setLinePenColor(self, color:PySide2.QtGui.QColor): ...
        def setLineVisible(self, visible:bool=...): ...
        def setMax(self, max:typing.Any): ...
        def setMin(self, min:typing.Any): ...
        def setMinorGridLineColor(self, color:PySide2.QtGui.QColor): ...
        def setMinorGridLinePen(self, pen:PySide2.QtGui.QPen): ...
        def setMinorGridLineVisible(self, visible:bool=...): ...
        def setRange(self, min:typing.Any, max:typing.Any): ...
        def setReverse(self, reverse:bool=...): ...
        def setShadesBorderColor(self, color:PySide2.QtGui.QColor): ...
        def setShadesBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setShadesColor(self, color:PySide2.QtGui.QColor): ...
        def setShadesPen(self, pen:PySide2.QtGui.QPen): ...
        def setShadesVisible(self, visible:bool=...): ...
        def setTitleBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setTitleFont(self, font:PySide2.QtGui.QFont): ...
        def setTitleText(self, title:str): ...
        def setTitleVisible(self, visible:bool=...): ...
        def setVisible(self, visible:bool=...): ...
        def shadesBorderColor(self) -> PySide2.QtGui.QColor: ...
        def shadesBrush(self) -> PySide2.QtGui.QBrush: ...
        def shadesColor(self) -> PySide2.QtGui.QColor: ...
        def shadesPen(self) -> PySide2.QtGui.QPen: ...
        def shadesVisible(self) -> bool: ...
        def show(self): ...
        def titleBrush(self) -> PySide2.QtGui.QBrush: ...
        def titleFont(self) -> PySide2.QtGui.QFont: ...
        def titleText(self) -> str: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractAxis.AxisType: ...

    class QAbstractBarSeries(PySide2.QtCharts.QAbstractSeries):
        LabelsCenter             : QtCharts.QAbstractBarSeries = ... # 0x0
        LabelsInsideEnd          : QtCharts.QAbstractBarSeries = ... # 0x1
        LabelsInsideBase         : QtCharts.QAbstractBarSeries = ... # 0x2
        LabelsOutsideEnd         : QtCharts.QAbstractBarSeries = ... # 0x3

        class LabelsPosition(object):
            LabelsCenter             : QtCharts.QAbstractBarSeries.LabelsPosition = ... # 0x0
            LabelsInsideEnd          : QtCharts.QAbstractBarSeries.LabelsPosition = ... # 0x1
            LabelsInsideBase         : QtCharts.QAbstractBarSeries.LabelsPosition = ... # 0x2
            LabelsOutsideEnd         : QtCharts.QAbstractBarSeries.LabelsPosition = ... # 0x3
        @typing.overload
        def append(self, set:PySide2.QtCharts.QtCharts.QBarSet) -> bool: ...
        @typing.overload
        def append(self, sets:typing.Sequence) -> bool: ...
        def barSets(self) -> typing.List: ...
        def barWidth(self) -> float: ...
        def clear(self): ...
        def count(self) -> int: ...
        def insert(self, index:int, set:PySide2.QtCharts.QtCharts.QBarSet) -> bool: ...
        def isLabelsVisible(self) -> bool: ...
        def labelsAngle(self) -> float: ...
        def labelsFormat(self) -> str: ...
        def labelsPosition(self) -> PySide2.QtCharts.QtCharts.QAbstractBarSeries.LabelsPosition: ...
        def labelsPrecision(self) -> int: ...
        def remove(self, set:PySide2.QtCharts.QtCharts.QBarSet) -> bool: ...
        def setBarWidth(self, width:float): ...
        def setLabelsAngle(self, angle:float): ...
        def setLabelsFormat(self, format:str): ...
        def setLabelsPosition(self, position:PySide2.QtCharts.QtCharts.QAbstractBarSeries.LabelsPosition): ...
        def setLabelsPrecision(self, precision:int): ...
        def setLabelsVisible(self, visible:bool=...): ...
        def take(self, set:PySide2.QtCharts.QtCharts.QBarSet) -> bool: ...

    class QAbstractSeries(PySide2.QtCore.QObject):
        SeriesTypeLine           : QtCharts.QAbstractSeries = ... # 0x0
        SeriesTypeArea           : QtCharts.QAbstractSeries = ... # 0x1
        SeriesTypeBar            : QtCharts.QAbstractSeries = ... # 0x2
        SeriesTypeStackedBar     : QtCharts.QAbstractSeries = ... # 0x3
        SeriesTypePercentBar     : QtCharts.QAbstractSeries = ... # 0x4
        SeriesTypePie            : QtCharts.QAbstractSeries = ... # 0x5
        SeriesTypeScatter        : QtCharts.QAbstractSeries = ... # 0x6
        SeriesTypeSpline         : QtCharts.QAbstractSeries = ... # 0x7
        SeriesTypeHorizontalBar  : QtCharts.QAbstractSeries = ... # 0x8
        SeriesTypeHorizontalStackedBar: QtCharts.QAbstractSeries = ... # 0x9
        SeriesTypeHorizontalPercentBar: QtCharts.QAbstractSeries = ... # 0xa
        SeriesTypeBoxPlot        : QtCharts.QAbstractSeries = ... # 0xb
        SeriesTypeCandlestick    : QtCharts.QAbstractSeries = ... # 0xc

        class SeriesType(object):
            SeriesTypeLine           : QtCharts.QAbstractSeries.SeriesType = ... # 0x0
            SeriesTypeArea           : QtCharts.QAbstractSeries.SeriesType = ... # 0x1
            SeriesTypeBar            : QtCharts.QAbstractSeries.SeriesType = ... # 0x2
            SeriesTypeStackedBar     : QtCharts.QAbstractSeries.SeriesType = ... # 0x3
            SeriesTypePercentBar     : QtCharts.QAbstractSeries.SeriesType = ... # 0x4
            SeriesTypePie            : QtCharts.QAbstractSeries.SeriesType = ... # 0x5
            SeriesTypeScatter        : QtCharts.QAbstractSeries.SeriesType = ... # 0x6
            SeriesTypeSpline         : QtCharts.QAbstractSeries.SeriesType = ... # 0x7
            SeriesTypeHorizontalBar  : QtCharts.QAbstractSeries.SeriesType = ... # 0x8
            SeriesTypeHorizontalStackedBar: QtCharts.QAbstractSeries.SeriesType = ... # 0x9
            SeriesTypeHorizontalPercentBar: QtCharts.QAbstractSeries.SeriesType = ... # 0xa
            SeriesTypeBoxPlot        : QtCharts.QAbstractSeries.SeriesType = ... # 0xb
            SeriesTypeCandlestick    : QtCharts.QAbstractSeries.SeriesType = ... # 0xc
        def attachAxis(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis) -> bool: ...
        def attachedAxes(self) -> typing.List: ...
        def chart(self) -> PySide2.QtCharts.QtCharts.QChart: ...
        def detachAxis(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis) -> bool: ...
        def hide(self): ...
        def isVisible(self) -> bool: ...
        def name(self) -> str: ...
        def opacity(self) -> float: ...
        def setName(self, name:str): ...
        def setOpacity(self, opacity:float): ...
        def setUseOpenGL(self, enable:bool=...): ...
        def setVisible(self, visible:bool=...): ...
        def show(self): ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...
        def useOpenGL(self) -> bool: ...

    class QAreaLegendMarker(PySide2.QtCharts.QLegendMarker):

        def __init__(self, series:PySide2.QtCharts.QtCharts.QAreaSeries, legend:PySide2.QtCharts.QtCharts.QLegend, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def series(self) -> PySide2.QtCharts.QtCharts.QAreaSeries: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QLegendMarker.LegendMarkerType: ...

    class QAreaSeries(PySide2.QtCharts.QAbstractSeries):

        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, upperSeries:PySide2.QtCharts.QtCharts.QLineSeries, lowerSeries:typing.Optional[PySide2.QtCharts.QtCharts.QLineSeries]=...): ...

        def borderColor(self) -> PySide2.QtGui.QColor: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def color(self) -> PySide2.QtGui.QColor: ...
        def lowerSeries(self) -> PySide2.QtCharts.QtCharts.QLineSeries: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def pointLabelsClipping(self) -> bool: ...
        def pointLabelsColor(self) -> PySide2.QtGui.QColor: ...
        def pointLabelsFont(self) -> PySide2.QtGui.QFont: ...
        def pointLabelsFormat(self) -> str: ...
        def pointLabelsVisible(self) -> bool: ...
        def pointsVisible(self) -> bool: ...
        def setBorderColor(self, color:PySide2.QtGui.QColor): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setColor(self, color:PySide2.QtGui.QColor): ...
        def setLowerSeries(self, series:PySide2.QtCharts.QtCharts.QLineSeries): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def setPointLabelsClipping(self, enabled:bool=...): ...
        def setPointLabelsColor(self, color:PySide2.QtGui.QColor): ...
        def setPointLabelsFont(self, font:PySide2.QtGui.QFont): ...
        def setPointLabelsFormat(self, format:str): ...
        def setPointLabelsVisible(self, visible:bool=...): ...
        def setPointsVisible(self, visible:bool=...): ...
        def setUpperSeries(self, series:PySide2.QtCharts.QtCharts.QLineSeries): ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...
        def upperSeries(self) -> PySide2.QtCharts.QtCharts.QLineSeries: ...

    class QBarCategoryAxis(PySide2.QtCharts.QAbstractAxis):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        @typing.overload
        def append(self, categories:typing.Sequence): ...
        @typing.overload
        def append(self, category:str): ...
        def at(self, index:int) -> str: ...
        def categories(self) -> typing.List: ...
        def clear(self): ...
        def count(self) -> int: ...
        def insert(self, index:int, category:str): ...
        def max(self) -> str: ...
        def min(self) -> str: ...
        def remove(self, category:str): ...
        def replace(self, oldCategory:str, newCategory:str): ...
        def setCategories(self, categories:typing.Sequence): ...
        @typing.overload
        def setMax(self, max:typing.Any): ...
        @typing.overload
        def setMax(self, maxCategory:str): ...
        @typing.overload
        def setMin(self, min:typing.Any): ...
        @typing.overload
        def setMin(self, minCategory:str): ...
        @typing.overload
        def setRange(self, min:typing.Any, max:typing.Any): ...
        @typing.overload
        def setRange(self, minCategory:str, maxCategory:str): ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractAxis.AxisType: ...

    class QBarLegendMarker(PySide2.QtCharts.QLegendMarker):

        def __init__(self, series:PySide2.QtCharts.QtCharts.QAbstractBarSeries, barset:PySide2.QtCharts.QtCharts.QBarSet, legend:PySide2.QtCharts.QtCharts.QLegend, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def barset(self) -> PySide2.QtCharts.QtCharts.QBarSet: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QAbstractBarSeries: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QLegendMarker.LegendMarkerType: ...

    class QBarModelMapper(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def count(self) -> int: ...
        def first(self) -> int: ...
        def firstBarSetSection(self) -> int: ...
        def lastBarSetSection(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QAbstractBarSeries: ...
        def setCount(self, count:int): ...
        def setFirst(self, first:int): ...
        def setFirstBarSetSection(self, firstBarSetSection:int): ...
        def setLastBarSetSection(self, lastBarSetSection:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setOrientation(self, orientation:PySide2.QtCore.Qt.Orientation): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QAbstractBarSeries): ...

    class QBarSeries(PySide2.QtCharts.QAbstractBarSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QBarSet(PySide2.QtCore.QObject):

        def __init__(self, label:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def __lshift__(self, value:float) -> PySide2.QtCharts.QtCharts.QBarSet: ...
        @typing.overload
        def append(self, value:float): ...
        @typing.overload
        def append(self, values:typing.Sequence): ...
        def at(self, index:int) -> float: ...
        def borderColor(self) -> PySide2.QtGui.QColor: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def color(self) -> PySide2.QtGui.QColor: ...
        def count(self) -> int: ...
        def insert(self, index:int, value:float): ...
        def label(self) -> str: ...
        def labelBrush(self) -> PySide2.QtGui.QBrush: ...
        def labelColor(self) -> PySide2.QtGui.QColor: ...
        def labelFont(self) -> PySide2.QtGui.QFont: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def remove(self, index:int, count:int=...): ...
        def replace(self, index:int, value:float): ...
        def setBorderColor(self, color:PySide2.QtGui.QColor): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setColor(self, color:PySide2.QtGui.QColor): ...
        def setLabel(self, label:str): ...
        def setLabelBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setLabelColor(self, color:PySide2.QtGui.QColor): ...
        def setLabelFont(self, font:PySide2.QtGui.QFont): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def sum(self) -> float: ...

    class QBoxPlotLegendMarker(PySide2.QtCharts.QLegendMarker):

        def __init__(self, series:PySide2.QtCharts.QtCharts.QBoxPlotSeries, legend:PySide2.QtCharts.QtCharts.QLegend, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def series(self) -> PySide2.QtCharts.QtCharts.QBoxPlotSeries: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QLegendMarker.LegendMarkerType: ...

    class QBoxPlotModelMapper(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def count(self) -> int: ...
        def first(self) -> int: ...
        def firstBoxSetSection(self) -> int: ...
        def lastBoxSetSection(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QBoxPlotSeries: ...
        def setCount(self, count:int): ...
        def setFirst(self, first:int): ...
        def setFirstBoxSetSection(self, firstBoxSetSection:int): ...
        def setLastBoxSetSection(self, lastBoxSetSection:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setOrientation(self, orientation:PySide2.QtCore.Qt.Orientation): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QBoxPlotSeries): ...

    class QBoxPlotSeries(PySide2.QtCharts.QAbstractSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        @typing.overload
        def append(self, box:PySide2.QtCharts.QtCharts.QBoxSet) -> bool: ...
        @typing.overload
        def append(self, boxes:typing.Sequence) -> bool: ...
        def boxOutlineVisible(self) -> bool: ...
        def boxSets(self) -> typing.List: ...
        def boxWidth(self) -> float: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def clear(self): ...
        def count(self) -> int: ...
        def insert(self, index:int, box:PySide2.QtCharts.QtCharts.QBoxSet) -> bool: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def remove(self, box:PySide2.QtCharts.QtCharts.QBoxSet) -> bool: ...
        def setBoxOutlineVisible(self, visible:bool): ...
        def setBoxWidth(self, width:float): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def take(self, box:PySide2.QtCharts.QtCharts.QBoxSet) -> bool: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QBoxSet(PySide2.QtCore.QObject):
        LowerExtreme             : QtCharts.QBoxSet = ... # 0x0
        LowerQuartile            : QtCharts.QBoxSet = ... # 0x1
        Median                   : QtCharts.QBoxSet = ... # 0x2
        UpperQuartile            : QtCharts.QBoxSet = ... # 0x3
        UpperExtreme             : QtCharts.QBoxSet = ... # 0x4

        class ValuePositions(object):
            LowerExtreme             : QtCharts.QBoxSet.ValuePositions = ... # 0x0
            LowerQuartile            : QtCharts.QBoxSet.ValuePositions = ... # 0x1
            Median                   : QtCharts.QBoxSet.ValuePositions = ... # 0x2
            UpperQuartile            : QtCharts.QBoxSet.ValuePositions = ... # 0x3
            UpperExtreme             : QtCharts.QBoxSet.ValuePositions = ... # 0x4

        @typing.overload
        def __init__(self, label:str=..., parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, le:float, lq:float, m:float, uq:float, ue:float, label:str=..., parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def __lshift__(self, value:float) -> PySide2.QtCharts.QtCharts.QBoxSet: ...
        @typing.overload
        def append(self, value:float): ...
        @typing.overload
        def append(self, values:typing.Sequence): ...
        def at(self, index:int) -> float: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def clear(self): ...
        def count(self) -> int: ...
        def label(self) -> str: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setLabel(self, label:str): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def setValue(self, index:int, value:float): ...

    class QCandlestickLegendMarker(PySide2.QtCharts.QLegendMarker):

        def __init__(self, series:PySide2.QtCharts.QtCharts.QCandlestickSeries, legend:PySide2.QtCharts.QtCharts.QLegend, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def series(self) -> PySide2.QtCharts.QtCharts.QCandlestickSeries: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QLegendMarker.LegendMarkerType: ...

    class QCandlestickModelMapper(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def close(self) -> int: ...
        def firstSetSection(self) -> int: ...
        def high(self) -> int: ...
        def lastSetSection(self) -> int: ...
        def low(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def open(self) -> int: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QCandlestickSeries: ...
        def setClose(self, close:int): ...
        def setFirstSetSection(self, firstSetSection:int): ...
        def setHigh(self, high:int): ...
        def setLastSetSection(self, lastSetSection:int): ...
        def setLow(self, low:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setOpen(self, open:int): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QCandlestickSeries): ...
        def setTimestamp(self, timestamp:int): ...
        def timestamp(self) -> int: ...

    class QCandlestickSeries(PySide2.QtCharts.QAbstractSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        @typing.overload
        def append(self, set:PySide2.QtCharts.QtCharts.QCandlestickSet) -> bool: ...
        @typing.overload
        def append(self, sets:typing.Sequence) -> bool: ...
        def bodyOutlineVisible(self) -> bool: ...
        def bodyWidth(self) -> float: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def capsVisible(self) -> bool: ...
        def capsWidth(self) -> float: ...
        def clear(self): ...
        def count(self) -> int: ...
        def decreasingColor(self) -> PySide2.QtGui.QColor: ...
        def increasingColor(self) -> PySide2.QtGui.QColor: ...
        def insert(self, index:int, set:PySide2.QtCharts.QtCharts.QCandlestickSet) -> bool: ...
        def maximumColumnWidth(self) -> float: ...
        def minimumColumnWidth(self) -> float: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        @typing.overload
        def remove(self, set:PySide2.QtCharts.QtCharts.QCandlestickSet) -> bool: ...
        @typing.overload
        def remove(self, sets:typing.Sequence) -> bool: ...
        def setBodyOutlineVisible(self, bodyOutlineVisible:bool): ...
        def setBodyWidth(self, bodyWidth:float): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setCapsVisible(self, capsVisible:bool): ...
        def setCapsWidth(self, capsWidth:float): ...
        def setDecreasingColor(self, decreasingColor:PySide2.QtGui.QColor): ...
        def setIncreasingColor(self, increasingColor:PySide2.QtGui.QColor): ...
        def setMaximumColumnWidth(self, maximumColumnWidth:float): ...
        def setMinimumColumnWidth(self, minimumColumnWidth:float): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def sets(self) -> typing.List: ...
        def take(self, set:PySide2.QtCharts.QtCharts.QCandlestickSet) -> bool: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QCandlestickSet(PySide2.QtCore.QObject):

        @typing.overload
        def __init__(self, open:float, high:float, low:float, close:float, timestamp:float=..., parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, timestamp:float=..., parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def brush(self) -> PySide2.QtGui.QBrush: ...
        def close(self) -> float: ...
        def high(self) -> float: ...
        def low(self) -> float: ...
        def open(self) -> float: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setClose(self, close:float): ...
        def setHigh(self, high:float): ...
        def setLow(self, low:float): ...
        def setOpen(self, open:float): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def setTimestamp(self, timestamp:float): ...
        def timestamp(self) -> float: ...

    class QCategoryAxis(PySide2.QtCharts.QValueAxis):
        AxisLabelsPositionCenter : QtCharts.QCategoryAxis = ... # 0x0
        AxisLabelsPositionOnValue: QtCharts.QCategoryAxis = ... # 0x1

        class AxisLabelsPosition(object):
            AxisLabelsPositionCenter : QtCharts.QCategoryAxis.AxisLabelsPosition = ... # 0x0
            AxisLabelsPositionOnValue: QtCharts.QCategoryAxis.AxisLabelsPosition = ... # 0x1

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def append(self, label:str, categoryEndValue:float): ...
        def categoriesLabels(self) -> typing.List: ...
        def count(self) -> int: ...
        def endValue(self, categoryLabel:str) -> float: ...
        def labelsPosition(self) -> PySide2.QtCharts.QtCharts.QCategoryAxis.AxisLabelsPosition: ...
        def remove(self, label:str): ...
        def replaceLabel(self, oldLabel:str, newLabel:str): ...
        def setLabelsPosition(self, position:PySide2.QtCharts.QtCharts.QCategoryAxis.AxisLabelsPosition): ...
        def setStartValue(self, min:float): ...
        def startValue(self, categoryLabel:str=...) -> float: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractAxis.AxisType: ...

    class QChart(PySide2.QtWidgets.QGraphicsWidget):
        ChartThemeLight          : QtCharts.QChart = ... # 0x0
        ChartTypeUndefined       : QtCharts.QChart = ... # 0x0
        NoAnimation              : QtCharts.QChart = ... # 0x0
        ChartThemeBlueCerulean   : QtCharts.QChart = ... # 0x1
        ChartTypeCartesian       : QtCharts.QChart = ... # 0x1
        GridAxisAnimations       : QtCharts.QChart = ... # 0x1
        ChartThemeDark           : QtCharts.QChart = ... # 0x2
        ChartTypePolar           : QtCharts.QChart = ... # 0x2
        SeriesAnimations         : QtCharts.QChart = ... # 0x2
        AllAnimations            : QtCharts.QChart = ... # 0x3
        ChartThemeBrownSand      : QtCharts.QChart = ... # 0x3
        ChartThemeBlueNcs        : QtCharts.QChart = ... # 0x4
        ChartThemeHighContrast   : QtCharts.QChart = ... # 0x5
        ChartThemeBlueIcy        : QtCharts.QChart = ... # 0x6
        ChartThemeQt             : QtCharts.QChart = ... # 0x7

        class AnimationOption(object):
            NoAnimation              : QtCharts.QChart.AnimationOption = ... # 0x0
            GridAxisAnimations       : QtCharts.QChart.AnimationOption = ... # 0x1
            SeriesAnimations         : QtCharts.QChart.AnimationOption = ... # 0x2
            AllAnimations            : QtCharts.QChart.AnimationOption = ... # 0x3

        class AnimationOptions(object): ...

        class ChartTheme(object):
            ChartThemeLight          : QtCharts.QChart.ChartTheme = ... # 0x0
            ChartThemeBlueCerulean   : QtCharts.QChart.ChartTheme = ... # 0x1
            ChartThemeDark           : QtCharts.QChart.ChartTheme = ... # 0x2
            ChartThemeBrownSand      : QtCharts.QChart.ChartTheme = ... # 0x3
            ChartThemeBlueNcs        : QtCharts.QChart.ChartTheme = ... # 0x4
            ChartThemeHighContrast   : QtCharts.QChart.ChartTheme = ... # 0x5
            ChartThemeBlueIcy        : QtCharts.QChart.ChartTheme = ... # 0x6
            ChartThemeQt             : QtCharts.QChart.ChartTheme = ... # 0x7

        class ChartType(object):
            ChartTypeUndefined       : QtCharts.QChart.ChartType = ... # 0x0
            ChartTypeCartesian       : QtCharts.QChart.ChartType = ... # 0x1
            ChartTypePolar           : QtCharts.QChart.ChartType = ... # 0x2

        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtWidgets.QGraphicsItem]=..., wFlags:PySide2.QtCore.Qt.WindowFlags=...): ...
        @typing.overload
        def __init__(self, type:PySide2.QtCharts.QtCharts.QChart.ChartType, parent:PySide2.QtWidgets.QGraphicsItem, wFlags:PySide2.QtCore.Qt.WindowFlags): ...

        def addAxis(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis, alignment:PySide2.QtCore.Qt.Alignment): ...
        def addSeries(self, series:PySide2.QtCharts.QtCharts.QAbstractSeries): ...
        def animationDuration(self) -> int: ...
        def animationEasingCurve(self) -> PySide2.QtCore.QEasingCurve: ...
        def animationOptions(self) -> PySide2.QtCharts.QtCharts.QChart.AnimationOptions: ...
        def axes(self, orientation:PySide2.QtCore.Qt.Orientations=..., series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...) -> typing.List: ...
        def axisX(self, series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...) -> PySide2.QtCharts.QtCharts.QAbstractAxis: ...
        def axisY(self, series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...) -> PySide2.QtCharts.QtCharts.QAbstractAxis: ...
        def backgroundBrush(self) -> PySide2.QtGui.QBrush: ...
        def backgroundPen(self) -> PySide2.QtGui.QPen: ...
        def backgroundRoundness(self) -> float: ...
        def chartType(self) -> PySide2.QtCharts.QtCharts.QChart.ChartType: ...
        def createDefaultAxes(self): ...
        def isBackgroundVisible(self) -> bool: ...
        def isDropShadowEnabled(self) -> bool: ...
        def isPlotAreaBackgroundVisible(self) -> bool: ...
        def isZoomed(self) -> bool: ...
        def legend(self) -> PySide2.QtCharts.QtCharts.QLegend: ...
        def locale(self) -> PySide2.QtCore.QLocale: ...
        def localizeNumbers(self) -> bool: ...
        def mapToPosition(self, value:PySide2.QtCore.QPointF, series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...) -> PySide2.QtCore.QPointF: ...
        def mapToValue(self, position:PySide2.QtCore.QPointF, series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...) -> PySide2.QtCore.QPointF: ...
        def margins(self) -> PySide2.QtCore.QMargins: ...
        def plotArea(self) -> PySide2.QtCore.QRectF: ...
        def plotAreaBackgroundBrush(self) -> PySide2.QtGui.QBrush: ...
        def plotAreaBackgroundPen(self) -> PySide2.QtGui.QPen: ...
        def removeAllSeries(self): ...
        def removeAxis(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis): ...
        def removeSeries(self, series:PySide2.QtCharts.QtCharts.QAbstractSeries): ...
        def scroll(self, dx:float, dy:float): ...
        def series(self) -> typing.List: ...
        def setAnimationDuration(self, msecs:int): ...
        def setAnimationEasingCurve(self, curve:PySide2.QtCore.QEasingCurve): ...
        def setAnimationOptions(self, options:PySide2.QtCharts.QtCharts.QChart.AnimationOptions): ...
        def setAxisX(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis, series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...): ...
        def setAxisY(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis, series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...): ...
        def setBackgroundBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setBackgroundPen(self, pen:PySide2.QtGui.QPen): ...
        def setBackgroundRoundness(self, diameter:float): ...
        def setBackgroundVisible(self, visible:bool=...): ...
        def setDropShadowEnabled(self, enabled:bool=...): ...
        def setLocale(self, locale:PySide2.QtCore.QLocale): ...
        def setLocalizeNumbers(self, localize:bool): ...
        def setMargins(self, margins:PySide2.QtCore.QMargins): ...
        def setPlotArea(self, rect:PySide2.QtCore.QRectF): ...
        def setPlotAreaBackgroundBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setPlotAreaBackgroundPen(self, pen:PySide2.QtGui.QPen): ...
        def setPlotAreaBackgroundVisible(self, visible:bool=...): ...
        def setTheme(self, theme:PySide2.QtCharts.QtCharts.QChart.ChartTheme): ...
        def setTitle(self, title:str): ...
        def setTitleBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setTitleFont(self, font:PySide2.QtGui.QFont): ...
        def theme(self) -> PySide2.QtCharts.QtCharts.QChart.ChartTheme: ...
        def title(self) -> str: ...
        def titleBrush(self) -> PySide2.QtGui.QBrush: ...
        def titleFont(self) -> PySide2.QtGui.QFont: ...
        def zoom(self, factor:float): ...
        @typing.overload
        def zoomIn(self): ...
        @typing.overload
        def zoomIn(self, rect:PySide2.QtCore.QRectF): ...
        def zoomOut(self): ...
        def zoomReset(self): ...

    class QChartView(PySide2.QtWidgets.QGraphicsView):
        NoRubberBand             : QtCharts.QChartView = ... # 0x0
        VerticalRubberBand       : QtCharts.QChartView = ... # 0x1
        HorizontalRubberBand     : QtCharts.QChartView = ... # 0x2
        RectangleRubberBand      : QtCharts.QChartView = ... # 0x3

        class RubberBand(object):
            NoRubberBand             : QtCharts.QChartView.RubberBand = ... # 0x0
            VerticalRubberBand       : QtCharts.QChartView.RubberBand = ... # 0x1
            HorizontalRubberBand     : QtCharts.QChartView.RubberBand = ... # 0x2
            RectangleRubberBand      : QtCharts.QChartView.RubberBand = ... # 0x3

        class RubberBands(object): ...

        @typing.overload
        def __init__(self, chart:PySide2.QtCharts.QtCharts.QChart, parent:typing.Optional[PySide2.QtWidgets.QWidget]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtWidgets.QWidget]=...): ...

        def chart(self) -> PySide2.QtCharts.QtCharts.QChart: ...
        def mouseMoveEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def mousePressEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def mouseReleaseEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def resizeEvent(self, event:PySide2.QtGui.QResizeEvent): ...
        def rubberBand(self) -> PySide2.QtCharts.QtCharts.QChartView.RubberBands: ...
        def setChart(self, chart:PySide2.QtCharts.QtCharts.QChart): ...
        def setRubberBand(self, rubberBands:PySide2.QtCharts.QtCharts.QChartView.RubberBands): ...

    class QDateTimeAxis(PySide2.QtCharts.QAbstractAxis):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def format(self) -> str: ...
        def max(self) -> PySide2.QtCore.QDateTime: ...
        def min(self) -> PySide2.QtCore.QDateTime: ...
        def setFormat(self, format:str): ...
        @typing.overload
        def setMax(self, max:PySide2.QtCore.QDateTime): ...
        @typing.overload
        def setMax(self, max:typing.Any): ...
        @typing.overload
        def setMin(self, min:PySide2.QtCore.QDateTime): ...
        @typing.overload
        def setMin(self, min:typing.Any): ...
        @typing.overload
        def setRange(self, min:PySide2.QtCore.QDateTime, max:PySide2.QtCore.QDateTime): ...
        @typing.overload
        def setRange(self, min:typing.Any, max:typing.Any): ...
        def setTickCount(self, count:int): ...
        def tickCount(self) -> int: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractAxis.AxisType: ...

    class QHBarModelMapper(PySide2.QtCharts.QBarModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def columnCount(self) -> int: ...
        def firstBarSetRow(self) -> int: ...
        def firstColumn(self) -> int: ...
        def lastBarSetRow(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QAbstractBarSeries: ...
        def setColumnCount(self, columnCount:int): ...
        def setFirstBarSetRow(self, firstBarSetRow:int): ...
        def setFirstColumn(self, firstColumn:int): ...
        def setLastBarSetRow(self, lastBarSetRow:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QAbstractBarSeries): ...

    class QHBoxPlotModelMapper(PySide2.QtCharts.QBoxPlotModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def columnCount(self) -> int: ...
        def firstBoxSetRow(self) -> int: ...
        def firstColumn(self) -> int: ...
        def lastBoxSetRow(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QBoxPlotSeries: ...
        def setColumnCount(self, rowCount:int): ...
        def setFirstBoxSetRow(self, firstBoxSetRow:int): ...
        def setFirstColumn(self, firstColumn:int): ...
        def setLastBoxSetRow(self, lastBoxSetRow:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QBoxPlotSeries): ...

    class QHCandlestickModelMapper(PySide2.QtCharts.QCandlestickModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def closeColumn(self) -> int: ...
        def firstSetRow(self) -> int: ...
        def highColumn(self) -> int: ...
        def lastSetRow(self) -> int: ...
        def lowColumn(self) -> int: ...
        def openColumn(self) -> int: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def setCloseColumn(self, closeColumn:int): ...
        def setFirstSetRow(self, firstSetRow:int): ...
        def setHighColumn(self, highColumn:int): ...
        def setLastSetRow(self, lastSetRow:int): ...
        def setLowColumn(self, lowColumn:int): ...
        def setOpenColumn(self, openColumn:int): ...
        def setTimestampColumn(self, timestampColumn:int): ...
        def timestampColumn(self) -> int: ...

    class QHPieModelMapper(PySide2.QtCharts.QPieModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def columnCount(self) -> int: ...
        def firstColumn(self) -> int: ...
        def labelsRow(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QPieSeries: ...
        def setColumnCount(self, columnCount:int): ...
        def setFirstColumn(self, firstColumn:int): ...
        def setLabelsRow(self, labelsRow:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QPieSeries): ...
        def setValuesRow(self, valuesRow:int): ...
        def valuesRow(self) -> int: ...

    class QHXYModelMapper(PySide2.QtCharts.QXYModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def columnCount(self) -> int: ...
        def firstColumn(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QXYSeries: ...
        def setColumnCount(self, columnCount:int): ...
        def setFirstColumn(self, firstColumn:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QXYSeries): ...
        def setXRow(self, xRow:int): ...
        def setYRow(self, yRow:int): ...
        def xRow(self) -> int: ...
        def yRow(self) -> int: ...

    class QHorizontalBarSeries(PySide2.QtCharts.QAbstractBarSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QHorizontalPercentBarSeries(PySide2.QtCharts.QAbstractBarSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QHorizontalStackedBarSeries(PySide2.QtCharts.QAbstractBarSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QLegend(PySide2.QtWidgets.QGraphicsWidget):
        MarkerShapeDefault       : QtCharts.QLegend = ... # 0x0
        MarkerShapeRectangle     : QtCharts.QLegend = ... # 0x1
        MarkerShapeCircle        : QtCharts.QLegend = ... # 0x2
        MarkerShapeFromSeries    : QtCharts.QLegend = ... # 0x3

        class MarkerShape(object):
            MarkerShapeDefault       : QtCharts.QLegend.MarkerShape = ... # 0x0
            MarkerShapeRectangle     : QtCharts.QLegend.MarkerShape = ... # 0x1
            MarkerShapeCircle        : QtCharts.QLegend.MarkerShape = ... # 0x2
            MarkerShapeFromSeries    : QtCharts.QLegend.MarkerShape = ... # 0x3
        def alignment(self) -> PySide2.QtCore.Qt.Alignment: ...
        def attachToChart(self): ...
        def borderColor(self) -> PySide2.QtGui.QColor: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def color(self) -> PySide2.QtGui.QColor: ...
        def detachFromChart(self): ...
        def font(self) -> PySide2.QtGui.QFont: ...
        def hideEvent(self, event:PySide2.QtGui.QHideEvent): ...
        def isAttachedToChart(self) -> bool: ...
        def isBackgroundVisible(self) -> bool: ...
        def labelBrush(self) -> PySide2.QtGui.QBrush: ...
        def labelColor(self) -> PySide2.QtGui.QColor: ...
        def markerShape(self) -> PySide2.QtCharts.QtCharts.QLegend.MarkerShape: ...
        def markers(self, series:typing.Optional[PySide2.QtCharts.QtCharts.QAbstractSeries]=...) -> typing.List: ...
        def paint(self, painter:PySide2.QtGui.QPainter, option:PySide2.QtWidgets.QStyleOptionGraphicsItem, widget:typing.Optional[PySide2.QtWidgets.QWidget]=...): ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def reverseMarkers(self) -> bool: ...
        def setAlignment(self, alignment:PySide2.QtCore.Qt.Alignment): ...
        def setBackgroundVisible(self, visible:bool=...): ...
        def setBorderColor(self, color:PySide2.QtGui.QColor): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setColor(self, color:PySide2.QtGui.QColor): ...
        def setFont(self, font:PySide2.QtGui.QFont): ...
        def setLabelBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setLabelColor(self, color:PySide2.QtGui.QColor): ...
        def setMarkerShape(self, shape:PySide2.QtCharts.QtCharts.QLegend.MarkerShape): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def setReverseMarkers(self, reverseMarkers:bool=...): ...
        def setShowToolTips(self, show:bool): ...
        def showEvent(self, event:PySide2.QtGui.QShowEvent): ...
        def showToolTips(self) -> bool: ...

    class QLegendMarker(PySide2.QtCore.QObject):
        LegendMarkerTypeArea     : QtCharts.QLegendMarker = ... # 0x0
        LegendMarkerTypeBar      : QtCharts.QLegendMarker = ... # 0x1
        LegendMarkerTypePie      : QtCharts.QLegendMarker = ... # 0x2
        LegendMarkerTypeXY       : QtCharts.QLegendMarker = ... # 0x3
        LegendMarkerTypeBoxPlot  : QtCharts.QLegendMarker = ... # 0x4
        LegendMarkerTypeCandlestick: QtCharts.QLegendMarker = ... # 0x5

        class LegendMarkerType(object):
            LegendMarkerTypeArea     : QtCharts.QLegendMarker.LegendMarkerType = ... # 0x0
            LegendMarkerTypeBar      : QtCharts.QLegendMarker.LegendMarkerType = ... # 0x1
            LegendMarkerTypePie      : QtCharts.QLegendMarker.LegendMarkerType = ... # 0x2
            LegendMarkerTypeXY       : QtCharts.QLegendMarker.LegendMarkerType = ... # 0x3
            LegendMarkerTypeBoxPlot  : QtCharts.QLegendMarker.LegendMarkerType = ... # 0x4
            LegendMarkerTypeCandlestick: QtCharts.QLegendMarker.LegendMarkerType = ... # 0x5
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def font(self) -> PySide2.QtGui.QFont: ...
        def isVisible(self) -> bool: ...
        def label(self) -> str: ...
        def labelBrush(self) -> PySide2.QtGui.QBrush: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries: ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setFont(self, font:PySide2.QtGui.QFont): ...
        def setLabel(self, label:str): ...
        def setLabelBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def setShape(self, shape:PySide2.QtCharts.QtCharts.QLegend.MarkerShape): ...
        def setVisible(self, visible:bool): ...
        def shape(self) -> PySide2.QtCharts.QtCharts.QLegend.MarkerShape: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QLegendMarker.LegendMarkerType: ...

    class QLineSeries(PySide2.QtCharts.QXYSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QLogValueAxis(PySide2.QtCharts.QAbstractAxis):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def base(self) -> float: ...
        def labelFormat(self) -> str: ...
        def max(self) -> float: ...
        def min(self) -> float: ...
        def minorTickCount(self) -> int: ...
        def setBase(self, base:float): ...
        def setLabelFormat(self, format:str): ...
        @typing.overload
        def setMax(self, max:typing.Any): ...
        @typing.overload
        def setMax(self, max:float): ...
        @typing.overload
        def setMin(self, min:typing.Any): ...
        @typing.overload
        def setMin(self, min:float): ...
        def setMinorTickCount(self, minorTickCount:int): ...
        @typing.overload
        def setRange(self, min:typing.Any, max:typing.Any): ...
        @typing.overload
        def setRange(self, min:float, max:float): ...
        def tickCount(self) -> int: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractAxis.AxisType: ...

    class QPercentBarSeries(PySide2.QtCharts.QAbstractBarSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QPieLegendMarker(PySide2.QtCharts.QLegendMarker):

        def __init__(self, series:PySide2.QtCharts.QtCharts.QPieSeries, slice:PySide2.QtCharts.QtCharts.QPieSlice, legend:PySide2.QtCharts.QtCharts.QLegend, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def series(self) -> PySide2.QtCharts.QtCharts.QPieSeries: ...
        def slice(self) -> PySide2.QtCharts.QtCharts.QPieSlice: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QLegendMarker.LegendMarkerType: ...

    class QPieModelMapper(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def count(self) -> int: ...
        def first(self) -> int: ...
        def labelsSection(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QPieSeries: ...
        def setCount(self, count:int): ...
        def setFirst(self, first:int): ...
        def setLabelsSection(self, labelsSection:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setOrientation(self, orientation:PySide2.QtCore.Qt.Orientation): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QPieSeries): ...
        def setValuesSection(self, valuesSection:int): ...
        def valuesSection(self) -> int: ...

    class QPieSeries(PySide2.QtCharts.QAbstractSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def __lshift__(self, slice:PySide2.QtCharts.QtCharts.QPieSlice) -> PySide2.QtCharts.QtCharts.QPieSeries: ...
        @typing.overload
        def append(self, label:str, value:float) -> PySide2.QtCharts.QtCharts.QPieSlice: ...
        @typing.overload
        def append(self, slice:PySide2.QtCharts.QtCharts.QPieSlice) -> bool: ...
        @typing.overload
        def append(self, slices:typing.Sequence) -> bool: ...
        def clear(self): ...
        def count(self) -> int: ...
        def holeSize(self) -> float: ...
        def horizontalPosition(self) -> float: ...
        def insert(self, index:int, slice:PySide2.QtCharts.QtCharts.QPieSlice) -> bool: ...
        def isEmpty(self) -> bool: ...
        def pieEndAngle(self) -> float: ...
        def pieSize(self) -> float: ...
        def pieStartAngle(self) -> float: ...
        def remove(self, slice:PySide2.QtCharts.QtCharts.QPieSlice) -> bool: ...
        def setHoleSize(self, holeSize:float): ...
        def setHorizontalPosition(self, relativePosition:float): ...
        def setLabelsPosition(self, position:PySide2.QtCharts.QtCharts.QPieSlice.LabelPosition): ...
        def setLabelsVisible(self, visible:bool=...): ...
        def setPieEndAngle(self, endAngle:float): ...
        def setPieSize(self, relativeSize:float): ...
        def setPieStartAngle(self, startAngle:float): ...
        def setVerticalPosition(self, relativePosition:float): ...
        def slices(self) -> typing.List: ...
        def sum(self) -> float: ...
        def take(self, slice:PySide2.QtCharts.QtCharts.QPieSlice) -> bool: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...
        def verticalPosition(self) -> float: ...

    class QPieSlice(PySide2.QtCore.QObject):
        LabelOutside             : QtCharts.QPieSlice = ... # 0x0
        LabelInsideHorizontal    : QtCharts.QPieSlice = ... # 0x1
        LabelInsideTangential    : QtCharts.QPieSlice = ... # 0x2
        LabelInsideNormal        : QtCharts.QPieSlice = ... # 0x3

        class LabelPosition(object):
            LabelOutside             : QtCharts.QPieSlice.LabelPosition = ... # 0x0
            LabelInsideHorizontal    : QtCharts.QPieSlice.LabelPosition = ... # 0x1
            LabelInsideTangential    : QtCharts.QPieSlice.LabelPosition = ... # 0x2
            LabelInsideNormal        : QtCharts.QPieSlice.LabelPosition = ... # 0x3

        @typing.overload
        def __init__(self, label:str, value:float, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def angleSpan(self) -> float: ...
        def borderColor(self) -> PySide2.QtGui.QColor: ...
        def borderWidth(self) -> int: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def color(self) -> PySide2.QtGui.QColor: ...
        def explodeDistanceFactor(self) -> float: ...
        def isExploded(self) -> bool: ...
        def isLabelVisible(self) -> bool: ...
        def label(self) -> str: ...
        def labelArmLengthFactor(self) -> float: ...
        def labelBrush(self) -> PySide2.QtGui.QBrush: ...
        def labelColor(self) -> PySide2.QtGui.QColor: ...
        def labelFont(self) -> PySide2.QtGui.QFont: ...
        def labelPosition(self) -> PySide2.QtCharts.QtCharts.QPieSlice.LabelPosition: ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def percentage(self) -> float: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QPieSeries: ...
        def setBorderColor(self, color:PySide2.QtGui.QColor): ...
        def setBorderWidth(self, width:int): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setColor(self, color:PySide2.QtGui.QColor): ...
        def setExplodeDistanceFactor(self, factor:float): ...
        def setExploded(self, exploded:bool=...): ...
        def setLabel(self, label:str): ...
        def setLabelArmLengthFactor(self, factor:float): ...
        def setLabelBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setLabelColor(self, color:PySide2.QtGui.QColor): ...
        def setLabelFont(self, font:PySide2.QtGui.QFont): ...
        def setLabelPosition(self, position:PySide2.QtCharts.QtCharts.QPieSlice.LabelPosition): ...
        def setLabelVisible(self, visible:bool=...): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def setValue(self, value:float): ...
        def startAngle(self) -> float: ...
        def value(self) -> float: ...

    class QPolarChart(PySide2.QtCharts.QChart):
        PolarOrientationRadial   : QtCharts.QPolarChart = ... # 0x1
        PolarOrientationAngular  : QtCharts.QPolarChart = ... # 0x2

        class PolarOrientation(object):
            PolarOrientationRadial   : QtCharts.QPolarChart.PolarOrientation = ... # 0x1
            PolarOrientationAngular  : QtCharts.QPolarChart.PolarOrientation = ... # 0x2

        class PolarOrientations(object): ...

        def __init__(self, parent:typing.Optional[PySide2.QtWidgets.QGraphicsItem]=..., wFlags:PySide2.QtCore.Qt.WindowFlags=...): ...

        @typing.overload
        def addAxis(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis, alignment:PySide2.QtCore.Qt.Alignment): ...
        @typing.overload
        def addAxis(self, axis:PySide2.QtCharts.QtCharts.QAbstractAxis, polarOrientation:PySide2.QtCharts.QtCharts.QPolarChart.PolarOrientation): ...
        @staticmethod
        def axisPolarOrientation(axis:PySide2.QtCharts.QtCharts.QAbstractAxis) -> PySide2.QtCharts.QtCharts.QPolarChart.PolarOrientation: ...

    class QScatterSeries(PySide2.QtCharts.QXYSeries):
        MarkerShapeCircle        : QtCharts.QScatterSeries = ... # 0x0
        MarkerShapeRectangle     : QtCharts.QScatterSeries = ... # 0x1

        class MarkerShape(object):
            MarkerShapeCircle        : QtCharts.QScatterSeries.MarkerShape = ... # 0x0
            MarkerShapeRectangle     : QtCharts.QScatterSeries.MarkerShape = ... # 0x1

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def borderColor(self) -> PySide2.QtGui.QColor: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def color(self) -> PySide2.QtGui.QColor: ...
        def markerShape(self) -> PySide2.QtCharts.QtCharts.QScatterSeries.MarkerShape: ...
        def markerSize(self) -> float: ...
        def setBorderColor(self, color:PySide2.QtGui.QColor): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setColor(self, color:PySide2.QtGui.QColor): ...
        def setMarkerShape(self, shape:PySide2.QtCharts.QtCharts.QScatterSeries.MarkerShape): ...
        def setMarkerSize(self, size:float): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QSplineSeries(PySide2.QtCharts.QLineSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QStackedBarSeries(PySide2.QtCharts.QAbstractBarSeries):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractSeries.SeriesType: ...

    class QVBarModelMapper(PySide2.QtCharts.QBarModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def firstBarSetColumn(self) -> int: ...
        def firstRow(self) -> int: ...
        def lastBarSetColumn(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def rowCount(self) -> int: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QAbstractBarSeries: ...
        def setFirstBarSetColumn(self, firstBarSetColumn:int): ...
        def setFirstRow(self, firstRow:int): ...
        def setLastBarSetColumn(self, lastBarSetColumn:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setRowCount(self, rowCount:int): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QAbstractBarSeries): ...

    class QVBoxPlotModelMapper(PySide2.QtCharts.QBoxPlotModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def firstBoxSetColumn(self) -> int: ...
        def firstRow(self) -> int: ...
        def lastBoxSetColumn(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def rowCount(self) -> int: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QBoxPlotSeries: ...
        def setFirstBoxSetColumn(self, firstBoxSetColumn:int): ...
        def setFirstRow(self, firstRow:int): ...
        def setLastBoxSetColumn(self, lastBoxSetColumn:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setRowCount(self, rowCount:int): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QBoxPlotSeries): ...

    class QVCandlestickModelMapper(PySide2.QtCharts.QCandlestickModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def closeRow(self) -> int: ...
        def firstSetColumn(self) -> int: ...
        def highRow(self) -> int: ...
        def lastSetColumn(self) -> int: ...
        def lowRow(self) -> int: ...
        def openRow(self) -> int: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def setCloseRow(self, closeRow:int): ...
        def setFirstSetColumn(self, firstSetColumn:int): ...
        def setHighRow(self, highRow:int): ...
        def setLastSetColumn(self, lastSetColumn:int): ...
        def setLowRow(self, lowRow:int): ...
        def setOpenRow(self, openRow:int): ...
        def setTimestampRow(self, timestampRow:int): ...
        def timestampRow(self) -> int: ...

    class QVPieModelMapper(PySide2.QtCharts.QPieModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def firstRow(self) -> int: ...
        def labelsColumn(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def rowCount(self) -> int: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QPieSeries: ...
        def setFirstRow(self, firstRow:int): ...
        def setLabelsColumn(self, labelsColumn:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setRowCount(self, rowCount:int): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QPieSeries): ...
        def setValuesColumn(self, valuesColumn:int): ...
        def valuesColumn(self) -> int: ...

    class QVXYModelMapper(PySide2.QtCharts.QXYModelMapper):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def firstRow(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def rowCount(self) -> int: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QXYSeries: ...
        def setFirstRow(self, firstRow:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setRowCount(self, rowCount:int): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QXYSeries): ...
        def setXColumn(self, xColumn:int): ...
        def setYColumn(self, yColumn:int): ...
        def xColumn(self) -> int: ...
        def yColumn(self) -> int: ...

    class QValueAxis(PySide2.QtCharts.QAbstractAxis):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def applyNiceNumbers(self): ...
        def labelFormat(self) -> str: ...
        def max(self) -> float: ...
        def min(self) -> float: ...
        def minorTickCount(self) -> int: ...
        def setLabelFormat(self, format:str): ...
        @typing.overload
        def setMax(self, max:typing.Any): ...
        @typing.overload
        def setMax(self, max:float): ...
        @typing.overload
        def setMin(self, min:typing.Any): ...
        @typing.overload
        def setMin(self, min:float): ...
        def setMinorTickCount(self, count:int): ...
        @typing.overload
        def setRange(self, min:typing.Any, max:typing.Any): ...
        @typing.overload
        def setRange(self, min:float, max:float): ...
        def setTickAnchor(self, anchor:float): ...
        def setTickCount(self, count:int): ...
        def setTickInterval(self, insterval:float): ...
        def tickAnchor(self) -> float: ...
        def tickCount(self) -> int: ...
        def tickInterval(self) -> float: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QAbstractAxis.AxisType: ...

    class QXYLegendMarker(PySide2.QtCharts.QLegendMarker):

        def __init__(self, series:PySide2.QtCharts.QtCharts.QXYSeries, legend:PySide2.QtCharts.QtCharts.QLegend, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def series(self) -> PySide2.QtCharts.QtCharts.QXYSeries: ...
        def type(self) -> PySide2.QtCharts.QtCharts.QLegendMarker.LegendMarkerType: ...

    class QXYModelMapper(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def count(self) -> int: ...
        def first(self) -> int: ...
        def model(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def orientation(self) -> PySide2.QtCore.Qt.Orientation: ...
        def series(self) -> PySide2.QtCharts.QtCharts.QXYSeries: ...
        def setCount(self, count:int): ...
        def setFirst(self, first:int): ...
        def setModel(self, model:PySide2.QtCore.QAbstractItemModel): ...
        def setOrientation(self, orientation:PySide2.QtCore.Qt.Orientation): ...
        def setSeries(self, series:PySide2.QtCharts.QtCharts.QXYSeries): ...
        def setXSection(self, xSection:int): ...
        def setYSection(self, ySection:int): ...
        def xSection(self) -> int: ...
        def ySection(self) -> int: ...

    class QXYSeries(PySide2.QtCharts.QAbstractSeries):
        @typing.overload
        def __lshift__(self, point:PySide2.QtCore.QPointF) -> PySide2.QtCharts.QtCharts.QXYSeries: ...
        @typing.overload
        def __lshift__(self, points:typing.Sequence) -> PySide2.QtCharts.QtCharts.QXYSeries: ...
        @typing.overload
        def append(self, point:PySide2.QtCore.QPointF): ...
        @typing.overload
        def append(self, points:typing.Sequence): ...
        @typing.overload
        def append(self, x:float, y:float): ...
        def at(self, index:int) -> PySide2.QtCore.QPointF: ...
        def brush(self) -> PySide2.QtGui.QBrush: ...
        def clear(self): ...
        def color(self) -> PySide2.QtGui.QColor: ...
        def count(self) -> int: ...
        def insert(self, index:int, point:PySide2.QtCore.QPointF): ...
        def pen(self) -> PySide2.QtGui.QPen: ...
        def pointLabelsClipping(self) -> bool: ...
        def pointLabelsColor(self) -> PySide2.QtGui.QColor: ...
        def pointLabelsFont(self) -> PySide2.QtGui.QFont: ...
        def pointLabelsFormat(self) -> str: ...
        def pointLabelsVisible(self) -> bool: ...
        def points(self) -> typing.List: ...
        def pointsVector(self) -> typing.List: ...
        def pointsVisible(self) -> bool: ...
        @typing.overload
        def remove(self, index:int): ...
        @typing.overload
        def remove(self, point:PySide2.QtCore.QPointF): ...
        @typing.overload
        def remove(self, x:float, y:float): ...
        def removePoints(self, index:int, count:int): ...
        @typing.overload
        def replace(self, index:int, newPoint:PySide2.QtCore.QPointF): ...
        @typing.overload
        def replace(self, index:int, newX:float, newY:float): ...
        @typing.overload
        def replace(self, oldPoint:PySide2.QtCore.QPointF, newPoint:PySide2.QtCore.QPointF): ...
        @typing.overload
        def replace(self, oldX:float, oldY:float, newX:float, newY:float): ...
        @typing.overload
        def replace(self, points:typing.Sequence): ...
        @typing.overload
        def replace(self, points:typing.List): ...
        def setBrush(self, brush:PySide2.QtGui.QBrush): ...
        def setColor(self, color:PySide2.QtGui.QColor): ...
        def setPen(self, pen:PySide2.QtGui.QPen): ...
        def setPointLabelsClipping(self, enabled:bool=...): ...
        def setPointLabelsColor(self, color:PySide2.QtGui.QColor): ...
        def setPointLabelsFont(self, font:PySide2.QtGui.QFont): ...
        def setPointLabelsFormat(self, format:str): ...
        def setPointLabelsVisible(self, visible:bool=...): ...
        def setPointsVisible(self, visible:bool=...): ...

# eof
