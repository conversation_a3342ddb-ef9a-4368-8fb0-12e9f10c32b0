import setuptools
from distutils.core import setup
from Cython.Build import cythonize
from distutils.extension import Extension
import os
import shutil


if __name__ == "__main__":
    py_file_list = []
    extension_list = []
    build_path = '{}/py/lsr/maya/rig'.format(os.path.dirname(
        os.path.dirname(os.path.abspath(__file__)))).replace('\\', '/')

    os.chdir(build_path)

    for root, dirs, files in os.walk(build_path):
        if root.endswith('face'):
            continue
        if root.endswith('core'):
            continue
        if 'userlib' in root:
            continue
        if root.endswith('build'):
            shutil.rmtree(root)

        # file_formats = ['.c', '.pyd', '.pyc']

        for f_obj in files:

            if f_obj.startswith('__'):
                continue
            if f_obj in ['marker.py', 'limb_root.py']:
                continue
            if os.path.splitext(f_obj)[-1] == '.py':
                py_file_list.append(os.path.join(root, f_obj).replace('\\', '/'))

    for f_obj in py_file_list:
        if '__' in f_obj:
            continue
        else:
            relative_path = f_obj.replace(build_path + '/', '')
            arg = relative_path.split('.py')[0].replace('/', '.')
            extension_list.append(Extension(str(arg), [relative_path]))

    setup(ext_modules=cythonize(extension_list, compiler_directives={'language_level': 2}))

    for f_obj in py_file_list:
        os.remove(f_obj)
        c_file = '{}.c'.format(os.path.splitext(f_obj)[0].replace('\\', '/'))
        if os.path.exists(c_file):
            os.remove(c_file)

    for root, dirs, files in os.walk(build_path):
        if root.endswith('\\build'):
            shutil.rmtree(root)
