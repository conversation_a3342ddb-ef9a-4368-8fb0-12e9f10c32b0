import argparse
import os
import sys
import subprocess
import shutil
import re
import json
# parser = argparse.ArgumentParser(description='Get environment information from a pipeline file')
# parser.add_argument('-p', '--pipeline', help='pipeline path', type=str, default="")

def parse_path(relative_path):
    #parse all environ variables.
    pattern = re.compile(r"\$\{(.+?)\}")
    keywords = set(pattern.findall(relative_path))
    for keyword in keywords:
        env_var = os.environ.get(keyword)
        if env_var is None:
            print("Failed to parse environment variable {}!".format(keyword))
        relative_path = relative_path.replace('${{{}}}'.format(keyword), env_var)
    
    return os.path.abspath(relative_path).replace('\\', '/')

def get_project_env(package_root):
    cwd = os.getcwd()
    try:        
        os.chdir(package_root)
        env_file_path = '{}/env/env.json'.format(package_root)
        if not os.path.isfile(env_file_path):
            print("{} does not contain the env.json.".format(env_file_path))
            return {}
        with open(env_file_path, 'r') as json_file:
            env_info = json.load(json_file)
            for key, values in env_info.items():
                if isinstance(values, list):#either list or string
                    idx = 0
                    for value in values:
                        values[idx] = parse_path(value)
                        idx += 1
                else:
                    env_info[key] = [parse_path(values)]
            return env_info
    except Exception as e:
        import traceback
        traceback.print_stack()
        pass    
    os.chdir(cwd)
    return {}

def get_lsr_env(packages_root):
    try:
        launch_env = {}
        pkgs = os.listdir('{}/packages'.format(packages_root))
        for pkg in pkgs:
            package_root = '{}/packages/{}'.format(packages_root, pkg)
            pkg_env = get_project_env(package_root)
            for key, values in pkg_env.items():
                new_env = launch_env.get(key,[])
                if isinstance(new_env, list):#either list or string
                    launch_env[key] = list(set(launch_env.get(key,[]) + values))
                else:
                    launch_env[key].append(new_env)
        return launch_env
    except Exception as e:
        print(e)
        import traceback
        traceback.print_stack()
        print("load env failed!\n")
        pass
    return {}

def get_path_from(reg_entry, reg_key):
    process = subprocess.Popen(['reg', 'query', reg_entry, "/v", reg_key],
                                stdout=subprocess.PIPE,
                                stderr=subprocess.STDOUT)
    stdout, _ = process.communicate()
    reg_info = str(stdout.decode("utf-8")).strip('\n').split('\n')
    if len(reg_info) == 4:
        reg_info = reg_info[-2].split('  ')
        reg_info = [i.strip('\r"\'') for i in reg_info if i]
        if len(reg_info)==3 and reg_info[0]==reg_key  and reg_info[1]=="REG_SZ":
            return reg_info[-1]
    return None

def get_maya_location(version=2020):
    location = get_path_from('HKLM\\SOFTWARE\\Autodesk\\Maya\\{}\\Setup\\InstallPath'.format(version), "MAYA_INSTALL_LOCATION")
    return location.strip('/\\')

if __name__ == "__main__":
    cwd = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    lsr_root = os.environ.get('LSR_ROOT', cwd)
    if 'MAYA_VERSION' not in os.environ:
        os.environ['MAYA_VERSION'] = '2020'
    maya_ver = os.environ.get('MAYA_VERSION')
    launch_env = get_lsr_env(lsr_root)
    #merge to system environ
    for key, values in launch_env.items():
        current_value = os.environ.get(key)
        if current_value:
            os.environ[key] =  os.pathsep.join(values)+os.pathsep+current_value
        else:
            os.environ[key] = os.pathsep.join(values)
    cmds = ['cmd'] if len(sys.argv) <= 1 else sys.argv[1:]
    # sys.argv[0] is python
    maya_location = get_maya_location(maya_ver)
    maya_path = '{}/bin/'.format(maya_location)
    path_var = os.environ.get('PATH')
    os.environ['PATH'] = "{};{}".format(maya_path, path_var) if path_var else maya_path
    os.system(" ".join(cmds))
