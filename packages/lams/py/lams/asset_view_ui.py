import os
import sys
import ctypes
import json
import uuid
from collections import OrderedDict

import asset_widgets as aw
import project_api
import asset_api
import p4_utils

from lams import lams_utils
from lams import version_utils as lvu

from lams.qt.Qt.QtCore import *
from lams.qt.Qt.QtGui import *
from lams.qt.Qt.QtWidgets import *

import path_utils
prj_dir = path_utils.get_prj_root_dir()
pkg_dir = path_utils.get_pkg_dir()

class TaskComponent(QFrame):
  def __init__(self,parent=None, task_list=[]):
    super(TaskComponent,self).__init__(parent)

    self.parent = parent
    
    #self.setSizePolicy(QSizePolicy(QSizePolicy.Minimum,QSizePolicy.Expanding))

    #MASTER LAYOUT
    self.m_layout = QVBoxLayout()
    self.m_layout.setSpacing(5)
    self.m_layout.setContentsMargins(2, 2, 2, 2)
    #
    self.setLayout(self.m_layout)

    #Task label
    asset_Label = QLabel('Task')
    #asset_Label_css ='font-size: 25px;'
    #asset_Label.setStyleSheet(asset_Label_css)
    self.m_layout.addWidget(asset_Label)

    #Task combo box
    self.task_ComboBox = QComboBox()
    self.task_ComboBox.addItems(task_list)
    self.m_layout.addWidget(self.task_ComboBox)

    #Component label
    component_label = QLabel('Component')
    self.m_layout.addWidget(component_label)

    #Component List
    self.component_ListWidget = QListWidget()
    self.m_layout.addWidget(self.component_ListWidget)

class VersionList(QListWidget):
  def __init__(self,parent=None):
    super(VersionList,self).__init__(parent)

    self.parent = parent

  def view_dir(self):
    #get context
    context = self.parent.context_toggle_Button.text()
    #get asset
    c_item = self.parent.parent.asset_tree.asset_TreeWidget.currentItem()
    asset = c_item.get_path()
    #get task
    task = self.parent.parent.task_component_Frame.task_ComboBox.currentText()
    #get component
    component = self.parent.parent.task_component_Frame.component_ListWidget.currentItem().text()
    #get version
    version = self.currentItem().text()

    ap_v_path = lvu.get_asset_version_dir(project=self.parent.parent.project,
                                          context=context,
                                          asset=asset,
                                          task=task,
                                          component=component,
                                          version=version)

    cmd = []
    if os.name == 'nt':
      cmd = ['explorer',ap_v_path]
    elif os.name == 'posix':
      cmd = ['open',ap_v_path]
    else:
      print(ap_v_path)
      return
    #
    lams_utils.launch_app(cmd=cmd,v=1)

  def contextMenuEvent(self, event):
    modifiers = QApplication.keyboardModifiers()

    item=self.currentItem()

    if item:
      self.ctx_menu = QMenu(self)

      view_dir_txt = 'Print Path'
      if os.name == 'nt':
        view_dir_txt = 'View in Explorer'
      elif os.name == 'posix':
        view_dir_txt = 'View in Finder'

      view_dir_Action = QAction(view_dir_txt,self.ctx_menu)
      view_dir_Action.triggered.connect(self.view_dir)
      self.ctx_menu.addAction(view_dir_Action)
      
      self.ctx_menu.exec_(QCursor.pos())
      self.ctx_menu.clear()
  

class VersionWidget(QFrame):
  def __init__(self,parent=None):
    super(VersionWidget,self).__init__(parent)

    self.parent = parent

    #MASTER LAYOUT
    self.m_layout = QVBoxLayout()
    self.m_layout.setSpacing(5)
    self.m_layout.setContentsMargins(2, 2, 2, 2)
    #
    self.setLayout(self.m_layout)

    #Task label
    asset_Label = QLabel('Version')
    #asset_Label_css ='font-size: 25px;'
    #asset_Label.setStyleSheet(asset_Label_css)
    self.m_layout.addWidget(asset_Label)

    #Context Toggle
    self.context_toggle_Button = QPushButton()
    self.context_toggle_Button.setText('local')
    self.context_toggle_Button.setStyleSheet("font-size: 15px;background-color : rgb(100, 50, 50)")
    self.context_toggle_Button.setSizePolicy(QSizePolicy(QSizePolicy.Expanding,QSizePolicy.Fixed))

    self.m_layout.addWidget(self.context_toggle_Button)

    #Version list
    self.version_ListWidget = VersionList(parent=self)

    self.m_layout.addWidget(self.version_ListWidget)

    #connect
    self.context_toggle_Button.clicked.connect(self.context_toggle)

  def context_toggle(self):
    text = self.context_toggle_Button.text()
    if text == 'publish':
      self.context_toggle_Button.setText('local')
      self.context_toggle_Button.setStyleSheet("font-size: 15px;background-color : rgb(100, 50, 50)")
    else:
      self.context_toggle_Button.setText('publish')
      self.context_toggle_Button.setStyleSheet("font-size: 15px;background-color : rgb(50, 100, 50)")

class AssetInfo(QFrame):
  def __init__(self,parent=None):
    super(AssetInfo,self).__init__(parent)

    self.parent = parent

    self.setSizePolicy(QSizePolicy(QSizePolicy.Expanding,QSizePolicy.Expanding))

    #MASTER LAYOUT
    self.m_layout = QVBoxLayout()
    self.m_layout.setSpacing(5)
    self.m_layout.setContentsMargins(2, 2, 2, 2)
    #
    self.setLayout(self.m_layout)

    self.scroll = QScrollArea()
    self.scroll.setWidgetResizable(1)
    self.content = QWidget()
    self.scroll.setWidget(self.content)
    self.content_layout = QVBoxLayout()
    self.content.setLayout(self.content_layout)
    self.m_layout.addWidget(self.scroll)
    
    #spacer
    spacer = QSpacerItem(10, 10, QSizePolicy.Expanding,QSizePolicy.Expanding)
    self.content_layout.addSpacerItem(spacer)

  def clear(self):
    #print('AssetInfo.clear()')
    #
    if self.content_layout.itemAt(0).widget():
      self.content_layout.itemAt(0).widget().deleteLater()

  def update_label_icon(self):
    print('update_label_icon')

  def update_info(self,info_json):
    #print('update_info:',os.path.isfile(info_json),info_json)
    self.clear()
    if not os.path.isfile(info_json):
      return
    #

    png_path = info_json.replace('.json','.png')
    pixmap = None
    if os.path.isfile(png_path):
      pixmap = QPixmap(png_path)
      pixmap = pixmap.scaledToHeight(100,Qt.SmoothTransformation)

    with open(info_json, 'r') as json_file:
      version_data = json.load(json_file, object_pairs_hook=OrderedDict)

    #print(version_data)
    
    #
    info_frame = QFrame()
    info_layout = QVBoxLayout()
    info_frame.setLayout(info_layout)

    if pixmap:
      thmb = QLabel()
      thmb.setPixmap(pixmap)
      info_layout.addWidget(thmb)

    for k,v in version_data.items():
      frame = aw.KeyLineEditFrame(self,key=k,val=v)
      frame.value_LineEdit.setReadOnly(1)
      info_layout.addWidget(frame)
    #
    self.content_layout.insertWidget(0,info_frame)

class AssetView(QMainWindow):
#class AssetView(base_class):
  def __init__(self,
              parent=None,
              project='***',
              asset_db=''):
    super(AssetView,self).__init__(parent)

    self.setAttribute(Qt.WA_DeleteOnClose)

    self.setWindowFlags(Qt.WindowStaysOnTopHint)

    self.setWindowTitle('Asset View - ' + project)

    self.project = project

    av_icon_path = os.path.join(pkg_dir,'resource/icons/default.png')
    self.setWindowIcon(QIcon(av_icon_path))

    self.prj_json = path_utils.get_prj_json(project=project)
    #
    self.asset_type_list = ['*']
    atl = project_api.get_asset_type_list(prj_json=self.prj_json)
    self.asset_type_list.extend(atl)
    #
    self.assets_db = os.path.join(prj_dir,self.project+'/cfg/assets_db')
    #
    self.source_asset_publish_root = project_api.get_source_asset_publish_root(project=self.project,
                                                                              prj_json=self.prj_json)

    self.resize(800,350)

    #MASTER LAYOUT
    self.m_layout = QVBoxLayout()
    self.m_layout.setSpacing(5)
    self.m_layout.setContentsMargins(5, 5, 5, 5)
    #
    self.m_frame = QFrame()
    self.m_frame.setLayout(self.m_layout)
    self.setCentralWidget(self.m_frame)

    #main splitter
    self.m_splitter = QSplitter(Qt.Horizontal)
    self.m_splitter.setHandleWidth(1)
    self.m_splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
    self.m_layout.addWidget(self.m_splitter)

    #asset column frame and layout
    self.asset_column_Layout = QVBoxLayout()
    self.asset_column_Layout.setSpacing(0)
    self.asset_column_Layout.setContentsMargins(1,1,1,1)
    self.asset_column_frame = QFrame()
    self.asset_column_frame.setLayout(self.asset_column_Layout)

    #asset label
    asset_Label = QLabel('Asset')
    #asset_Label_css ='font-size: 15px;'
    #asset_Label.setStyleSheet(asset_Label_css)
    self.asset_column_Layout.addWidget(asset_Label)

    #asset tree widget
    self.asset_tree = aw.AssetTreeFrame(parent=self,
                                        project=self.project,
                                        assets_db=self.assets_db,
                                        asset_type_list=self.asset_type_list,
                                        edit_toggle=False)
    self.asset_column_Layout.addWidget(self.asset_tree)

    #add asset column to main horizontal splitter
    self.m_splitter.addWidget(self.asset_column_frame)

    #Task Component Widget
    task_list = project_api.get_task_list(prj_json=self.prj_json)
    
    self.task_component_Frame = TaskComponent(task_list=task_list)

    self.m_splitter.addWidget(self.task_component_Frame)

    #Version Widget
    self.version_widget_Frame = VersionWidget(parent=self)

    self.m_splitter.addWidget(self.version_widget_Frame)

    #info column layout
    self.info_column_Layout = QVBoxLayout()
    self.info_column_Layout.setSpacing(1)
    self.info_column_Layout.setContentsMargins(1,1,1,1)
    self.info_column_frame = QFrame()
    self.info_column_frame.setLayout(self.info_column_Layout)

    #info label and icon
    self.info_label_icon_Layout = QHBoxLayout()
    self.info_label_icon_Layout.setSpacing(1)
    self.info_label_icon_Layout.setContentsMargins(1,1,1,1)
    self.info_label_icon_frame = QFrame()
    self.info_label_icon_frame.setLayout(self.info_label_icon_Layout)

    info_Label = QLabel('Info')
    #info_Label_css ='font-size: 25px;'
    #info_Label.setStyleSheet(info_Label_css)
    self.info_label_icon_Layout.addWidget(info_Label)

    self.info_icon_Label = QLabel()
    #info_Label_css ='font-size: 25px;'
    #info_Label.setStyleSheet(info_Label_css)
    self.info_label_icon_Layout.addWidget(self.info_icon_Label)

    spacer = QSpacerItem(10, 10, QSizePolicy.Expanding,QSizePolicy.Fixed)
    self.info_label_icon_Layout.addSpacerItem(spacer)

    self.info_column_Layout.addWidget(self.info_label_icon_frame)

    #info list
    self.info_Widget = AssetInfo(parent=self)
    self.info_column_Layout.addWidget(self.info_Widget)

    #add asset column to main grid
    self.m_splitter.addWidget(self.info_column_frame)
    self.m_splitter.setSizes([1000,1,1,1000])

    #p4 status label
    #self.p4_status_label = QLabel('P4 Status:')
    #self.m_layout.addWidget(self.p4_status_label)

    ######

    #connect widgets
    self.asset_tree.asset_TreeWidget.itemClicked.connect(self.asset_tree_select)
    self.asset_tree.asset_search_ListWidget.itemClicked.connect(self.asset_tree_select)
    self.asset_tree.asset_TreeWidget.cleared.connect(self.asset_tree_select)
    self.task_component_Frame.task_ComboBox.currentIndexChanged.connect(self.asset_tree_select)
    self.task_component_Frame.component_ListWidget.itemClicked.connect(self.component_select)
    self.version_widget_Frame.context_toggle_Button.clicked.connect(self.component_select)
    self.version_widget_Frame.version_ListWidget.itemClicked.connect(self.version_select)

  def asset_tree_select(self):
    #print('asset_tree_callback: fill component list')
    #get asset
    c_item = self.asset_tree.asset_TreeWidget.currentItem()

    #clear everything
    self.task_component_Frame.component_ListWidget.clear()
    self.version_widget_Frame.version_ListWidget.clear()
    self.info_Widget.clear()
    #custom clear info frame CRUD style

    #return if nothing selected
    if not c_item:
      return

    #get task
    c_task = self.task_component_Frame.task_ComboBox.currentText()

    #get component list from selection 
    task_component_list = c_item._data['component'][c_task]
    #udpate component list
    self.task_component_Frame.component_ListWidget.addItems(task_component_list)

  def task_combo_update(self): #Might Not Need This One.
    print('task_combo_update')

  def component_select(self):
    #print('component_callback: update version list, query local/pub state')
    #clear versions
    self.version_widget_Frame.version_ListWidget.clear()
    self.info_Widget.clear()

    #get asset
    c_item = self.asset_tree.asset_TreeWidget.currentItem()
    if not c_item:
      return
    #get local/pub toggle
    context = self.version_widget_Frame.context_toggle_Button.text()

    prj_dir_root = prj_dir
    if context == 'publish':
      prj_dir_root = self.source_asset_publish_root 
    
    #print('prj_dir_root',prj_dir_root)

    #get source asset path
    source_asset_path = os.path.join(prj_dir_root,
                                    self.project,
                                    'source_assets',
                                    context,
                                    c_item.get_path())
    
    #get task
    c_task = self.task_component_Frame.task_ComboBox.currentText()
    #get component
    if not self.task_component_Frame.component_ListWidget.currentItem():
      #skip if no component selected
      return
    c_component = self.task_component_Frame.component_ListWidget.currentItem().text()

    data = c_item._data
    data['project'] = self.project
    version_list = asset_api.get_versions(data=data,
                                          source_asset_path=source_asset_path,
                                          task=c_task,
                                          component=c_component)

    self.version_widget_Frame.version_ListWidget.addItems(version_list)

  def version_select(self):
    #print('version_callback: update Info Frame')
    #get asset
    c_item = self.asset_tree.asset_TreeWidget.currentItem()
    if not c_item:
      return
    #get local/pub toggle
    context = self.version_widget_Frame.context_toggle_Button.text()
    #get task
    c_task = self.task_component_Frame.task_ComboBox.currentText()
    #get component
    c_component = self.task_component_Frame.component_ListWidget.currentItem().text()
    #get version
    c_version = self.version_widget_Frame.version_ListWidget.currentItem().text()

    #local/pub version root
    prj_dir_root = prj_dir
    if context == 'publish':
      prj_dir_root = self.source_asset_publish_root 

    #local version dir
    version_path_dir = os.path.join(prj_dir_root,
                                    self.project,
                                    'source_assets',
                                    context,
                                    c_item.get_path(),
                                    c_task,
                                    c_component,
                                    c_version)

    #p4
    pub_mode = project_api.get_source_asset_publish_mode(project=self.project)
    if (context == 'publish') and (pub_mode == 'p4'):
      dict_list = p4_utils.sync(project=self.project,
                              path=version_path_dir,
                              v=0)

    #get source asset path
    version_file_name_json = '_'.join([c_item._data['name'],c_task,c_component])+'.json'
    #
    version_path_json = os.path.join(version_path_dir,
                                    version_file_name_json)

    #print(os.path.isfile(version_path_json),version_path_json)
    #print('version_path_json',version_path_json)

    self.info_Widget.update_info(version_path_json)

def window(project):
  import qdarkstyle

  myappid = 'tencent.lams.asset_view_ui.0'
  if os.name == 'nt':
    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)

  os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
    
  app = QApplication(sys.argv)
  app.setStyleSheet(qdarkstyle.load_stylesheet())
  app.setAttribute(Qt.AA_EnableHighDpiScaling, True) #enable highdpi scaling
  app.setAttribute(Qt.AA_UseHighDpiPixmaps, True) #use highdpi icons

  av_icon_path = os.path.join(pkg_dir,'resource/icons/assetview.png')
  app.setWindowIcon(QIcon(av_icon_path))

  av_ui = AssetView(project = project)
  av_ui.show()

  sys.exit(app.exec_())

if __name__ == '__main__':
  window(sys.argv[1])