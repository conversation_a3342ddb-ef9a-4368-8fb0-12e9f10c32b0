import numpy as np

reveal_type(np.Inf)  # E: float
reveal_type(np.Infinity)  # E: float
reveal_type(np.NAN)  # E: float
reveal_type(np.NINF)  # E: float
reveal_type(np.NZERO)  # E: float
reveal_type(np.NaN)  # E: float
reveal_type(np.PINF)  # E: float
reveal_type(np.PZERO)  # E: float
reveal_type(np.e)  # E: float
reveal_type(np.euler_gamma)  # E: float
reveal_type(np.inf)  # E: float
reveal_type(np.infty)  # E: float
reveal_type(np.nan)  # E: float
reveal_type(np.pi)  # E: float

reveal_type(np.ALLOW_THREADS)  # E: int
reveal_type(np.BUFSIZE)  # E: int
reveal_type(np.CLIP)  # E: int
reveal_type(np.ERR_CALL)  # E: int
reveal_type(np.ERR_DEFAULT)  # E: int
reveal_type(np.ERR_IGNORE)  # E: int
reveal_type(np.ERR_LOG)  # E: int
reveal_type(np.ERR_PRINT)  # E: int
reveal_type(np.ERR_RAISE)  # E: int
reveal_type(np.ERR_WARN)  # E: int
reveal_type(np.FLOATING_POINT_SUPPORT)  # E: int
reveal_type(np.FPE_DIVIDEBYZERO)  # E: int
reveal_type(np.FPE_INVALID)  # E: int
reveal_type(np.FPE_OVERFLOW)  # E: int
reveal_type(np.FPE_UNDERFLOW)  # E: int
reveal_type(np.MAXDIMS)  # E: int
reveal_type(np.MAY_SHARE_BOUNDS)  # E: int
reveal_type(np.MAY_SHARE_EXACT)  # E: int
reveal_type(np.RAISE)  # E: int
reveal_type(np.SHIFT_DIVIDEBYZERO)  # E: int
reveal_type(np.SHIFT_INVALID)  # E: int
reveal_type(np.SHIFT_OVERFLOW)  # E: int
reveal_type(np.SHIFT_UNDERFLOW)  # E: int
reveal_type(np.UFUNC_BUFSIZE_DEFAULT)  # E: int
reveal_type(np.WRAP)  # E: int
reveal_type(np.tracemalloc_domain)  # E: int

reveal_type(np.little_endian)  # E: bool
reveal_type(np.True_)  # E: numpy.bool_
reveal_type(np.False_)  # E: numpy.bool_

reveal_type(np.UFUNC_PYVALS_NAME)  # E: str

reveal_type(np.sctypeDict)  # E: dict
reveal_type(np.sctypes)  # E: TypedDict
