"""
MotionBuilder scene utility actions
"""

import pyfbsdk as fb

import lsr.protostar.core.parameter as pa
import lsr.protostar.core.exception as exp
from lsr.mobu.rig.file_operator import import_file, save_file, open_file, new_file
from lsr.mobu.base_actions import BaseSceneAction


class NewMobuScene(BaseSceneAction):
    """
    Creates a new MotionBuilder scene.
    """

    def run(self):
        """Executes this action."""
        new_file()
        self.info('create a new scene')


class OpenMobuScene(BaseSceneAction):
    """
    Open the current MotionBuilder scene.
    """

    @pa.file_param(ext=('fbx', 'FBX'))
    def file_path(self):
        """The file path to save the current scene to."""

    def run(self):
        """Executes this action."""
        path = self.file_path.value
        if not path:
            raise exp.ActionError('File path is empty.')

        open_file(path)
        self.info('open scene to {}'.format(path))


class SaveMobuScene(BaseSceneAction):
    """
    Save exist MotionBuilder Scene.
    """

    @pa.file_param(ext=('fbx', 'FBX'))
    def file_path(self):
        """The file path need to open."""

    def run(self):
        """Executes this action."""
        path = self.file_path.value
        if not path:
            raise exp.ActionError('File path is empty.')

        save_file(path)
        self.info('save scene {}'.format(path))


class ImportMobuScene(BaseSceneAction):
    """
    Import MotionBuilder Scene.
    """

    @pa.file_param(ext=('fbx', 'FBX'))
    def file_path(self):
        """The file path need to open."""

    def run(self):
        """Executes this action."""
        path = self.file_path.value
        if not path:
            raise exp.ActionError('File path is empty.')

        import_file(path)
        self.info('Import scene {}'.format(path))
