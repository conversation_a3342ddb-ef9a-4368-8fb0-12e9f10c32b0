"""
Actor Limb Action
"""

import os
from collections import OrderedDict

import pyfbsdk as fb

import lsr.protostar.core.parameter as pa
import lsr.protostar.core.exception as exp

import lsr.mobu.rig.constants as const

from lsr.mobu.nodezoo.node import Node
from lsr.mobu.nodezoo.node import ParentConstraint
from lsr.mobu.nodezoo.node import RelationConstraint

from lsr.mobu.base_actions import DataExchangeAction
from lsr.mobu.base_actions import RigAction
from lsr.mobu.utils.FindObjects import get_scene_root_bones, find_animation_node, ls, clear_selection
from lsr.mobu.rig.file_operator import import_file_with_namespace, new_file


class Actor_Limb(RigAction):
    """
    Actor Limb Action
    """

    @pa.str_param(default='Body')
    def body_namespace(self):
        """The body namespace."""

    @pa.file_param(ext=['fbx', 'FBX'])
    def body_file_path(self):
        """The body file path."""

    @pa.str_param(default='Gun')
    def gun_namespace(self):
        """The gun namespace."""

    @pa.file_param(ext=['fbx', 'FBX'])
    def gun_file_path(self):
        """The gun file path."""

    @pa.str_param(default='Prop01')
    def prop_01_namespace(self):
        """The prop 01 namespace."""
    @pa.file_param(ext=['fbx', 'FBX'])
    def prop_01_file_path(self):
        """The prop 01 file path."""

    @pa.str_param(default='Prop02')
    def prop_02_namespace(self):
        """The prop 02 namespace."""

    @pa.file_param(ext=['fbx', 'FBX'])
    def prop_02_file_path(self):
        """The prop 02 file path."""

    @pa.str_param(default='Prop03')
    def prop_03_namespace(self):
        """The prop 03 namespace."""

    @pa.file_param(ext=['fbx', 'FBX'])
    def prop_03_file_path(self):
        """The prop 03 file path."""

    @pa.list_param(item_type='str', default=[const.RightWrist_ToGrip,
                                             const.LeftWrist_ToGrip,
                                             const.RightWrist_ToForegrip,
                                             const.LeftWrist_ToForegrip])
    def ik_names_list(self):
        """The ik names."""

    def _pre_check_nodes(self):
        if not os.path.exists(self.body_file_path.value):
            raise exp.ParameterError('body_file_path is exist.')

        if not os.path.exists(self.gun_file_path.value):
            raise exp.ParameterError('gun_file_path is exist.')

        prop_path = OrderedDict()

        if self.prop_01_file_path.value and os.path.exists(self.prop_01_file_path.value):
            prop_path[self.prop_01_namespace.value] = self.prop_01_file_path.value

        if self.prop_02_file_path.value and os.path.exists(self.prop_02_file_path.value):
            prop_path[self.prop_02_namespace.value] = self.prop_02_file_path.value

        if self.prop_03_file_path.value and os.path.exists(self.prop_03_file_path.value):
            prop_path[self.prop_03_namespace.value] = self.prop_03_file_path.value

        self.debug('body_file_path: {}'.format(self.body_file_path.value))
        self.debug('gun_file_path: {}'.format(self.gun_file_path.value))

        self.prop_path = prop_path

        return True

    def _post_check_nodes(self):
        body_rig_name = '{}:{}'.format(self.body_namespace.value, const.RIG_ROOT)
        if not Node.object_exist(body_rig_name):
            raise exp.ActionError('body RIG Node is not exist.')

        gun_rig_name = '{}:{}'.format(self.gun_namespace.value, const.GUN_ROOT)
        if not Node.object_exist(gun_rig_name):
            raise exp.ActionError('GUN Node is not exist.')

        self.body_rig = Node(body_rig_name)
        self.gun_rig = Node(gun_rig_name)

        self.prop_rigs = []
        
        for prop_path in self.prop_path:
            prop_rig_name = '{}:{}'.format(prop_path, const.RIG_ROOT)

            if not Node.object_exist(prop_rig_name):
                raise exp.ActionError('prop: {}\'s RIG Node is not exist.'.format(prop_path))

            self.prop_rigs.append(Node(prop_rig_name))

        return True

    def run(self):
        """Executes this action."""

        self.ik_names = self.ik_names_list.value
        self.reach_suffix = 'Reach_Master'

        self._pre_check_nodes()

        pre_check = new_file()
        if not pre_check:
            raise exp.ActionError('pre check nodes failed.')

        import_file_with_namespace(self.body_file_path.value, self.body_namespace.value)
        import_file_with_namespace(self.gun_file_path.value, self.gun_namespace.value)

        for prop_path in self.prop_path:
            import_file_with_namespace(self.prop_path[prop_path], prop_path)

        post_check = self._post_check_nodes()
        if not post_check:
            raise exp.ActionError('post check nodes failed.')

        self.info('import file success.')

        for prop_rig in self.prop_rigs:
            self.warn('connect gun to prop rig: {}'.format(prop_rig.long_name))
            self.connect_gun(prop_rig)

        self.edit_aux_refs()
        self.edit_gun_multi_constraint()

    def edit_gun_multi_constraint(self):
        """
        Edit gun multi constraint

        Returns:
            None
        """
        constraint = self.gun_rig.get_tag(const.GUN_MULTI_CONSTRAINT)
        left_wrist_eff = self.body_rig.get_tag(const.LEFT_WRIST_EFF)
        right_wrist_eff = self.body_rig.get_tag(const.RIGHT_WRIST_EFF)

        constraint.add_parent(right_wrist_eff)
        constraint.add_parent(left_wrist_eff)

    def connect_gun(self, rig_node):
        """
        Connect gun to prop rig

        Args:
            rig_node (Node): prop rig node

        Returns:
            list: ctrl_plc_constraints
        """
        if not rig_node:
            return

        name_space = rig_node.namespace
        prop_ctfd = rig_node.get_tag(const.CONSTRAINT_FOLDER)
        prop_limb_root = rig_node.get_tag(const.LIMB_ROOT)
        gun_ctrl = self.gun_rig.get_tag(const.GUN_CTRL)
        gun_plc = gun_ctrl.parent

        weapon_const = ParentConstraint.create(gun_ctrl,
                                               prop_limb_root,
                                               name='Parent_{}'.format(prop_limb_root.name))
        weapon_const.namespace = name_space

        if prop_ctfd:
            prop_ctfd.add_constraint(weapon_const)

        ik_nodes = []
        for ik_name in self.ik_names:
            ik_nodes.append(rig_node.get_tag(ik_name))

        ctrl_nodes = []
        for ik_name in self.ik_names:
            ctrl_nodes.append(self.gun_rig.get_tag(ik_name))

        ctrl_plc_constraints = []
        for ik_node, ctrl_node in zip(ik_nodes, ctrl_nodes):
            if not ik_node or not ctrl_node:
                continue
            ctrl_plc = ctrl_node.parent
            constraint_node = ParentConstraint.create(ik_node,
                                                      ctrl_plc,
                                                      name='Parent_{}'.format(ctrl_plc.name))

            constraint_node.namespace = name_space

            if prop_ctfd:
                prop_ctfd.add_constraint(constraint_node)
                ctrl_plc_constraints.append(constraint_node)

        self.edit_prop_relation_constraint(rig_node.get_tag(const.RELATION_CONSTRAINT), ctrl_plc_constraints)

        return ctrl_plc_constraints

    def edit_prop_relation_constraint(self, relation_const, constraints):
        """
        Edit prop relation constraint

        Args:
            relation_const (RelationConstraint): relation constraint
            constraints (list[ParentConstraint]): ctrl_plc_constraints

        Returns:
            None
        """

        mul_box = relation_const.add_function_box('Number', 'Multiply (a x b)')
        relation_const.set_box_position(mul_box, -400, 0)
        mul_box_a = find_animation_node(mul_box.AnimationNodeInGet(), 'a')
        mul_box_b = find_animation_node(mul_box.AnimationNodeInGet(), 'b')
        mul_box_b.WriteData([100.0])

        mul_box_out = find_animation_node(mul_box.AnimationNodeOutGet(), 'Result')

        ref_box = relation_const.find_box_by_name(const.RIG_ROOT)
        if ref_box:
            active_out = find_animation_node(ref_box.AnimationNodeOutGet(), 'Is_Active')
            fb.FBConnect(active_out, mul_box_a)

        for i, constraint in enumerate(constraints):
            constraint.set_weight_animatable(True)
            box = relation_const.set_destination(constraint)
            relation_const.set_box_position(box, 100, 100 * i)
            weight_in = find_animation_node(box.AnimationNodeInGet(), 'Weight')
            fb.FBConnect(mul_box_out, weight_in)

        self.info('edit prop: {} relation constraint success.'.format(relation_const.namespace))

    def create_aux_constraint(self, grip_ctrl, fore_ctrl, effector):
        """
        Create aux constraint
        Args:
            grip_ctrl ():
            effector ():
            fore_ctrl ():

        Returns:

        """
        aux_constraint = ParentConstraint.create(grip_ctrl, effector, name='Parent_{}'.format(effector.name))
        aux_constraint.add_reference(1, fore_ctrl)
        aux_constraint.set_weight_animatable(True)

        folder = self.gun_rig.get_tag(const.CONSTRAINT_FOLDER)
        if folder:
            folder.add_constraint(aux_constraint)

        return aux_constraint

    def connect_animation_nodes(self, constraint_box, gun_ctrl_box, grip_ctrl, grip_idx):
        """
        Connect animation nodes
        Args:
            constraint_box ():
            gun_ctrl_box ():
            grip_ctrl ():
            grip_idx ():

        Returns:

        """
        constraint_in = find_animation_node(constraint_box.AnimationNodeInGet(),
                                            '{}.Weight'.format(grip_ctrl.long_name))
        grip_out = find_animation_node(gun_ctrl_box.AnimationNodeOutGet(),
                                       '{}_{}'.format(self.ik_names[grip_idx], self.reach_suffix))

        fb.FBConnect(grip_out, constraint_in)

    def connect_ik_reach(self, aux_box, gun_ctrl_box, grip_idx, constraint):
        """
        Connect ik reach
        """
        grip_attr_01 = find_animation_node(
            gun_ctrl_box.AnimationNodeOutGet(),
            '{}_{}'.format(self.ik_names[grip_idx[0]], self.reach_suffix))

        grip_attr_02 = find_animation_node(
            gun_ctrl_box.AnimationNodeOutGet(),
            '{}_{}'.format(self.ik_names[grip_idx[1]], self.reach_suffix))

        # reach_out = find_animation_node(gun_ctrl_box.AnimationNodeOutGet(),
        #                                 '{}_{}'.format(grip_ctrl.name, self.reach_suffix))

        add_box = constraint.add_function_box('Number', 'Add (a + b)')
        constraint.set_box_position(add_box, -300, 0)
        add_box_a = find_animation_node(add_box.AnimationNodeInGet(), 'a')
        add_box_b = find_animation_node(add_box.AnimationNodeInGet(), 'b')
        add_box_out = find_animation_node(add_box.AnimationNodeOutGet(), 'Result')

        fb.FBConnect(grip_attr_01, add_box_a)
        fb.FBConnect(grip_attr_02, add_box_b)

        ik_t_attr = find_animation_node(aux_box.AnimationNodeInGet(), 'IK Reach Translation')
        ik_r_attr = find_animation_node(aux_box.AnimationNodeInGet(), 'IK Reach Rotation')

        fb.FBConnect(add_box_out, ik_t_attr)
        fb.FBConnect(add_box_out, ik_r_attr)

    def edit_aux_refs(self):
        """ Edit aux refs. """
        aux_names = [const.RIGHT_WRIST_AUX, const.LEFT_WRIST_AUX]
        ik_attrs = ['IK Reach Translation', 'IK Reach Rotation']

        # effector_names = [const.RIGHT_WRIST_EFF, const.LEFT_WRIST_EFF]
        grip_indices = [(0, 2), (1, 3)]
        box_positions = [600, 750]

        for aux_name, grip_idx, box_pos in zip(aux_names, grip_indices, box_positions):
            grip_ctrl = self.gun_rig.get_tag(self.ik_names[grip_idx[0]])
            fore_ctrl = self.gun_rig.get_tag(self.ik_names[grip_idx[1]])
            aux_node = self.body_rig.get_tag(aux_name)
            aux_constraint = self.create_aux_constraint(grip_ctrl, fore_ctrl, aux_node)

            re_const = self.gun_rig.get_tag(const.RELATION_CONSTRAINT)
            gun_ctrl = self.gun_rig.get_tag(const.GUN_CTRL)

            if re_const:
                constraint_box = re_const.set_destination(aux_constraint)
                re_const.set_box_position(constraint_box, -300, box_pos)
                gun_ctrl_box = re_const.find_box_by_name(gun_ctrl.name)

                self.connect_animation_nodes(constraint_box, gun_ctrl_box, grip_ctrl, grip_idx[0])
                self.connect_animation_nodes(constraint_box, gun_ctrl_box, fore_ctrl, grip_idx[1])

                aux_node.attr('IK Reach Translation').animatable = True
                aux_node.attr('IK Reach Rotation').animatable = True
                aux_box = re_const.set_destination(aux_node)
                re_const.set_box_position(aux_box, 0, box_pos + 200)
                self.connect_ik_reach(aux_box, gun_ctrl_box, grip_idx, re_const)

    def end(self):
        """End this action."""
        self.body_rig.get_tag(const.LEFT_WRIST_AUX).visibility.value = False
        self.body_rig.get_tag(const.RIGHT_WRIST_AUX).visibility.value = False
        cha_node = self.body_rig.get_tag('character')
        gun_ext = self.gun_rig.get_tag(const.EXTENSION)
        gun_ext.update_stance_pose()
        gun_ext.attached_character(cha_node)
        gun_ext.update_stance_pose()
        for prop_rig in self.prop_rigs:
            prop_ext = prop_rig.get_tag(const.EXTENSION)
            prop_ext.update_stance_pose()
            prop_ext.attached_character(cha_node)
            prop_ext.update_stance_pose()


class Part_File_Limb(DataExchangeAction):
    """
    Actor Limb Action
    """

    @pa.file_param(ext=['fbx', 'FBX'])
    def file_path(self):
        """The skin file path."""

    def run(self):
        pass


class Biped_Body_Limb(Part_File_Limb):
    """
    Biped Body Limb Action
    """

    def run(self):
        pass


class Prop(Part_File_Limb):
    """
    Prop Action
    """

    def run(self):
        pass

