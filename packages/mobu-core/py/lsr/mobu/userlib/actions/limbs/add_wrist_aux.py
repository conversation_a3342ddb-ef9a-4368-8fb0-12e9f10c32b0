
"""
Add Wrist Aux Pivots to a HumanIK character
"""

import pyfbsdk as fb

import lsr.protostar.core.parameter as pa
import lsr.mobu.rig.constants as const

from lsr.mobu.nodezoo.node import Node

from lsr.mobu.base_actions import RigAction
from lsr.mobu.nodezoo.node import Character


class WristPivots(RigAction):
    """
    Create humanIK Wrist Pivots
    """

    _UI_ICON = 'wrist_pivots'

    @pa.message_param()
    def character(self):
        """The character node."""

    def run(self):
        """Executes this action."""
        character = None

        if self.character.value:
            character = self.character.value

        if not character:
            character = Character(fb.FBApplication().CurrentCharacter)

        if not character:
            self.error('No character has been defined')
            raise ValueError('No character has been defined')

        if not character.has_control_rig:
            self.error('No Control rig has been defined')
            raise ValueError('No Control rig has been defined')

        # Create Wrist Pivots
        wrist_ik_models = character.add_wrist_aux_pivot()
        self.lf_wrist_pivot = wrist_ik_models[0]
        self.rt_wrist_pivot = wrist_ik_models[1]
        self.lf_wrist_end_effector = wrist_ik_models[2]
        self.rt_wrist_end_effector = wrist_ik_models[3]

        self.info("Added {} wrist pivots".format(character.name))

    def end(self):
        """
        End the action

        Returns:
            None
        """
        super(WristPivots, self).end()
        rig_node = Node(const.RIG_ROOT)
        rig_node.add_tag(const.LEFT_WRIST_AUX, self.lf_wrist_pivot)
        rig_node.add_tag(const.RIGHT_WRIST_AUX, self.rt_wrist_pivot)
        rig_node.add_tag(const.LEFT_WRIST_EFF, self.lf_wrist_end_effector)
        rig_node.add_tag(const.RIGHT_WRIST_EFF, self.rt_wrist_end_effector)
