from packages.project import Project


class GN_GameProject(Project):
    """docstring for C_GameProject"""
    def __init__(self, name, flavor, maya_vers=[2023]) -> None:
        super(GN_GameProject, self).__init__(name=name, flavor=flavor)

    def build(self):
        pass


def get_project(flavor):
    """docstring for get_project"""
    project = GN_GameProject(name='GN-Game', flavor=flavor, maya_vers=[2023])
    project.install(src='py', dst='py', local_edit=True)
    project.install(src='resource', dst='resource')
    project.install(src='maya', dst='maya')
    project.install(src='mobu', dst='mobu')

    return project


def build(flavor, maya_ver):
    """docstring for build"""
    project = GN_GameProject(name='GN-Game', flavor=flavor, maya_vers=[maya_ver])
    project.build()
