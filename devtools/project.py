from packages.project import Project


def get_project(flavor):
    """
    Get the project.
    Args:
        flavor ():

    Returns:

    """
    project = Project(name='magic-core', flavor=flavor)
    project.install(src='py', dst='py', local_edit=True)
    project.install(src='publish/env', dst='env')
    project.install(src='plugins', dst='plugins')
    project.install(src='resource', dst='resource')

    return project
