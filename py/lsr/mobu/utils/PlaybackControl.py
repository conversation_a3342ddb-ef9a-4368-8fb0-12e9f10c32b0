import pyfbsdk as fb


def get_time_range(*args, **kwargs):
    """
    Get time range

    Returns:
        list: [start, end]
    """
    start = fb.FBSystem().CurrentTake.LocalTimeSpan.GetStart().GetFrame()
    end = fb.FBSystem().CurrentTake.LocalTimeSpan.GetStop().GetFrame()
    return [start, end]


def get_time_range_time(*args, **kwargs):
    """
    Get time range timeF

    Returns:
        list: [start, end]
    """
    start = fb.FBSystem().CurrentTake.LocalTimeSpan.GetStart().Get()
    end = fb.FBSystem().CurrentTake.LocalTimeSpan.GetStop().Get()
    return [start, end]


def set_time_range(start, end, *args, **kwargs):
    """
    Set time range

    Args:
        start (int):
        end (int):

    Returns:
        None
    """
    if not isinstance(start, int):
        try:
            start = int(float(start))
        except ValueError:
            return

    if not isinstance(end, int):
        try:
            end = int(float(end))
        except ValueError:
            return

    fb.FBSystem().CurrentTake.LocalTimeSpan = fb.FBTimeSpan(fb.FBTime(0, 0, 0, start),
                                                           fb.FBTime(0, 0, 0, end))


def find_limits(pNode, pLLimit=None, pRLimit=None, *args, **kwargs):
    """
    Find limits
    Args:
        pNode ():
        pLLimit ():
        pRLimit ():

    Returns:
        list
    """
    # First let's see if the node has any keys
    if pNode.FCurve:
        for lKey in pNode.FCurve.Keys:
            if pLLimit:
                if lKey.Time.Get() < pLLimit.Get():
                    pLLimit.Set(lKey.Time.Get())
            else:
                pLLimit = fb.FBTime()
                pLLimit.Set(lKey.Time.Get())

            if pRLimit:
                if lKey.Time.Get() > pRLimit.Get():
                    pRLimit.Set(lKey.Time.Get())
            else:
                pRLimit = fb.FBTime()
                pRLimit.Set(lKey.Time.Get())

    # If the node has any children nodes, we navigate those.
    if pNode.Nodes:
        for lNode in pNode.Nodes:
            [pLLimit, pRLimit] = find_limits(lNode, pLLimit, pRLimit)

    return [pLLimit, pRLimit]


def offset_keys(pNode, pDelta, *args, **kwargs):
    """
    Offset keys

    Args:
        pNode ():
        pDelta ():

    Returns:
        None
    """
    # Modify all the keys of the current node.
    if pNode.FCurve:
        lKeys = [lKey for lKey in pNode.FCurve.Keys]

        if pDelta.Get() > 0:
            lKeys.reverse()
        for lKey in lKeys:
            lTime = fb.FBTime()
            lTime.Set(lKey.Time.Get() + pDelta.Get())
            lKey.Time = lTime

    if pNode.Nodes:
        for lNode in pNode.Nodes:
            offset_keys(lNode, pDelta)


def curve_offset(curve, delta, *args, **kwargs):
    """
    Curve offset
    Args:
        curve (fb.FBFCurve):
        delta (fb.FBTime):

    Returns:
        None
    """
    if not isinstance(curve, fb.FBFCurve) and not isinstance(delta, fb.FBTime):
        return

    keys = curve.Keys
    for key in keys:
        time = key.Time
        time -= delta
        key.Time = time

    return


def delete_frame(model, start_frame, duration, *args, **kwargs):
    """
    Delete frame
    Args:
        model (fb.FBModel):
        start_frame (fb.FBTime):
        duration (fb.FBTime):

    Returns:
        None
    """
    if not isinstance(model, fb.FBModel):
        return
    if not isinstance(start_frame, fb.FBTime) and isinstance(duration, fb.FBTime):
        return

    animation_nodes = model.AnimationNode.Nodes
    for anim_node in animation_nodes:
        child_count = len(anim_node.Nodes)

        if child_count == 0:
            curve = anim_node.FCurve
            if curve:
                curve.KeyDeleteByTimeRange(start_frame, duration)
        else:
            for node in anim_node.Nodes:
                Fcurve = node.FCurve
                if Fcurve:
                    Fcurve.KeyDeleteByTimeRange(start_frame, duration)

    return


def get_current_frame(*args, **kwargs):
    """
    Get current frame

    Returns:
        fb.FBTime
    """
    f = fb.FBSystem().LocalTime.GetFrame()
    return f


def get_frame_rate(*args, **kwargs):
    """
    Get frame rate

    Returns:
        float
    """
    return fb.FBPlayerControl().GetTransportFpsValue()


def set_frame_rate(frame_rate, *args, **kwargs):
    """
    Set frame rate

    Args:
        frame_rate (float):

    Returns:
        None
    """
    if not isinstance(frame_rate, int):
        try:
            frame_rate = int(float(frame_rate))
        except ValueError:
            return

    fb.FBPlayerControl().SetTransportFps(fb.FBTimeMode.kFBTimeModeCustom,
                                         frame_rate)

def frames_to_timecode(frames, frame_rate, *args, **kwargs):
    """
    Convert frames to timecode
    Args:
        frames (int): frames
        frame_rate (int): frame rate

    Returns:
        list
    """
    hours = frames // (frame_rate * 3600)
    frames %= frame_rate * 3600

    minutes = frames // (frame_rate * 60)
    frames %= frame_rate * 60

    seconds = frames // frame_rate
    frames %= frame_rate

    time_info = [hours, minutes, seconds, frames]

    return time_info
