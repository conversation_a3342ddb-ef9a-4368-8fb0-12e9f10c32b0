# -*- coding: utf-8 -*-

# Import built-in modules
import os
from functools import partial

from lsr.qt.core import QtWidgets, QtCore
import lsr.mobu.tool_lib.TrimTakeTool.TrimTake as TrimTake
from lsr.qt.core.base_main_window import get_window_class

# get the base main window class
base_class = get_window_class(app_name='Take Trim_Tool v2.0')


class Trim_Tool_UI(base_class):
    """
        The main animation ExportTool_UI UI
        """

    _REUSE_SINGLETON = False

    def __init__(self):
        """ Creates and initializes this window. """
        self.__thread = None
        self.__cur_progress_max = 0
        self.__save_path = None
        super(Trim_Tool_UI, self).__init__()

        self.FBX_FILE_FILTERS = "FBX (*.fbx *.FBX);;FBX (*.FBX);;fbx (*.fbx);;All Files (*.*)"
        self.FBX_selected_filter = "FBX (*.fbx,*.FBX)"
        self.csv_selected_filter = "CSV (*.csv)"

        self.create_connections()

    def setup_ui(self):
        """Creates UI elements."""
        self.resize(764, 380)
        centralwidget = QtWidgets.QWidget(self)

        vbox = QtWidgets.QVBoxLayout(centralwidget)

        csv_hbox = QtWidgets.QHBoxLayout()
        csv_LB = QtWidgets.QLabel(centralwidget)
        csv_LB.setText('Select CSV File')
        csv_hbox.addWidget(csv_LB)
        self.csv_LineEdit = QtWidgets.QLineEdit(centralwidget)
        csv_hbox.addWidget(self.csv_LineEdit)
        self.csv_BTN = QtWidgets.QPushButton(centralwidget)
        self.csv_BTN.setText('Brown')
        csv_hbox.addWidget(self.csv_BTN)
        csv_hbox.setStretch(0, 4)
        csv_hbox.setStretch(1, 10)
        csv_hbox.setStretch(2, 2)
        vbox.addLayout(csv_hbox)
        fbxFolder_hbox = QtWidgets.QHBoxLayout()
        fbxFolder_LB = QtWidgets.QLabel(centralwidget)
        fbxFolder_LB.setText('Select FBX Folder')
        fbxFolder_hbox.addWidget(fbxFolder_LB)
        self.fbxFolder_LineEdit = QtWidgets.QLineEdit(centralwidget)
        fbxFolder_hbox.addWidget(self.fbxFolder_LineEdit)
        self.fbxFolder_BTN = QtWidgets.QPushButton(centralwidget)
        self.fbxFolder_BTN.setText('Brown')
        fbxFolder_hbox.addWidget(self.fbxFolder_BTN)
        fbxFolder_hbox.setStretch(0, 4)
        fbxFolder_hbox.setStretch(1, 10)
        fbxFolder_hbox.setStretch(2, 2)
        vbox.addLayout(fbxFolder_hbox)
        opt_hbox = QtWidgets.QHBoxLayout()
        opt_LB = QtWidgets.QLabel(centralwidget)
        opt_LB.setText('Select Output Folder')
        opt_hbox.addWidget(opt_LB)
        self.opt_LineEdit = QtWidgets.QLineEdit(centralwidget)
        opt_hbox.addWidget(self.opt_LineEdit)
        self.opt_BTN = QtWidgets.QPushButton(centralwidget)
        self.opt_BTN.setText('Brown')
        opt_hbox.addWidget(self.opt_BTN)
        opt_hbox.setStretch(0, 4)
        opt_hbox.setStretch(1, 10)
        opt_hbox.setStretch(2, 2)
        vbox.addLayout(opt_hbox)
        run_hbox = QtWidgets.QHBoxLayout()
        self.Run_BTN = QtWidgets.QPushButton(centralwidget)
        self.Run_BTN.setText('Run')
        run_hbox.addWidget(self.Run_BTN)
        vbox.addLayout(run_hbox)

        self.prog_bar = QtWidgets.QProgressBar()
        vbox.addWidget(self.prog_bar)
        self.prog_bar.setValue(0)
        self.prog_bar.setVisible(False)

        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum,
                                           QtWidgets.QSizePolicy.Expanding)
        vbox.addItem(spacerItem)

        self.setCentralWidget(centralwidget)

    def hideEvent(self, event):
        """ Save settings before hiding. """
        self.closeEvent(event)

    def create_connections(self):
        """
        create_connections
        """
        self.csv_BTN.clicked.connect(partial(self.show_dialog, self.csv_LineEdit, 'file'))
        self.fbxFolder_BTN.clicked.connect(partial(self.show_dialog, self.fbxFolder_LineEdit, 'folder'))
        self.opt_BTN.clicked.connect(partial(self.show_dialog, self.opt_LineEdit, 'folder'))
        self.Run_BTN.clicked.connect(partial(self.run))

    def save_settings(self):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(Trim_Tool_UI, self).save_settings()

        settings.beginGroup('take_trim_Tool')
        settings.setValue('fbx_folder', self.fbxFolder_LineEdit.text())
        settings.setValue('opt_folder', self.opt_LineEdit.text())
        settings.setValue('csv_file', self.csv_LineEdit.text())

        settings.endGroup()

        settings.sync()
        return settings

    def load_settings(self):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(Trim_Tool_UI, self).load_settings()

        settings.beginGroup('take_trim_Tool')
        self.fbxFolder_LineEdit.setText(settings.value('fbx_folder', ''))
        self.opt_LineEdit.setText(settings.value('opt_folder', ''))
        self.csv_LineEdit.setText(settings.value('csv_file', ''))

        settings.endGroup()

        return settings

    def show_dialog(self, widget, dialog_type='file', *args, **kwargs):
        file_path = None
        if dialog_type == 'file':
            file_path, self.csv_selected_filter = QtWidgets.QFileDialog.getOpenFileName(
                self, 'Select File', '', self.csv_selected_filter, self.csv_selected_filter)
        elif dialog_type == 'folder':
            file_path = QtWidgets.QFileDialog.getExistingDirectory(
                self, 'Select Folder')

        if file_path:
            widget.setText(file_path)
            # print(file_path)
            return file_path
        else:
            return None

    def run(self, *args, **kwargs):
        """
        main function

        :return:
        """
        folder = self.fbxFolder_LineEdit.text()
        opt_folder = self.opt_LineEdit.text()
        csv_path = self.csv_LineEdit.text()
        if not os.path.exists(folder):
            QtWidgets.QMessageBox.warning(self,
                                          'Warning',
                                          'Path is not exist!',
                                          QtWidgets.QMessageBox.Ok)
            return

        trimtake = TrimTake.TrimTake(folder=folder,
                                     opt_folder=opt_folder,
                                     csv_path=csv_path)
        trimtake.run(self)

        return
