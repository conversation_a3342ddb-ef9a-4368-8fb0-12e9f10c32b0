import pyfbsdk as fb

from Qt import QtCore, QtWidgets
from lsr.mobu.tool_lib.BatchRetargetTool import UI
from lsr.mobu.OG2_tool_lib.BatchRetargetToolOG2.Batch_Retarget_OG2 import RetargetThread

base_class = UI.Batch_Retarget_Tool


class BatchRetargetToolForOG2(base_class):

    _APP_NAME = "Batch Retarget Tool -- OG2"

    def __init__(self, *args, **kwargs):
        super(BatchRetargetToolForOG2, self).__init__()

        self.og2_2077_retarget_ui()
        self.init_og2_fps_ui()
        self.centralwidget.layout().layout().addLayout(self.horizontalLayout_3)
        self.resize(764, 370)

    def og2_2077_retarget_ui(self, *args, **kwargs):
        vbox = self.centralwidget.layout()

        self.cbox_for_2077 = QtWidgets.QCheckBox('Retarget For 2077', self.centralwidget)
        h_lay_2077_btns = QtWidgets.QHBoxLayout()
        self.label_2077 = QtWidgets.QLabel(self.centralwidget)
        self.label_2077.setText("Select 2077 Character file:")
        h_lay_2077_btns.addWidget(self.label_2077)
        self.line_2077_character = UI.DragDropLineEdit(self.centralwidget)
        h_lay_2077_btns.addWidget(self.line_2077_character)
        self.btn_2077 = QtWidgets.QPushButton(self.centralwidget)
        self.btn_2077.setText("Brown")
        h_lay_2077_btns.addWidget(self.btn_2077)
        h_lay_2077_btns.setStretch(0, 4)
        h_lay_2077_btns.setStretch(1, 10)
        h_lay_2077_btns.setStretch(2, 2)

        vbox.insertWidget(4, self.cbox_for_2077)
        vbox.insertLayout(5, h_lay_2077_btns)

        self.btn_2077.clicked.connect(UI.partial(self.show_dialog, self.line_2077_character, 'file'))

    def init_og2_fps_ui(self, *args, **kwargs):
        self.fps_label = QtWidgets.QLabel('Target Frame Rate:', self.centralwidget)
        self.fps_dsb = QtWidgets.QDoubleSpinBox(self.centralwidget, minimum=12, maximum=120)
        self.fps_dsb.setSingleStep(1)
        self.fps_dsb.setValue(30)
        # self.fps_dsb.setEnabled(False)
        # self.fps_label.setVisible(False)
        # self.fps_dsb.setVisible(False)
        self.centralwidget.layout().insertWidget(6, self.fps_label)
        self.centralwidget.layout().insertWidget(7, self.fps_dsb)


    @QtCore.Slot()
    def start_thread(self, *args, **kwargs):
        """
        Main func to execute a batch retarget.

            -> rewrite by oxenLiang
        """

        app = fb.FBApplication()
        self.fbxFilesList = []
        fbx_folder = self.fbxFolder_LineEdit.text()
        temp = str(self.temp_LineEdit.text())
        opt_folder = str(self.opt_LineEdit.text())

        kwargs = {}
        kwargs['retarget_setting'] = self.retarget_setting
        kwargs['use_retarget_setting'] = self.use_retarget_setting_cbox.isChecked()
        kwargs['fbxSrcFolder'] = fbx_folder
        kwargs['fbxFilesList'] = self.get_file_list(fbx_folder)
        kwargs['temp'] = temp
        kwargs['opt_folder'] = opt_folder
        kwargs['copy_root'] = self.copy_root_checkBox.isChecked()
        kwargs['create_root_motion'] = self.create_root_motion_cb.isChecked()
        kwargs['create_root_rotation'] = self.create_root_rotation_cb.isChecked()
        kwargs['aim_axis'] = [value for btn, value in self.aim_axis_btn_dict.items() if btn.isChecked()][0]
        kwargs['up_axis'] = [value for btn, value in self.up_axis_btn_dict.items() if btn.isChecked()][0]
        kwargs['target_fps'] = self.fps_dsb.value()

        # for 2077
        character_2077 = str(self.line_2077_character.text())
        kwargs['retarget_for_2077'] = self.cbox_for_2077.isChecked()
        kwargs['origin_character_file'] = character_2077

        thread = RetargetThread(parent=self, **kwargs)
        thread.retarget_started.connect(self.set_progress_start)
        thread.frame_count_changed.connect(self.set_progress_count)
        thread.current_frame_changed.connect(self.increment_progress)

        self.set_progress_count(len(kwargs['fbxFilesList']))

        self.__thread = thread
        self.__thread.run()

        # app.FileNew()
        QtWidgets.QMessageBox.information(self, u'Finish Retargeting', u'OK', QtWidgets.QMessageBox.Yes)

    def paintEvent(self, event, *args, **kwargs):
        super(BatchRetargetToolForOG2, self).paintEvent(event)
        if self.cbox_for_2077.isChecked():
            self.label_2077.setVisible(True)
            self.line_2077_character.setVisible(True)
            self.btn_2077.setVisible(True)

            self.copy_root_checkBox.setChecked(False)
            self.copy_root_checkBox.setEnabled(False)
        else:
            self.label_2077.setVisible(False)
            self.line_2077_character.setVisible(False)
            self.btn_2077.setVisible(False)

            self.copy_root_checkBox.setEnabled(True)

    def save_settings(self, *args, **kwargs):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(BatchRetargetToolForOG2, self).save_settings()

        settings.beginGroup('og2')
        settings.setValue('2077_source_character', self.line_2077_character.text())
        settings.setValue('retarget_for_2077', self.cbox_for_2077.isChecked())
        settings.endGroup()

        settings.sync()
        return settings

    def load_settings(self, *args, **kwargs):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(BatchRetargetToolForOG2, self).load_settings()
        settings.beginGroup('og2')
        self.line_2077_character.setText(settings.value('2077_source_character', ''))

        for_2077 = settings.value('retarget_for_2077', 'true')
        for_2077 = True if for_2077 == 'true' else False
        self.cbox_for_2077.setChecked(for_2077)
        settings.endGroup()

        return settings

