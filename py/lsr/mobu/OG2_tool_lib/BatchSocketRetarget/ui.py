# -*- coding: utf-8 -*-
import os
from functools import partial

from lsr.qt.core.base_main_window import get_window_class
from lsr.qt.core.widgets.drag_drop_widget import DragDropLineEdit
from Qt import QtWidgets, QtCore
from lsr.mobu.OG2_tool_lib.BatchSocketRetarget.socket_retarget_base import SocketRetargetBase
from lsr.mobu.tool_lib.BatchRetargetTool.retargetSettingDialog import RetargetSettingDialog

import pyfbsdk as fb

# get the base main window class
base_class = get_window_class(app_name='Socket Retarget v0.0.0.1')


class SocketRetargetTool(base_class):
    """
    The main Socket_Retarget_Tool UI
    """
    name_spine_weapon_l = 'weapon_ToWorld_Target_Chest_Left'
    name_spine_weapon_r = 'weapon_ToWorld_Target_Chest_Right'
    name_mag_load_l = 'mag_loaded_l'
    name_mag_load_r = 'mag_loaded_r'
    name_mag_reload_l = 'mag_reload_l'
    name_mag_reload_r = 'mag_reload_r'
    name_weapon_hand_l = 'weapon_l'
    name_weapon_hand_r = 'weapon_r'
    name_spine_item = 'item_spine'
    name_hand_item_l = 'item_l'
    name_hand_item_r = 'item_r'

    def __init__(self):
        """ Creates and initializes this window. """
        super(SocketRetargetTool, self).__init__(
            banner_widget=True, has_art=True, top=True,
            email_address='<EMAIL>')
        self.json_selected_filter = "FBX (*.fbx *.FBX)"
        self.retarget_setting = {}

    def setup_ui(self):
        self._init_central_widget()
        self._init_info_collection_ui()
        self._init_checkbox_socket()
        self._init_run_btn()

        self.layout().setAlignment(QtCore.Qt.AlignTop)

    def _init_central_widget(self):
        self.central_widget = QtWidgets.QWidget(self)
        self.setCentralWidget(self.central_widget)
        vbox = QtWidgets.QVBoxLayout(self.central_widget)
        vbox.setSpacing(3)
        vbox.setContentsMargins(5, 5, 5, 5)
        vbox.setAlignment(QtCore.Qt.AlignTop)

    def _init_run_btn(self):
        self.run_btn = QtWidgets.QPushButton('run', self.central_widget)
        self.central_widget.layout().addWidget(self.run_btn)

        self.run_btn.clicked.connect(self.run_method)

    def _init_checkbox_socket(self):
        self.item_weapon_ref_grp = QtWidgets.QGroupBox(u'参考挂点(武器/道具)', self.central_widget)
        # socket
        self.hand_item_socket_rb = QtWidgets.QRadioButton(u'手部item挂点(item_l/item_r)', self.central_widget)
        self.spine_weapon_socket_rb = QtWidgets.QRadioButton(u'spine武器挂点(weapon_ToWorld_Target_Chest_Left / '
                                                             u'weapon_ToWorld_Target_Chest_Right)',
                                                             self.central_widget)
        self.spine_item_socket_rb = QtWidgets.QRadioButton(u'spine下的item挂点(背包类)(item_spine)', self.central_widget)
        self.hand_weapon_socket_rb = QtWidgets.QRadioButton(u'手部武器挂点(weapon_l/weapon_r)', self.central_widget)
        # btn grp
        self.ref_btn_grp = QtWidgets.QButtonGroup(self.central_widget)
        self.ref_btn_grp.addButton(self.hand_item_socket_rb)
        self.ref_btn_grp.addButton(self.spine_weapon_socket_rb)
        self.ref_btn_grp.addButton(self.spine_item_socket_rb)
        self.ref_btn_grp.addButton(self.hand_weapon_socket_rb)
        # ref side
        self.left_hand_cb = QtWidgets.QCheckBox(u'左手手持', self.central_widget)
        self.right_hand_cb = QtWidgets.QCheckBox(u'右手手持', self.central_widget)

        cb_h_1lay = QtWidgets.QHBoxLayout()
        cb_h_1lay.setAlignment(QtCore.Qt.AlignLeft)
        cb_h_1lay.addWidget(self.item_weapon_ref_grp)
        grp_base_lay = QtWidgets.QHBoxLayout(self.item_weapon_ref_grp)
        self.item_weapon_ref_grp.setLayout(grp_base_lay)
        self.ref_cb_lay = QtWidgets.QVBoxLayout()
        self.side_cb_lay = QtWidgets.QVBoxLayout()
        grp_base_lay.addLayout(self.ref_cb_lay)
        grp_base_lay.addLayout(self.side_cb_lay)

        self.ref_cb_lay.addWidget(self.hand_item_socket_rb)
        self.ref_cb_lay.addWidget(self.spine_weapon_socket_rb)
        self.ref_cb_lay.addWidget(self.spine_item_socket_rb)
        self.ref_cb_lay.addWidget(self.hand_weapon_socket_rb)
        self.side_cb_lay.addWidget(self.left_hand_cb)
        self.side_cb_lay.addWidget(self.right_hand_cb)

        # preset state
        self.right_hand_cb.setChecked(True)
        self.hand_item_socket_rb.setChecked(True)

        self.central_widget.layout().addLayout(cb_h_1lay)

        self.ik_mode_cbox = QtWidgets.QCheckBox(u'手部IK模式(强交互动画可勾选)', self.central_widget)
        self.ik_mode_cbox.setChecked(True)
        self.central_widget.layout().addWidget(self.ik_mode_cbox)

        self.engine_mode_cbox = QtWidgets.QCheckBox(u'引擎模式(可修复不同轴向和Unreal Take命名，源文件为UE导出的动画时请勾选)', self.central_widget)
        self.engine_mode_cbox.setChecked(True)
        self.central_widget.layout().addWidget(self.engine_mode_cbox)

        self.use_retarget_setting_cbox = QtWidgets.QCheckBox(u'使用重定向高级设置', self.central_widget)
        self.retarget_setting_btn = QtWidgets.QPushButton(u'编辑重定向设置', self.central_widget)
        self.retarget_setting_btn.clicked.connect(partial(self.get_retarget_setting))
        h_1lay = QtWidgets.QHBoxLayout()
        h_1lay.setAlignment(QtCore.Qt.AlignLeft)
        h_1lay.addWidget(self.use_retarget_setting_cbox)
        h_1lay.addWidget(self.retarget_setting_btn)
        self.central_widget.layout().addLayout(h_1lay)

    def _init_info_collection_ui(self):
        # 1
        label_1 = QtWidgets.QLabel(u'选择旧动画文件夹', self.central_widget)
        self.source_folder_line = DragDropLineEdit(self.central_widget)
        self.source_folder_brown_btn = QtWidgets.QPushButton("Brown", self.central_widget)

        self.source_folder_brown_btn.clicked.connect(partial(self.show_dialog, self.source_folder_line, 'folder'))

        self.source_anim_lay = QtWidgets.QHBoxLayout()
        self.source_anim_lay.addWidget(label_1)
        self.source_anim_lay.addWidget(self.source_folder_line)
        self.source_anim_lay.addWidget(self.source_folder_brown_btn)
        self.source_anim_lay.setStretch(0, 4)
        self.source_anim_lay.setStretch(1, 10)
        self.source_anim_lay.setStretch(2, 2)

        # 2
        label_2 = QtWidgets.QLabel(u'选择输出动画文件夹', self.central_widget)
        self.output_folder_line = DragDropLineEdit(self.central_widget)
        self.output_folder_brown_btn = QtWidgets.QPushButton("Brown", self.central_widget)

        self.output_folder_brown_btn.clicked.connect(partial(self.show_dialog, self.output_folder_line, 'folder'))

        self.output_anim_lay = QtWidgets.QHBoxLayout()
        self.output_anim_lay.addWidget(label_2)
        self.output_anim_lay.addWidget(self.output_folder_line)
        self.output_anim_lay.addWidget(self.output_folder_brown_btn)
        self.output_anim_lay.setStretch(0, 4)
        self.output_anim_lay.setStretch(1, 10)
        self.output_anim_lay.setStretch(2, 2)

        # 3
        label_3 = QtWidgets.QLabel(u'选择旧绑定', self.central_widget)
        self.source_rig_line = DragDropLineEdit(self.central_widget)
        self.source_rig_brown_btn = QtWidgets.QPushButton("Brown", self.central_widget)

        self.source_rig_brown_btn.clicked.connect(partial(self.show_dialog, self.source_rig_line, 'file'))

        self.source_rig_lay = QtWidgets.QHBoxLayout()
        self.source_rig_lay.addWidget(label_3)
        self.source_rig_lay.addWidget(self.source_rig_line)
        self.source_rig_lay.addWidget(self.source_rig_brown_btn)
        self.source_rig_lay.setStretch(0, 4)
        self.source_rig_lay.setStretch(1, 10)
        self.source_rig_lay.setStretch(2, 2)

        # 4
        label_4 = QtWidgets.QLabel(u'选择新绑定', self.central_widget)
        self.new_rig_line = DragDropLineEdit(self.central_widget)
        self.new_rig_brown_btn = QtWidgets.QPushButton("Brown", self.central_widget)

        self.new_rig_brown_btn.clicked.connect(partial(self.show_dialog, self.new_rig_line, 'file'))

        self.new_rig_lay = QtWidgets.QHBoxLayout()
        self.new_rig_lay.addWidget(label_4)
        self.new_rig_lay.addWidget(self.new_rig_line)
        self.new_rig_lay.addWidget(self.new_rig_brown_btn)
        self.new_rig_lay.setStretch(0, 4)
        self.new_rig_lay.setStretch(1, 10)
        self.new_rig_lay.setStretch(2, 2)

        tips_lab = QtWidgets.QLabel(u'      \n本工具主要为OG2的挂点重定向工具\n\n'
                                    u'注意事项：\n'
                                    u'      绑定文件为Actor文件（带武器版本）\n',
                                    self.central_widget)

        self.central_widget.layout().addLayout(self.source_anim_lay)
        self.central_widget.layout().addLayout(self.output_anim_lay)
        self.central_widget.layout().addLayout(self.source_rig_lay)
        self.central_widget.layout().addLayout(self.new_rig_lay)
        self.central_widget.layout().addWidget(tips_lab)

    def show_dialog(self, widget, dialog_type='file'):
        """
        show dialog

        Args:
            widget (DragDropLineEdit):
            dialog_type (str):

        Returns:
            str or None
        """
        file_path = None
        if dialog_type == 'file':
            file_path, self.json_selected_filter = QtWidgets.QFileDialog.getOpenFileName(
                self, 'Select File', '', self.json_selected_filter, self.json_selected_filter)
        elif dialog_type == 'folder':
            file_path = QtWidgets.QFileDialog.getExistingDirectory(
                self, 'Select Folder')

        if file_path:
            widget.setText(file_path)
            return file_path
        else:
            return None

    def get_retarget_setting(self, *args, **kwargs):
        dialog = RetargetSettingDialog(self.retarget_setting, self)
        result = dialog.exec_()
        if result:
            self.retarget_setting = result

    def check_collect_input(self):
        if not os.path.exists(self.source_folder_line.text()):
            QtWidgets.QMessageBox.warning(self, u'错误', u'源动画文件夹不存在')
            return False
        if not os.path.exists(self.source_rig_line.text()):
            QtWidgets.QMessageBox.warning(self, u'错误', u'旧绑定文件不存在')
            return False
        if not os.path.exists(self.new_rig_line.text()):
            QtWidgets.QMessageBox.warning(self, u'错误', u'新绑定文件不存在')
            return False
        if not os.path.exists(self.source_rig_line.text()):
            QtWidgets.QMessageBox.warning(self, u'错误', u'旧绑定文件不存在')
            return False
        return True

    def run_method(self):
        if self.check_collect_input():
            SocketRetargetBase(
                source_rig_path=self.source_rig_line.text(),
                target_rig_path=self.new_rig_line.text(),
                source_folder_path=self.source_folder_line.text(),
                target_folder_path=self.output_folder_line.text(),

                run_hand_item=self.hand_item_socket_rb.isChecked(),
                run_spine_weapon=self.spine_weapon_socket_rb.isChecked(),
                run_spine_item=self.spine_item_socket_rb.isChecked(),
                run_hand_weapon=self.hand_weapon_socket_rb.isChecked(),

                is_left_hand=self.left_hand_cb.isChecked(),
                is_right_hand=self.right_hand_cb.isChecked(),

                ik_mode=self.ik_mode_cbox.isChecked(),

                ue_mode=self.engine_mode_cbox.isChecked(),

                use_retarget_setting=self.use_retarget_setting_cbox.isChecked(),
                retarget_setting=self.retarget_setting
            ).run()

    def save_settings(self):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(SocketRetargetTool, self).save_settings()

        settings.beginGroup('og2_socket_retarget')
        settings.setValue('fbx_folder', self.source_folder_line.text())
        settings.setValue('new_folder', self.output_folder_line.text())
        settings.setValue('old_rig', self.source_rig_line.text())
        settings.setValue('target_rig', self.new_rig_line.text())

        settings.setValue('hand_item', self.hand_item_socket_rb.isChecked())
        settings.setValue('spine_weapon', self.spine_weapon_socket_rb.isChecked())
        settings.setValue('spine_item', self.spine_item_socket_rb.isChecked())
        settings.setValue('hand_weapon', self.hand_weapon_socket_rb.isChecked())
        settings.setValue('left_hand', self.left_hand_cb.isChecked())
        settings.setValue('right_hand', self.right_hand_cb.isChecked())
        settings.setValue('ue_mode', self.engine_mode_cbox.isChecked())
        settings.setValue('retarget_setting', self.retarget_setting)
        settings.setValue('use_retarget_setting', self.use_retarget_setting_cbox.isChecked())
        settings.endGroup()
        settings.sync()
        return settings

    def load_settings(self):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(SocketRetargetTool, self).load_settings()

        settings.beginGroup('og2_socket_retarget')
        self.source_folder_line.setText(settings.value('fbx_folder', ''))
        self.output_folder_line.setText(settings.value('new_folder', ''))
        self.source_rig_line.setText(settings.value('old_rig', ''))
        self.new_rig_line.setText(settings.value('target_rig', ''))

        hand_item = settings.value('hand_item', 'true')
        hand_item = True if hand_item == 'true' else False
        self.hand_item_socket_rb.setChecked(hand_item)

        spine_weapon = settings.value('spine_weapon', 'true')
        spine_weapon = True if spine_weapon == 'true' else False
        self.spine_weapon_socket_rb.setChecked(spine_weapon)

        spine_item = settings.value('spine_item', 'true')
        spine_item = True if spine_item == 'true' else False
        self.spine_item_socket_rb.setChecked(spine_item)

        hand_weapon = settings.value('hand_weapon', 'true')
        hand_weapon = True if hand_weapon == 'true' else False
        self.hand_weapon_socket_rb.setChecked(hand_weapon)

        left_hand = settings.value('left_hand', 'true')
        left_hand = True if left_hand == 'true' else False
        self.left_hand_cb.setChecked(left_hand)

        right_hand = settings.value('right_hand', 'true')
        right_hand = True if right_hand == 'true' else False
        self.right_hand_cb.setChecked(right_hand)

        engine_mode = settings.value('ue_mode', 'true')
        engine_mode = True if engine_mode == 'true' else False
        self.engine_mode_cbox.setChecked(engine_mode)

        use_retarget_setting = settings.value('use_retarget_setting', 'true')
        use_retarget_setting = True if use_retarget_setting == 'true' else False
        self.use_retarget_setting_cbox.setChecked(use_retarget_setting)

        self.retarget_setting = settings.value('retarget_setting', dict())
        settings.endGroup()

        return settings

    def hideEvent(self, event):
        """ Save settings before hiding. """
        self.closeEvent(event)

# ---------------------------------------------------------------------
# Debug code
# ---------------------------------------------------------------------

# import sys
#
# # RELOAD
# try:
#     main.Window.close_instances()
# except Exception:
#     pass
# # delete module cache
# def module_cleanup(module_name):
#     """Cleanup module_name in sys.modules cache.
#
#     Args:
#         module_name (str): Module Name
#     """
#     if module_name in sys.builtin_module_names:
#         return
#     packages = [mod for mod in sys.modules if mod.startswith("%s." % module_name)]
#     for package in packages + [module_name]:
#         module = sys.modules.get(package)
#         if module is not None:
#             del sys.modules[package]
# module_cleanup("lsr.mobu.OG2_tool_lib")
# module_cleanup("lsr.qt")
#
#
# # delete instance cache
# import __main__
# __main__.__dict__['lsr.qt.core.base_main_window_lsr_single_ui']={};
# from lsr.mobu.OG2_tool_lib.BatchSocketRetarget import ui
# ui.SocketRetargetTool.holder_show()
#
#
#
# sys.stdout.flush()

