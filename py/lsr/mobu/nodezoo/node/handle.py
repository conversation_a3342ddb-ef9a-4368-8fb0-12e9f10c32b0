""" Handle node class. """

import pyfbsdk as fb
from six import string_types
from lsr.mobu.nodezoo.node import Box
from lsr.mobu.nodezoo.node import Node
import lsr.mobu.utils.FindObjects as FindObjects


class Handle(Box):
    """
    Wrap FBHandle node class.
    """

    __NODETYPE__ = 'FBHandle'

    @classmethod
    def create(cls, *args, **kwargs):
        """Create a new node."""
        name = kwargs.get('name', None)
        try:
            FindObjects.clear_selection()
            node_class = getattr(fb, cls.__NODETYPE__)
            if name:
                node = node_class(name)
            elif args:
                if isinstance(args[0], string_types):
                    node = node_class(args[0])
                else:
                    raise TypeError('First argument must be a string.')
            else:
                raise TypeError('First argument must be a string.')
            # node = node_class(args, **kwargs)
            node.Show = True
            node.Selected = True
        except RuntimeError as e:
            raise RuntimeError(str(e))
        if node:
            _handle = cls(node)
            _handle.standard3D()
            return _handle

    @property
    def display2D(self):
        """Get display2D."""
        attr = self.fb_node.PropertyList.Find("Visibility")
        return attr.Data

    @display2D.setter
    def display2D(self, value):
        """Set display2D."""
        if not isinstance(value, bool):
            return
        attr = self.fb_node.PropertyList.Find("Visibility")
        attr.Data = value

    @property
    def display3D(self):
        """Get display3D."""
        attr = self.fb_node.PropertyList.Find("HandlerShow3DModel")
        return attr.Data

    @display3D.setter
    def display3D(self, value):
        """Set display3D."""
        if not isinstance(value, bool):
            return
        attr = self.fb_node.PropertyList.Find("HandlerShow3DModel")
        attr.Data = value

    @property
    def look3D(self):
        """Get look3D."""
        attr = self.fb_node.PropertyList.Find("Handler3DModelLook")
        return attr.Data

    @look3D.setter
    def look3D(self, value):
        """Set look3D."""
        if not isinstance(value, int):
            return
        attr = self.fb_node.PropertyList.Find("Handler3DModelLook")
        attr.Data = value

    @property
    def wire_shading(self):
        """Get wire_shading."""
        attr = self.fb_node.PropertyList.Find("HandlerShow3DModelWireFrame")
        return attr.Data

    @wire_shading.setter
    def wire_shading(self, value):
        """Set wire_shading."""
        if not isinstance(value, bool):
            return
        attr = self.fb_node.PropertyList.Find("HandlerShow3DModelWireFrame")
        attr.Data = value

    @property
    def color3D(self):
        """Get color3D."""
        attr = self.fb_node.PropertyList.Find("Handler3DModelColor")
        return attr.Data

    @color3D.setter
    def color3D(self, value):
        """Set color3D."""
        if isinstance(value, (list, tuple)):
            value = fb.FBColor(value[0], value[1], value[2])
        if not isinstance(value, fb.FBColor):
            raise TypeError("Invalid type for FBColor: {}".format(type(value)))
        
        attr = self.fb_node.PropertyList.Find("Handler3DModelColor")
        attr.Data = value

    @property
    def size3D(self):
        """Get size3D."""
        attr = self.fb_node.PropertyList.Find("Handler3DModelScaling")
        return attr.Data

    @size3D.setter
    def size3D(self, value):
        """Set size3D."""
        if isinstance(value, (list, tuple)):
            value = fb.FBVector3d(value[0], value[1], value[2])
        if not isinstance(value, fb.FBVector3d):
            raise TypeError("Invalid type for FBColor: {}".format(type(value)))

        attr = self.fb_node.PropertyList.Find("Handler3DModelScaling")
        attr.Data = value

    @property
    def follow_list(self):
        """Get follow_list."""
        obj_list = self.fb_node.PropertyList.Find("Follow")
        if len(obj_list) == 0:
            return None
        return [Node(obj) for obj in obj_list]

    @follow_list.setter
    def follow_list(self, value):
        """Set follow_list."""
        node_list = []
        if not isinstance(value, (list, tuple)):
            value = [value]
        for obj in value:
            if isinstance(obj, fb.FBModel):
                node_list.append(Node(obj))
            elif isinstance(obj, string_types):
                node_list.append(Node(obj))
            elif isinstance(obj, Node):
                node_list.append(obj)
            else:
                continue

        attr = self.fb_node.PropertyList.Find("Follow")

        for obj in node_list:
            attr.append(obj.fb_node)

    @property
    def manipulate_list(self):
        """Get manipulate_list."""
        obj_list = self.fb_node.PropertyList.Find("Manipulate")
        if len(obj_list) == 0:
            return None
        return [Node(obj) for obj in obj_list]

    @manipulate_list.setter
    def manipulate_list(self, value):
        """Set manipulate_list."""
        node_list = []
        if not isinstance(value, (list, tuple)):
            value = [value]
        for obj in value:
            if isinstance(obj, fb.FBModel):
                node_list.append(Node(obj))
            elif isinstance(obj, string_types):
                node_list.append(Node(obj))
            elif isinstance(obj, Node):
                node_list.append(obj)
            else:
                continue

        attr = self.fb_node.PropertyList.Find("Manipulate")

        for obj in node_list:
            attr.append(obj.fb_node)

    def standard3D(self):
        """Set standardSize."""
        self.display2D = False
        self.display3D = True
        self.look3D = 0
        self.wire_shading = True
        self.color3D = (1.0, 1.0, 0.0)
        self.size3D = (2.0, 2.0, 2.0)
