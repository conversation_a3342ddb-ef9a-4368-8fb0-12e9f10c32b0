""" Shader node class. """

import pyfbsdk as fb
from six import string_types
from lsr.mobu.nodezoo.node import Box
from lsr.mobu.nodezoo.node import Node
import lsr.mobu.utils.FindObjects as FindObjects


class Shader(Box):
    """
    Wrap FBShader node class.
    """

    __NODETYPE__ = 'FBShader'

    @classmethod
    def create(cls, *args, **kwargs):
        """
        Create a Shader.
        Args:
            *args ():
            **kwargs ():

        Returns:

        """
        FindObjects.clear_selection()
        name = kwargs.get('name', None)
        shader_type = kwargs.get('shader_type', 'ReflexionShader')

        _shader = fb.FBShaderManager().CreateShader(shader_type)

        if name:
            _shader.Name = name

        node = Node(_shader)
        return node

    def replace_all(self, model):
        """
        Replace all.
        Args:
            model (Node, FBModel, str): model

        Returns:

        """
        if isinstance(model, Node):
            self.fb_node.ReplaceAll(model.fb_node)
            model.fb_node.Shaders.append(self.fb_node)
        elif isinstance(model, fb.FBModel):
            self.fb_node.ReplaceAll(model)
            model.Shaders.append(self.fb_node)
        elif isinstance(model, string_types):
            model = FindObjects.get_object_by_name(model)
            self.fb_node.ReplaceAll(model)
            model.Shaders.append(self.fb_node)
        else:
            raise TypeError('Argument must be a Node, FBModel or string.')

        return True

    def append_mesh(self, model):
        """
        Append.
        Args:
            model (Node, FBModel, str): model

        Returns:

        """
        if isinstance(model, Node):
            self.fb_node.Append(model.fb_node)
            model.fb_node.Shaders.append(self.fb_node)
        elif isinstance(model, fb.FBModel):
            self.fb_node.Append(model)
            model.Shaders.append(self.fb_node)
        elif isinstance(model, string_types):
            model = FindObjects.get_object_by_name(model)
            self.fb_node.Append(model)
            model.Shaders.append(self.fb_node)
        else:
            raise TypeError('Argument must be a Node, FBModel or string.')

        return True
