"""
Create a HumanIK character
"""

import pyfbsdk as fb

import lsr.protostar.core.parameter as pa
import lsr.mobu.rig.constants as const

import lsr.mobu.utils.FindObjects as FindObjects
from lsr.mobu.utils.anim_util import clear_keyframes
from lsr.mobu.base_actions import RigAction
from lsr.mobu.nodezoo.node import Character
from lsr.mobu.nodezoo.node import Node


class CreateHIK(RigAction):
    """
    Create humanIK character
    """

    _UI_ICON = 'HIKCharacter'

    @pa.str_param(default='')
    def character_name(self):
        """The character name."""

    @pa.enum_param(items=['Biped', 'Quadruped'], default='Biped')
    def character_type(self):
        """The character type."""

    @pa.bool_param(default=True)
    def revert_pose(self):
        """If True, will revert to the bind pose."""

    @pa.pyobject_param(output=True)
    def character(self):
        """The character node."""

    def run(self):
        """Add or get Control Rig"""
        char_node = None
        if self.character_name.value:
            char_node = Character(self.character_name.value)
        else:
            char = fb.FBApplication().CurrentCharacter
            if not char:
                raise ValueError('No character selected')
            char_node = Character(char.LongName)

        if not char_node:
            raise ValueError('No character has been defined')

        if self.revert_pose.value:
            # add skeleton tree keys
            FindObjects.clear_selection()
            char_node.select_bone_hierarchy()
            fb.FBPlayerControl().Key()

        if not char_node.has_control_rig:
            if self.character_type.enum_value == 'Biped':
                char_node.fb_node.SetCharacterizeOn(True)
            else:
                char_node.fb_node.SetCharacterizeOn(False)

            char_node.fb_node.CreateControlRig(True)
            char_node.reference_ctrl.fb_node.Translation = fb.FBVector3d(0, 0, 0)
            char_node.reference_ctrl.fb_node.Rotation = fb.FBVector3d(0, 0, 0)
            if Node.object_exist(const.LIMB_ROOT):
                char_node.reference_ctrl.parent = Node(const.LIMB_ROOT)

            effector_nodes = [Node(effector) for effector in char_node.control_rig_effectors]
            for effector in effector_nodes:
                effector.namespace = const.HIK_NAMESPACE

        else:
            self.warn('{}\'s Control rig already exists'.format(char_node.name))

        char_node.fb_node.PropertyList.Find('Left Shoulder Roll').Data = 100
        char_node.fb_node.PropertyList.Find('Right Shoulder Roll').Data = 100
        char_node.fb_node.PropertyList.Find('Left Hip Roll').Data = 100
        char_node.fb_node.PropertyList.Find('Right Hip Roll').Data = 100

        char_node.active_input = True
        self.character.value = char_node

        if self.revert_pose.value:
            self.current_take = fb.FBSystem().CurrentTake
            self.current_take.CopyTake('APose')
            pose_take = fb.FBSystem().CurrentTake
            char_node.active_input = False
            char_node.plot_controller_animation()
            pose_a = char_node.create_pose('A_Pose')
            fb.FBSystem().CurrentTake = self.current_take
            pose_take.FBDelete()

            for bone in char_node.all_skeletons:
                clear_keyframes(bone)
            fb.FBSystem().Scene.Evaluate()

    def end(self):
        super(CreateHIK, self).end()
        rig_node = Node(const.RIG_ROOT)
        rig_node.add_tag('character', self.character.value)
        FindObjects.clear_selection()
