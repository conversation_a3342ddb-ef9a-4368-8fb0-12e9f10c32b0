# Simplified Chinese translations for mintty package
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the mintty package.
# <PERSON> <<EMAIL>>, 2017.
# GeekDuan<PERSON>ian <<EMAIL>>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: mintty\n"
"Report-Msgid-Bugs-To: https://github.com/mintty/mintty/issues/700\n"
"POT-Creation-Date: 2022-04-24 05:12+0200\n"
"PO-Revision-Date: 2017-10-23 17:09+0800\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.3\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: charset.c:226 charset.c:237 winmain.c:5570 winmain.c:5683 winmain.c:5690
msgid "(Default)"
msgstr "(默认)"

#: charset.c:248
msgid "(OEM codepage)"
msgstr "(OEM 代码页)"

#: charset.c:252
msgid "(ANSI codepage)"
msgstr "(ANSI 代码页)"

#: child.c:96
msgid "There are no available terminals"
msgstr "没有可用的终端"

#: child.c:171
msgid "Error: Could not open log file"
msgstr "错误：无法打开日志文件"

#: child.c:305
msgid "Error: Could not fork child process"
msgstr "错误：无法创建子进程"

#: child.c:307
msgid "DLL rebasing may be required; see 'rebaseall / rebase --help'"
msgstr ""
"可能需要 DLL 地址重定位，运行此命令查看帮助 'rebaseall / rebase --help'"

#. __ %1$s: client command (e.g. shell) to be run; %2$s: error message
#: child.c:397
msgid "Failed to run '%s': %s"
msgstr "运行 '%s' 失败：%s"

#. __ %1$s: client command (e.g. shell) terminated, %2$i: exit code
#: child.c:528
msgid "%s: Exit %i"
msgstr "%s：退出 %i"

#. __ default inline notification if ExitWrite=yes
#: child.c:535
msgid "TERMINATED"
msgstr "已终止"

#: child.c:1148
msgid "Error: Could not fork child daemon"
msgstr "错误：无法创建子进程"

#. __ Options - Text - Emojis - Placement
#: config.c:649
msgid "stretch"
msgstr "拉伸"

#. __ Options - Text - Emojis - Placement
#: config.c:651
msgid "align"
msgstr "适应"

#. __ Options - Text - Emojis - Placement
#: config.c:653
msgid "middle"
msgstr "居中"

#. __ Options - Text - Emojis - Placement
#: config.c:655
msgid "full"
msgstr "填充"

#. __ %s: unknown option name
#: config.c:814
msgid "Ignoring unknown option '%s'"
msgstr "忽略未知选项 '%s'"

#: config.c:859 config.c:888
msgid "Internal error: too many options"
msgstr "内部错误：过多选项"

#: config.c:875
msgid "Internal error: too many options/comments"
msgstr "内部错误：过多选项/注释"

#. __ %2$s: option name, %1$s: invalid value
#. report errors only during initialisation
#: config.c:1045
msgid "Ignoring invalid value '%s' for option '%s'"
msgstr "忽略 '%s' 无效值，属于该选项 '%s'"

#. __ %s: option name
#: config.c:1057
msgid "Ignoring option '%s' with missing value"
msgstr "忽略 '%s' 选项，因为缺少值"

#. __ %1$s: config file name, %2$s: error message
#: config.c:1727
msgid ""
"Could not save options to '%s':\n"
"%s."
msgstr ""
"无法将选项保存到 '%s'：\n"
"%s。"

#: config.c:2166
msgid "◇ None (printing disabled) ◇"
msgstr "◇ 无 (禁用打印) ◇"

#: config.c:2168
msgid "◆ Default printer ◆"
msgstr "◆ 默认打印机 ◆"

#. __ UI language
#: config.c:2277
msgid "– None –"
msgstr "– 无 –"

#: config.c:2278
msgid "@ Windows language @"
msgstr "@ Windows 语言 @"

#: config.c:2279
msgid "* Locale environm. *"
msgstr "* Locale 环境变量 *"

#: config.c:2280
msgid "= cfg. Text Locale ="
msgstr "= 已配置的 Locale ="

#: config.c:2385
msgid "simple beep"
msgstr "简单的嘟声"

#: config.c:2386
msgid "no beep"
msgstr "无提示音"

#: config.c:2387
msgid "Default Beep"
msgstr "默认的嘟声"

#: config.c:2388
msgid "Critical Stop"
msgstr "错误提示音"

#: config.c:2389
msgid "Question"
msgstr "疑问提示音"

#: config.c:2390
msgid "Exclamation"
msgstr "惊叹提示音"

#: config.c:2391
msgid "Asterisk"
msgstr "星号提示音"

#: config.c:2434
msgid "◇ None (system sound) ◇"
msgstr "◇ 无 (系统声音) ◇"

#. __ terminal theme / colour scheme
#. __ emojis style
#: config.c:2804 config.c:3354
msgid "◇ None ◇"
msgstr "◇ 无 ◇"

#. __ indicator of unsaved downloaded colour scheme
#: config.c:2807
msgid "downloaded / give me a name!"
msgstr "已下载 / 给我起名！"

#: config.c:2912
msgid "Could not load web theme"
msgstr "无法加载网络主题"

#: config.c:2969
msgid "Cannot write theme file"
msgstr "无法写入主题文件"

#: config.c:2974
msgid "Cannot store theme file"
msgstr "无法储存主题文件"

#. __ Options - Text:
#: config.c:3431 config.c:3769 config.c:3859
msgid "as font"
msgstr "字形"

#. __ Options - Text:
#: config.c:3432 config.c:3774 config.c:3864
msgid "as colour"
msgstr "颜色"

#: config.c:3433
msgid "as font & as colour"
msgstr "字形 & 颜色"

#. __ Options - Text:
#: config.c:3434 config.c:3779 config.c:3869
msgid "xterm"
msgstr ""

#. __ Dialog button - show About text
#: config.c:3582
msgid "About..."
msgstr "关于..."

#. __ Dialog button - save changes
#: config.c:3585
msgid "Save"
msgstr "保存"

#. __ Dialog button - cancel
#: config.c:3589 winctrls.c:1254 windialog.c:872
msgid "Cancel"
msgstr "取消"

#. __ Dialog button - apply changes
#: config.c:3593
msgid "Apply"
msgstr "应用"

#. __ Dialog button - take notice
#: config.c:3597 windialog.c:869
msgid "I see"
msgstr "我了解了"

#. __ Dialog button - confirm action
#: config.c:3599 winctrls.c:1253 windialog.c:871
msgid "OK"
msgstr "确定"

#. __ Options - Looks: treeview label
#: config.c:3606 config.c:3637 config.c:3696
msgid "Looks"
msgstr "外观"

#. __ Options - Looks: panel title
#: config.c:3608
msgid "Looks in Terminal"
msgstr "终端外观"

#. __ Options - Looks: section title
#: config.c:3610
msgid "Colours"
msgstr "颜色"

#. __ Options - Looks:
#: config.c:3614
msgid "&Foreground..."
msgstr "前景(&F)..."

#. __ Options - Looks:
#: config.c:3618
msgid "&Background..."
msgstr "背景(&B)..."

#. __ Options - Looks:
#: config.c:3622
msgid "&Cursor..."
msgstr "光标(&C)..."

#. __ Options - Looks:
#: config.c:3626
msgid "&Theme"
msgstr "主题(&T)"

#. __ Options - Looks: name of web service
#: config.c:3631
msgid "Color Scheme Designer"
msgstr "颜色样式设计工具"

#. __ Options - Looks: store colour scheme
#: config.c:3634 winctrls.c:461
msgid "Store"
msgstr "保存"

#. __ Options - Looks: section title
#: config.c:3639
msgid "Transparency"
msgstr "透明度"

#. __ Options - Looks: transparency
#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Window:
#: config.c:3645 config.c:4003 config.c:4166 config.c:4363
msgid "&Off"
msgstr "关(&O)"

#. __ Options - Looks: transparency
#: config.c:3647
msgid "&Low"
msgstr "低(&L)"

#. __ Options - Looks: transparency, short form of radio button label "Medium"
#: config.c:3649
msgid "&Med."
msgstr "中(&M)"

#. __ Options - Looks: transparency
#: config.c:3651
msgid "&Medium"
msgstr "中(&M)"

#. __ Options - Looks: transparency
#: config.c:3653
msgid "&High"
msgstr "高(&H)"

#. __ Options - Looks: transparency
#: config.c:3655
msgid "Gla&ss"
msgstr "玻璃"

#. __ Options - Looks: transparency
#: config.c:3662 config.c:3674 config.c:3681
msgid "Opa&que when focused"
msgstr "获得焦点后变为不透明(&Q)"

#. __ Options - Looks: transparency
#: config.c:3667
msgid "Blu&r"
msgstr "模糊(&R)"

#: config.c:3688
msgid "◄"
msgstr ""

#: config.c:3691
msgid "►"
msgstr ""

#. __ Options - Looks: section title
#: config.c:3698
msgid "Cursor"
msgstr "光标"

#. __ Options - Looks: cursor type
#: config.c:3703
msgid "Li&ne"
msgstr "竖线"

#. __ Options - Looks: cursor type
#: config.c:3705
msgid "Bloc&k"
msgstr "方块"

#. __ Options - Looks: cursor type
#: config.c:3708
msgid "Bo&x"
msgstr ""

#. __ Options - Looks: cursor type
#: config.c:3711
msgid "&Underscore"
msgstr "下划线"

#. __ Options - Looks: cursor feature
#: config.c:3716
msgid "Blinkin&g"
msgstr "闪烁(&G)"

#. __ Options - Text: treeview label
#: config.c:3723 config.c:3748 config.c:3763 config.c:3803 config.c:3853
#: config.c:3877 config.c:3890 config.c:3903 config.c:3911
msgid "Text"
msgstr "文本"

#. __ Options - Text: panel title
#: config.c:3725
msgid "Text and Font properties"
msgstr "文本和字体属性"

#. __ Options - Text: section title
#: config.c:3727
msgid "Font"
msgstr "字体"

#. __ Options - Text:
#. __ Font chooser:
#: config.c:3735 winctrls.c:1264
msgid "Font st&yle:"
msgstr "字体样式(&Y)"

#. __ Font chooser:
#: config.c:3740 winctrls.c:1266
msgid "&Size:"
msgstr "大小(&S)"

#. __ Options - Text:
#: config.c:3752 config.c:3822
msgid "Sho&w bold as font"
msgstr "显示粗体字形(&W)"

#. __ Options - Text:
#: config.c:3757 config.c:3827
msgid "Show &bold as colour"
msgstr "显示粗体颜色(&B)"

#. __ Options - Text:
#: config.c:3765 config.c:3786 config.c:3855 config.c:3880
msgid "Show bold"
msgstr "显示粗体的"

#. __ Options - Text:
#: config.c:3790 config.c:3832 config.c:3884
msgid "&Allow blinking"
msgstr "允许闪烁(&A)"

#. __ Options - Text:
#: config.c:3807 config.c:3840 config.c:3875
msgid "Font smoothing"
msgstr "字体平滑"

#. __ Options - Text:
#: config.c:3810 config.c:3843 config.c:4054 config.c:4093 config.c:4248
#: config.c:4261
msgid "&Default"
msgstr "默认(&D)"

#. __ Options - Text:
#. __ Options - Window: scrollbar
#: config.c:3812 config.c:3845 config.c:4052 config.c:4091 config.c:4246
#: config.c:4259 config.c:4344
msgid "&None"
msgstr "无(&N)"

#. __ Options - Text:
#: config.c:3814 config.c:3847 config.c:4053 config.c:4092 config.c:4247
#: config.c:4260
msgid "&Partial"
msgstr "部分(&P)"

#. __ Options - Text:
#: config.c:3816 config.c:3849 config.c:4055 config.c:4094 config.c:4249
#: config.c:4262
msgid "&Full"
msgstr "全部(&F)"

#: config.c:3893
msgid "&Locale"
msgstr "本地 &Locale"

#: config.c:3896
msgid "&Character set"
msgstr "字符集(&C)"

#. __ Options - Text - Emojis:
#. __ Options - Text:
#: config.c:3907 config.c:3913
msgid "Emojis"
msgstr "Emoji"

#. __ Options - Text - Emojis:
#: config.c:3917
msgid "Style"
msgstr "风格"

#. __ Options - Text - Emojis:
#: config.c:3922
msgid "Placement"
msgstr "位置"

#. __ Options - Keys: treeview label
#: config.c:3930 config.c:3955 config.c:3990 config.c:4008
msgid "Keys"
msgstr "按键"

#. __ Options - Keys: panel title
#: config.c:3932
msgid "Keyboard features"
msgstr "键盘特性"

#. __ Options - Keys:
#: config.c:3936
msgid "&Backarrow sends ^H"
msgstr "退格键发送 ^H (&B)"

#. __ Options - Keys:
#: config.c:3941
msgid "&Delete sends DEL"
msgstr "删除键发送 DEL (&D)"

#. __ Options - Keys:
#: config.c:3946
msgid "Ctrl+LeftAlt is Alt&Gr"
msgstr "将 Ctrl+左Alt 设置为 Alt&Gr"

#. __ Options - Keys:
#: config.c:3951
msgid "AltGr is also Alt"
msgstr "将 AltGr 也视为 Alt"

#. __ Options - Keys: section title
#: config.c:3957
msgid "Shortcuts"
msgstr "快捷键"

#. __ Options - Keys:
#: config.c:3960
msgid "Cop&y and Paste (Ctrl/Shift+Ins)"
msgstr "复制和粘贴(&Y) (Ctrl/Shift+Ins)"

#. __ Options - Keys:
#: config.c:3965
msgid "&Menu and Full Screen (Alt+Space/Enter)"
msgstr "显示菜单和全屏(&M) (Alt+Space/Enter)"

#. __ Options - Keys:
#: config.c:3970
msgid "&Switch window (Ctrl+[Shift+]Tab)"
msgstr "切换窗口(&S) (Ctrl+[Shift+]Tab)"

#. __ Options - Keys:
#: config.c:3975
msgid "&Zoom (Ctrl+plus/minus/zero)"
msgstr "缩放(&Z) (Ctrl + +/-/0)"

#. __ Options - Keys:
#: config.c:3980
msgid "&Alt+Fn shortcuts"
msgstr "Alt+Fn 快捷键(&A)"

#. __ Options - Keys:
#: config.c:3985
msgid "&Ctrl+Shift+letter shortcuts"
msgstr "Ctrl+Shift+字母快捷键(&C)"

#. __ Options - Keys: section title
#: config.c:3992 config.c:4010
msgid "Compose key"
msgstr "组合按键"

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Shift:
#. __ Options - Window:
#. __ Options - Modifier - Shift:
#: config.c:3997 config.c:4158 config.c:4177 config.c:4355 config.c:4374
msgid "&Shift"
msgstr ""

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Control:
#. __ Options - Window:
#. __ Options - Modifier - Control:
#: config.c:3999 config.c:4160 config.c:4185 config.c:4357 config.c:4382
msgid "&Ctrl"
msgstr ""

#. __ Options - Keys:
#. __ Options - Mouse:
#. __ Options - Modifier - Alt:
#. __ Options - Window:
#. __ Options - Modifier - Alt:
#: config.c:4001 config.c:4162 config.c:4181 config.c:4359 config.c:4378
msgid "&Alt"
msgstr ""

#. __ Options - Mouse: treeview label
#: config.c:4017 config.c:4106 config.c:4138
msgid "Mouse"
msgstr "鼠标"

#. __ Options - Mouse: panel title
#: config.c:4019
msgid "Mouse functions"
msgstr "鼠标功能"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4027 config.c:4060 config.c:4076 config.c:4226
msgid "Cop&y on select"
msgstr "选中后立即复制(&Y)"

#. __ Options - Mouse:
#. __ Options - Selection:
#. __ Context menu:
#: config.c:4032 config.c:4065 config.c:4231 wininput.c:667
msgid "Copy with TABs"
msgstr "以纯文本复制（包括 &Tab）"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4037 config.c:4070 config.c:4082 config.c:4238
msgid "Copy as &rich text"
msgstr "以富文本复制(&R)"

#. __ Options - Mouse:
#. __ Options - Selection:
#: config.c:4043 config.c:4050 config.c:4089 config.c:4244 config.c:4257
msgid "Copy as &HTML"
msgstr "以 HTML 复制"

#. __ Options - Mouse:
#: config.c:4102
msgid "Clic&ks place command line cursor"
msgstr "使用鼠标点击来定位光标(&K)"

#. __ Options - Mouse: section title
#: config.c:4108
msgid "Click actions"
msgstr "单击动作"

#. __ Options - Mouse:
#: config.c:4111
msgid "Right mouse button"
msgstr "鼠标右键"

#. __ Options - Mouse:
#: config.c:4114 config.c:4128
msgid "&Paste"
msgstr "粘贴(&P)"

#. __ Options - Mouse:
#: config.c:4116 config.c:4130
msgid "E&xtend"
msgstr "扩展(&E)"

#. __ Options - Mouse:
#: config.c:4118
msgid "&Menu"
msgstr "菜单(&M)"

#. __ Options - Mouse:
#: config.c:4120 config.c:4134
msgid "Ente&r"
msgstr "回车(&E)"

#. __ Options - Mouse:
#: config.c:4125
msgid "Middle mouse button"
msgstr "鼠标中键"

#. __ Options - Mouse:
#: config.c:4132
msgid "&Nothing"
msgstr "无(&N)"

#. __ Options - Mouse: section title
#: config.c:4140
msgid "Application mouse mode"
msgstr "应用程序鼠标模式"

#. __ Options - Mouse:
#: config.c:4143
msgid "Default click target"
msgstr "默认点击目标"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4146
msgid "&Window"
msgstr "窗口(&W)"

#. __ Options - Mouse: application mouse mode click target
#: config.c:4148
msgid "&Application"
msgstr "应用程序(&A)"

#. __ Options - Mouse:
#: config.c:4155 config.c:4173
msgid "Modifier for overriding default"
msgstr "覆盖默认配置的修饰键"

#. __ Options - Window:
#. __ Options - Modifier - Win:
#. __ Options - Window:
#. __ Options - Modifier - Win:
#: config.c:4164 config.c:4189 config.c:4361 config.c:4386
msgid "&Win"
msgstr ""

#. __ Options - Modifier - Super:
#: config.c:4193 config.c:4390
msgid "&Sup"
msgstr ""

#. __ Options - Modifier - Hyper:
#: config.c:4197 config.c:4394
msgid "&Hyp"
msgstr ""

#. __ Options - Selection: treeview label
#: config.c:4207 config.c:4220 config.c:4281
msgid "Selection"
msgstr "选中"

#. __ Options - Selection: panel title
#: config.c:4209
msgid "Selection and clipboard"
msgstr "选中字符和剪切板功能"

#. __ Options - Selection:
#: config.c:4213
msgid "Clear selection on input"
msgstr "输入后取消选中"

#. __ Options - Selection: section title
#: config.c:4222
msgid "Clipboard"
msgstr "剪切板"

#. __ Options - Selection:
#: config.c:4271
msgid "Trim space from selection"
msgstr "移除不必要的空格"

#. __ Options - Selection:
#: config.c:4276
msgid "Allow setting selection"
msgstr "允许使用 OSC 52 转义字符设置剪切板内容"

#. __ Options - Selection: section title
#. __ Options - Window: treeview label
#: config.c:4283 config.c:4305 config.c:4330 config.c:4403
msgid "Window"
msgstr "窗口"

#. __ Options - Selection:
#: config.c:4288
msgid "Show size while selecting (0..12)"
msgstr "选择时显示尺寸 (0:关闭/1-12:方位)"

#. __ Options - Selection:
#: config.c:4295
msgid "Suspend output while selecting"
msgstr "选择时暂停屏幕输出"

#. __ Options - Window: panel title
#: config.c:4307
msgid "Window properties"
msgstr "窗口属性"

#. __ Options - Window: section title
#: config.c:4309
msgid "Default size"
msgstr "默认大小"

#. __ Options - Window:
#: config.c:4313
msgid "Colu&mns"
msgstr "列数"

#. __ Options - Window:
#: config.c:4317
msgid "Ro&ws"
msgstr "行数"

#. __ Options - Window:
#: config.c:4321
msgid "C&urrent size"
msgstr "当前大小(&U)"

#. __ Options - Window:
#: config.c:4326
msgid "Re&wrap on resize"
msgstr "调整大小时重新折行(&W)"

#. __ Options - Window:
#: config.c:4334
msgid "Scroll&back lines"
msgstr "保存历史行数"

#. __ Options - Window:
#: config.c:4339
msgid "Scrollbar"
msgstr "滚动条"

#. __ Options - Window: scrollbar
#: config.c:4342
msgid "&Left"
msgstr "左(&L)"

#. __ Options - Window: scrollbar
#: config.c:4346
msgid "&Right"
msgstr "右(&R)"

#. __ Options - Window:
#: config.c:4352 config.c:4370
msgid "Modifier for scrolling"
msgstr "用于滚动的修饰键"

#. __ Options - Window:
#: config.c:4399
msgid "&PgUp and PgDn scroll without modifier"
msgstr "即使不按修饰键，也用 PgUp 和 PgDn 滚动"

#. __ Options - Window: section title
#: config.c:4405
msgid "UI language"
msgstr "界面语言"

#. __ Options - Terminal: treeview label
#: config.c:4415 config.c:4428 config.c:4489 config.c:4503
msgid "Terminal"
msgstr "终端"

#. __ Options - Terminal: panel title
#: config.c:4417
msgid "Terminal features"
msgstr "终端特性"

#. __ Options - Terminal:
#: config.c:4421
msgid "&Type"
msgstr "终端类型(&T)"

#. __ Options - Terminal:
#: config.c:4425
msgid "&Answerback"
msgstr "应答(&A)"

#. __ Options - Terminal: section title
#: config.c:4430
msgid "Bell"
msgstr "响铃"

#. __ Options - Terminal: bell
#: config.c:4437
msgid "► &Play"
msgstr "► 播放(&P)"

#. __ Options - Terminal: bell
#: config.c:4443
msgid "&Wave"
msgstr "声音"

#. __ Options - Terminal: bell
#: config.c:4465 config.c:4478
msgid "&Flash"
msgstr "闪屏(&F)"

#. __ Options - Terminal: bell
#: config.c:4467 config.c:4482
msgid "&Highlight in taskbar"
msgstr "任务栏高亮(&H)"

#. __ Options - Terminal: bell
#: config.c:4469 config.c:4486
msgid "&Popup"
msgstr "弹出(&P)"

#. __ Options - Terminal: section title
#: config.c:4491
msgid "Printer"
msgstr "打印机"

#. __ Options - Terminal:
#: config.c:4506
msgid "Prompt about running processes on &close"
msgstr "退出时提示还在运行的进程(&C)"

#: textprint.c:44 textprint.c:122
msgid "[Printing...] "
msgstr "[打印中...] "

#. __ Options - Text: font chooser activation button
#: winctrls.c:912
msgid "&Select..."
msgstr "选择(&S)..."

#. __ Font chooser: title bar label
#: winctrls.c:1258
msgid "Font "
msgstr "字体 "

#. __ Font chooser: button
#: winctrls.c:1260
msgid "&Apply"
msgstr "应用(&A)"

#. __ Font chooser:
#: winctrls.c:1262
msgid "&Font:"
msgstr "字体(&F)："

#. __ Font chooser:
#: winctrls.c:1268
msgid "Sample"
msgstr "实例"

#. __ Font chooser: text sample ("AaBbYyZz" by default)
#: winctrls.c:1272 winctrls.c:1531 winctrls.c:1696
msgid "Ferqœm’4€"
msgstr "ABC abc 文字实例"

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1289
msgid "Sc&ript:"
msgstr "字符集："

#. __ Font chooser: this field is only shown with FontMenu=1
#: winctrls.c:1291
msgid "<A>Show more fonts</A>"
msgstr "<A>显示更多字体</A>"

#. __ Colour chooser: title bar label
#: winctrls.c:1296
msgid "Colour "
msgstr "颜色 "

#. __ Colour chooser:
#: winctrls.c:1309 winctrls.c:1321
msgid "B&asic colours:"
msgstr "基本颜色(&B)："

#. __ Colour chooser:
#: winctrls.c:1330
msgid "&Custom colours:"
msgstr "自定义颜色(&C)："

#. __ Colour chooser:
#: winctrls.c:1337
msgid "De&fine Custom Colours >>"
msgstr "定义当前颜色(&F)>>"

#. __ Colour chooser:
#: winctrls.c:1340
msgid "Colour"
msgstr "颜色"

#. __ Colour chooser:
#: winctrls.c:1342
msgid "|S&olid"
msgstr "|纯色(&O)"

#. __ Colour chooser:
#: winctrls.c:1344
msgid "&Hue:"
msgstr "色调(&H)"

#. __ Colour chooser:
#: winctrls.c:1347
msgid "&Sat:"
msgstr "饱和度(&S)"

#. __ Colour chooser:
#: winctrls.c:1349
msgid "&Lum:"
msgstr "亮度(&L)"

#. __ Colour chooser:
#: winctrls.c:1351
msgid "&Red:"
msgstr "红色(&R)："

#. __ Colour chooser:
#: winctrls.c:1353
msgid "&Green:"
msgstr "绿色(&G)："

#. __ Colour chooser:
#: winctrls.c:1355
msgid "&Blue:"
msgstr "蓝色(&B)："

#. __ Colour chooser:
#: winctrls.c:1358
msgid "A&dd to Custom Colours"
msgstr "添加到自定义颜色(&D)"

#. __ Options: dialog title
#: windialog.c:243 windialog.c:816
msgid "Options"
msgstr "选项"

#. __ Options: dialog title: "mintty <release> available (for download)"
#: windialog.c:245
msgid "available"
msgstr "可用"

#. __ Options: dialog width scale factor (80...200)
#: windialog.c:760
msgid "100"
msgstr ""

#: windialog.c:901 windialog.c:928
msgid "Error"
msgstr "错误"

#. __ Context menu, session switcher ("virtual tabs") menu label
#: wininput.c:290
msgid "Session switcher"
msgstr "切换会话"

#. __ Context menu, session launcher ("virtual tabs") menu label
#: wininput.c:310
msgid "Session launcher"
msgstr "启动会话"

#: wininput.c:419 wininput.c:425
msgid "Ctrl+"
msgstr ""

#: wininput.c:420 wininput.c:426
msgid "Alt+"
msgstr ""

#: wininput.c:421 wininput.c:427
msgid "Shift+"
msgstr ""

#. __ System menu:
#: wininput.c:452
msgid "&Restore"
msgstr "恢复(&R)"

#. __ System menu:
#: wininput.c:454
msgid "&Move"
msgstr "移动(&M)"

#. __ System menu:
#: wininput.c:456
msgid "&Size"
msgstr "大小(&S)"

#. __ System menu:
#: wininput.c:458
msgid "Mi&nimize"
msgstr "最小化(&N)"

#. __ System menu:
#: wininput.c:460
msgid "Ma&ximize"
msgstr "最大化(&X)"

#. __ System menu:
#: wininput.c:462
msgid "&Close"
msgstr "关闭(&C)"

#. __ System menu:
#: wininput.c:467
msgid "New &Window"
msgstr "新建窗口(&W)"

#. __ System menu:
#: wininput.c:473
msgid "New &Tab"
msgstr "新建标签(&T)"

#. __ Context menu:
#: wininput.c:480
msgid "&Copy"
msgstr "复制(&C)"

#. __ Context menu:
#: wininput.c:499
msgid "&Paste "
msgstr "粘贴(&P) "

#. __ Context menu:
#: wininput.c:504
msgid "Copy → Paste"
msgstr "复制 → 粘贴"

#. __ Context menu:
#: wininput.c:509
msgid "S&earch"
msgstr "搜索(&E)"

#. __ Context menu:
#: wininput.c:516
msgid "&Log to File"
msgstr "写入到日志文件(&L)"

#. __ Context menu:
#: wininput.c:522
msgid "Character &Info"
msgstr "字符详情(&I)"

#. __ Context menu:
#: wininput.c:528
msgid "VT220 Keyboard"
msgstr "VT200 键盘"

#. __ Context menu:
#: wininput.c:533
msgid "&Reset"
msgstr "重置(&R)"

#. __ Context menu:
#: wininput.c:541
msgid "&Default Size"
msgstr "默认大小(&D)"

#. __ Context menu:
#: wininput.c:551
msgid "Scroll&bar"
msgstr "滚动条"

#. __ Context menu:
#: wininput.c:557
msgid "&Full Screen"
msgstr "全屏(&F)"

#. __ Context menu:
#: wininput.c:563
msgid "Flip &Screen"
msgstr "翻转屏幕(&S)"

#. __ System menu:
#: wininput.c:573 wininput.c:744
msgid "Copy &Title"
msgstr "复制标题(&T)"

#. __ System menu:
#. __ Context menu:
#. __ System menu:
#: wininput.c:575 wininput.c:728 wininput.c:746
msgid "&Options..."
msgstr "选项(&O)..."

#. __ Context menu:
#: wininput.c:660
msgid "Ope&n"
msgstr "打开(&O)"

#. __ Context menu:
#: wininput.c:665
msgid "Copy as text"
msgstr "以纯文本复制"

#. __ Context menu:
#: wininput.c:669
msgid "Copy as RTF"
msgstr "以 RTF 复制"

#. __ Context menu:
#: wininput.c:671
msgid "Copy as HTML text"
msgstr "以 HTML 文本复制"

#. __ Context menu:
#: wininput.c:673
msgid "Copy as HTML"
msgstr "以 HTML 复制"

#. __ Context menu:
#: wininput.c:675
msgid "Copy as HTML full"
msgstr "以 HTML 全部复制"

#. __ Context menu:
#: wininput.c:682
msgid "Select &All"
msgstr "全选(&A)"

#. __ Context menu:
#: wininput.c:684
msgid "Save as &Image"
msgstr "保存截图(&I)"

#. __ Context menu: write terminal window contents as HTML file
#: wininput.c:696
msgid "HTML Screen Dump"
msgstr "HTML 屏幕转储"

#. __ Context menu: clear scrollback buffer (lines scrolled off the window)
#: wininput.c:704
msgid "Clear Scrollback"
msgstr "清除滚动历史"

#. __ Context menu: generate a TTY BRK condition (tty line interrupt)
#: wininput.c:714
msgid "Send Break"
msgstr "发送 Break"

#. __ Context menu, user commands
#: wininput.c:813
msgid "User commands"
msgstr "用户命令"

#: wininput.c:1418 wininput.c:1439 wininput.c:1441 wininput.c:1443
#: wininput.c:1480
msgid "[NO SCROLL] "
msgstr "［暂停滚屏］"

#: wininput.c:1431 wininput.c:1440 wininput.c:1445 wininput.c:1501
msgid "[SCROLL MODE] "
msgstr "［滚屏模式］"

#: winmain.c:3064
msgid "Processes are running in session:"
msgstr "在当前会话中还运行着进程："

#: winmain.c:3065
msgid "Close anyway?"
msgstr "确认关闭？"

#: winmain.c:3278
msgid "Try '--help' for more information"
msgstr "使用 --help 参数运行来了解如何使用"

#: winmain.c:3286
msgid "Could not load icon"
msgstr "无法加载图标"

#: winmain.c:5398
msgid "Usage:"
msgstr "用法："

#: winmain.c:5399
msgid "[OPTION]... [ PROGRAM [ARG]... | - ]"
msgstr "[选项]... [ 程序名 [参数]... | - ]"

#. __ help text (output of -H / --help), after initial line ("synopsis")
#: winmain.c:5402
msgid ""
"Start a new terminal session running the specified program or the user's "
"shell.\n"
"If a dash is given instead of a program, invoke the shell as a login shell.\n"
"\n"
"Options:\n"
"  -c, --config FILE     Load specified config file (cf. -C or -o ThemeFile)\n"
"  -e, --exec ...        Treat remaining arguments as the command to execute\n"
"  -h, --hold never|start|error|always  Keep window open after command "
"finishes\n"
"  -p, --position X,Y    Open window at specified coordinates\n"
"  -p, --position center|left|right|top|bottom  Open window at special "
"position\n"
"  -p, --position @N     Open window on monitor N\n"
"  -s, --size COLS,ROWS  Set screen size in characters (also COLSxROWS)\n"
"  -s, --size maxwidth|maxheight  Set max screen size in given dimension\n"
"  -t, --title TITLE     Set window title (default: the invoked command) (cf. "
"-T)\n"
"  -w, --window normal|min|max|full|hide  Set initial window state\n"
"  -i, --icon FILE[,IX]  Load window icon from file, optionally with index\n"
"  -l, --log FILE|-      Log output to file or stdout\n"
"      --nobidi|--nortl  Disable bidi (right-to-left support)\n"
"  -o, --option OPT=VAL  Set/Override config file option with given value\n"
"  -B, --Border frame|void  Use thin/no window border\n"
"  -R, --Report s|o      Report window position (short/long) after exit\n"
"      --nopin           Make this instance not pinnable to taskbar\n"
"  -D, --daemon          Start new instance with Windows shortcut key\n"
"  -H, --help            Display help and exit\n"
"  -V, --version         Print version information and exit\n"
"See manual page for further command line options and configuration.\n"
msgstr ""
"用于新终端会话来运行用户 shell 的指定程序。\n"
"如果指定的程序是 dash，那么将它作为一个登陆 shell 来运行。\n"
"\n"
"选项：\n"
"  -c, --config FILE     加载特定的配置文件（相关选项 -C 或者 -o 主题文件）\n"
"  -e, --exec ...        将剩余参数作为命令来运行\n"
"  -h, --hold never|start|error|always  命令运行结束后维持窗口开启状态\n"
"  -p, --position X,Y    在指定屏幕坐标打开窗口\n"
"  -p, --position center|left|right|top|bottom  在特定位置打开窗口\n"
"  -p, --position @N     在第 N 个显示器打开窗口\n"
"  -s, --size COLS,ROWS  设置屏幕大小（也可以用 COLSxROWS）\n"
"  -s, --size maxwidth|maxheight  设置屏幕的最大宽度或高度\n"
"  -t, --title TITLE     设置窗口标题（默认是运行过的命令（相关选项  -T）\n"
"  -w, --window normal|min|max|full|hide  设置窗口的初始状态\n"
"  -i, --icon FILE[,IX]  从文件加载图标，图标序号可选\n"
"  -l, --log FILE|-      输出日志到文件或者标准输出\n"
"      --nobidi|--nortl  禁用 bidi（从右向左的支持）\n"
"  -o, --option OPT=VAL  用指定的值覆盖配置文件中的值\n"
"  -B, --Border frame|void  使用窗口窄边框/无边框\n"
"  -R, --Report s|o      退出后报告窗口位置信息（短/长）\n"
"      --nopin           让这个实例不能被固定在任务栏上\n"
"  -D, --daemon          使用 Win 快捷键来启动新实例\n"
"  -H, --help            显示帮助信息然后退出\n"
"  -V, --version         打印版本信息然后退出\n"
"请查阅 man 手册了解更多命令行选项和配置相关帮助内容。\n"

#: winmain.c:5570 winmain.c:5683 winmain.c:5690
msgid "WSL distribution '%s' not found"
msgstr "找不到 '%s' WSL 发行版"

#: winmain.c:5721
msgid "Duplicate option '%s'"
msgstr "重复的选项 %s"

#: winmain.c:5729 winmain.c:5815
msgid "Unknown option '%s'"
msgstr "未知的选项 %s"

#: winmain.c:5731
msgid "Option '%s' requires an argument"
msgstr "选项 %s 需要一个参数"

#: winmain.c:5758
msgid "Syntax error in position argument '%s'"
msgstr "position 选项的参数 '%s' 有语法错误"

#: winmain.c:5769
msgid "Syntax error in size argument '%s'"
msgstr "size 选项的参数 '%s' 有语法错误"

#: winmain.c:5924
msgid "Syntax error in geometry argument '%s'"
msgstr "geometry 选项的参数 '%s' 有语法错误"

#: winmain.c:6021
msgid "Mintty could not detach from caller, starting anyway"
msgstr "Mintty 不能从调用者分离，但继续启动"

#: winmain.c:6336
msgid "Using default title due to invalid characters in program name"
msgstr "使用默认的标题栏文字，因为程序名中有无效字符"

#: winsearch.c:232
msgid "◀"
msgstr ""

#: winsearch.c:233
msgid "▶"
msgstr ""

#: winsearch.c:234
msgid "X"
msgstr ""

#. __ Options - Text: font properties information: "Leading": total line padding (see option RowSpacing), Bold/Underline modes (font or manual, see options BoldAsFont/UnderlineManual/UnderlineColour)
#: wintext.c:157
msgid "Leading: %d, Bold: %s, Underline: %s"
msgstr "行间距：%d，粗体：%s，下划线：%s"

#. __ Options - Text: font properties: value taken from font
#: wintext.c:159
msgid "font"
msgstr "字体"

#. __ Options - Text: font properties: value affected by option
#: wintext.c:161
msgid "manual"
msgstr "手动"

#: wintext.c:483
msgid "Font not found, using system substitute"
msgstr "找不到字体，使用系统字体替代"

#: wintext.c:498
msgid "Font has limited support for character ranges"
msgstr "字体对字符范围支持有限"

#: wintext.c:606
msgid "Font installation corrupt, using system substitute"
msgstr "字体安装错误，使用系统字体替代"

#: wintext.c:619
msgid "Font does not support system locale"
msgstr "字体不支持系统语言环境"

#: appinfo.h:60
msgid "There is no warranty, to the extent permitted by law."
msgstr "在法律许可的情况下特此明示，本软件不提供任何担保。"

#. __ %s: WEBSITE (URL)
#: appinfo.h:65
msgid ""
"Please report bugs or request enhancements through the issue tracker on the "
"mintty project page located at\n"
"%s.\n"
"See also the Wiki there for further hints, thanks and credits."
msgstr ""
"在 mintty 项目主页的 Issues 版块反馈问题或者提交功能改进\n"
"地址：%s\n"
"Wiki 页面中有更多帮助、鸣谢和许可等信息。"
