# help.pl.txt - pl GnuPG online help
# Copyright (C) 2007 Free Software Foundation, Inc.
#
# This file is part of GnuPG.
#
# GnuPG is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 3 of the License, or
# (at your option) any later version.
# 
# GnuPG is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
# 
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.


.gpg.edit_ownertrust.value
Te wartości użytkownik przydziela wg swojego uznania; nie będą nigdy
eksportowane poza ten system. Potrzebne są one do zbudowania sieci
zaufania, i nie ma to nic wspólnego z tworzoną automatycznie siecią
certyfikatów.
.

.gpg.edit_ownertrust.set_ultimate.okay
Aby zbu<PERSON>, GnuPG potrzebuje znać klucze do których
masz absolutne zaufanie. Zwykle są to klucze do których masz klucze
tajne. Odpowiedz ,,tak'', jeśli chcesz określić ten klucz jako klucz
do którego masz absolutne zaufanie.

.

.gpg.untrusted_key.override
Jeśli mimo wszystko chcesz użyć tego klucza, klucza, co do którego nie ma
żadnej pewności do kogo należy, odpowiedz ,,tak''.
.

.gpg.pklist.user_id.enter
Podaj adresatów tej wiadomości.
.

.gpg.keygen.algo
Proszę wybrać algorytm.

DSA (znany także jako DSS) to algorytm podpisu cyfrowego (Digital Signature
Algorithm) i może być używany tylko do podpisów.

Elgamal to algorytm tylko do szyfrowania.

RSA może być używany do podpisów lub szyfrowania.

Pierwszy (główny) klucz zawsze musi być kluczem nadającym się do podpisywania.
.

.gpg.keygen.algo.rsa_se
Używanie tego samego klucza do podpisywania i szyfrowania nie jest dobrym
pomysłem. Można tak postępować tylko w niektórych zastosowaniach. Proszę się
najpierw skonsultować z ekspertem od bezpieczeństwa. 
.

.gpg.keygen.size
Wprowadź rozmiar klucza
.

.gpg.keygen.size.huge.okay
Odpowiedz "tak" lub "nie".
.

.gpg.keygen.size.large.okay
Odpowiedz "tak" lub "nie".
.

.gpg.keygen.valid
Wprowadź żądaną wartość (jak w znaku zachęty).
Można tu podać datę w formacie ISO (RRRR-MM-DD) ale nie da to
właściwej obsługi błędów - system próbuje interpretować podaną wartość
jako okres.
.

.gpg.keygen.valid.okay
Odpowiedz "tak" lub "nie".
.

.gpg.keygen.name
Nazwa właściciela klucza.
.

.gpg.keygen.email
proszę wprowadzić opcjonalny ale wysoce doradzany adres e-mail
.

.gpg.keygen.comment
Proszę wprowadzić opcjonalny komentarz
.

.gpg.keygen.userid.cmd
N aby zmienić nazwę (nazwisko).
C aby zmienić komentarz.<
E aby zmienić adres e-mail.
O aby kontynuować tworzenie klucza.
Q aby zrezygnować z tworzenia klucza.
.

.gpg.keygen.sub.okay
Jeśli ma zostać wygenerowany podklucz, należy odpowiedzieć "tak".
.

.gpg.sign_uid.okay
Odpowiedz "tak" lub "nie".
.

.gpg.sign_uid.class
Przy podpisywaniu identyfikatora użytkownika na kluczu należy sprawdzić,
czy tożsamość użytkownika odpowiada temu, co jest wpisane w identyfikatorze.
Innym użytkownikom przyda się informacja, jak dogłębnie zostało to przez
Ciebie sprawdzone.

"0" oznacza, że nie podajesz żadnych informacji na temat tego jak dogłębnie
    tożsamość użytkownika została przez Ciebie potwierdzona.

"1" oznacza, że masz przekonanie, że tożsamość użytkownika odpowiada
    identyfikatorowi klucza, ale nie było możliwości sprawdzenia tego.
    Taka sytuacja występuje też kiedy podpisujesz identyfikator będący
    pseudonimem.

"2" oznacza, że tożsamość użytkownika została przez Ciebie potwierdzona
    pobieżnie - sprawdziliście odcisk klucza, sprawdziłaś/eś tożsamość
    na okazanym dokumencie ze zdjęciem.

"3" to dogłębna weryfikacja tożsamości. Na przykład sprawdzenie odcisku
    klucza, sprawdzenie tożsamości z okazanego oficjalnego dokumentu ze
    zdjęciem (np paszportu) i weryfikacja poprawności adresu poczty
    elektronicznej przez wymianę poczty z tym adresem.

Zauważ, że podane powyżej przykłady dla poziomów "2" i "3" to *tylko*
przykłady. Do Ciebie należy decyzja co oznacza "pobieżny" i "dogłębny" w
kontekście poświadczania i podpisywania kluczy.

Jeśli nie wiesz co odpowiedzieć, podaj "0".
.

.gpg.change_passwd.empty.okay
Odpowiedz "tak" lub "nie".
.

.gpg.keyedit.save.okay
Odpowiedz "tak" lub "nie".
.

.gpg.keyedit.cancel.okay
Odpowiedz "tak" lub "nie".
.

.gpg.keyedit.sign_all.okay
Odpowiedz "tak", aby podpisać WSZYSTKIE identyfikatory użytkownika.
.

.gpg.keyedit.remove.uid.okay
Aby skasować ten identyfikator użytkownika (co wiąże się ze utratą
wszystkich jego poświadczeń!) należy odpowiedzieć ,,tak''.
.

.gpg.keyedit.remove.subkey.okay
Aby skasować podklucz należy odpowiedzieć "tak".
.

.gpg.keyedit.delsig.valid
To jest poprawny podpis na tym kluczu; normalnie nie należy go usuwać
ponieważ może być ważny dla zestawienia połączenia zaufania do klucza
którym go złożono lub do innego klucza nim poświadczonego.
.

.gpg.keyedit.delsig.unknown
Ten podpis nie może zostać potwierdzony ponieważ nie ma
odpowiadającego mu klucza publicznego. Należy odłożyć usunięcie tego
podpisu do czasu, kiedy okaże się który klucz został użyty, ponieważ
w momencie uzyskania tego klucza może pojawić się ścieżka zaufania
pomiędzy tym a innym, już poświadczonym kluczem.
.

.gpg.keyedit.delsig.invalid
Ten podpis jest niepoprawny. Można usunąć go ze zbioru kluczy.
.

.gpg.keyedit.delsig.selfsig
To jest podpis wiążący identyfikator użytkownika z kluczem. Nie należy
go usuwać - GnuPG może nie móc posługiwać się dalej kluczem bez
takiego podpisu. Bezpiecznie można go usunąć tylko jeśli ten podpis
klucza nim samym z jakichś przyczyn nie jest poprawny, i klucz jest
drugi raz podpisany w ten sam sposób.
.

.gpg.keyedit.updpref.okay
Przestawienie wszystkich (lub tylko wybranych) identyfikatorów na aktualne
ustawienia. Data na odpowiednich podpisach zostane przesunięta do przodu o
jedną sekundę.

.

.gpg.passphrase.enter
Podaj długie, skomplikowane hasło, np. całe zdanie.

.

.gpg.passphrase.repeat
Proszę powtórzyć hasło, aby upewnić się że nie było pomyłki.
.

.gpg.detached_signature.filename
Podaj nazwę pliku którego dotyczy ten podpis
.

.gpg.openfile.overwrite.okay
Jeśli można nadpisać ten plik, należy odpowiedzieć ,,tak''
.

.gpg.openfile.askoutname
Nazwa pliku. Naciśnięcie ENTER potwierdzi nazwę domyślną (w nawiasach).
.

.gpg.ask_revocation_reason.code
Nalezy podać powód unieważnienia klucza. W zależności od kontekstu można
go wybrać z listy:
  "Klucz został skompromitowany"
      Masz powody uważać że twój klucz tajny dostał się w niepowołane ręce.
  "Klucz został zastąpiony"
      Klucz został zastąpiony nowym.
  "Klucz nie jest już używany"
      Klucz został wycofany z użycia.
  "Identyfikator użytkownika przestał być poprawny"
      Identyfikator użytkownika (najczęściej adres e-mail przestał być
      poprawny.

.

.gpg.ask_revocation_reason.text
Jeśli chcesz, możesz podać opis powodu wystawienia certyfikatu
unieważnienia. Opis powinien byc zwięzły.
Pusta linia kończy wprowadzanie tekstu.

.



# Local variables:
# mode: fundamental
# coding: utf-8
# End:
