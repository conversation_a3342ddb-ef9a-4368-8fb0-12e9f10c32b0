# !!!!!!!   DO NOT EDIT THIS FILE   !!!!!!!
# This file is machine-generated by lib/unicore/mktables from the Unicode
# database, Version 13.0.0.  Any changes made here will be lost!

# !!!!!!!   INTERNAL PERL USE ONLY   !!!!!!!
# This file is for internal use by core Perl only.  The format and even the
# name or existence of this file are subject to change without notice.  Don't
# use it directly.  Use Unicode::UCD to access the Unicode character data
# base.


return <<'END';
V458
36
37
43
44
60
63
94
95
96
97
124
125
126
127
162
167
168
170
172
173
174
178
180
181
184
185
215
216
247
248
706
710
722
736
741
748
749
750
751
768
885
886
900
902
1014
1015
1154
1155
1421
1424
1542
1545
1547
1548
1550
1552
1758
1759
1769
1770
1789
1791
2038
2039
2046
2048
2546
2548
2554
2556
2801
2802
2928
2929
3059
3067
3199
3200
3407
3408
3449
3450
3647
3648
3841
3844
3859
3860
3861
3864
3866
3872
3892
3893
3894
3895
3896
3897
4030
4038
4039
4045
4046
4048
4053
4057
4254
4256
5008
5018
5741
5742
6107
6108
6464
6465
6622
6656
7009
7019
7028
7037
8125
8126
8127
8130
8141
8144
8157
8160
8173
8176
8189
8191
8260
8261
8274
8275
8314
8317
8330
8333
8352
8384
8448
8450
8451
8455
8456
8458
8468
8469
8470
8473
8478
8484
8485
8486
8487
8488
8489
8490
8494
8495
8506
8508
8512
8517
8522
8526
8527
8528
8586
8588
8592
8968
8972
9001
9003
9255
9280
9291
9372
9450
9472
10088
10132
10181
10183
10214
10224
10627
10649
10712
10716
10748
10750
11124
11126
11158
11159
11264
11493
11499
11856
11858
11904
11930
11931
12020
12032
12246
12272
12284
12292
12293
12306
12308
12320
12321
12342
12344
12350
12352
12443
12445
12688
12690
12694
12704
12736
12772
12800
12831
12842
12872
12880
12881
12896
12928
12938
12977
12992
13312
19904
19968
42128
42183
42752
42775
42784
42786
42889
42891
43048
43052
43062
43066
43639
43642
43867
43868
43882
43884
64297
64298
64434
64450
65020
65022
65122
65123
65124
65127
65129
65130
65284
65285
65291
65292
65308
65311
65342
65343
65344
65345
65372
65373
65374
65375
65504
65511
65512
65519
65532
65534
65847
65856
65913
65930
65932
65935
65936
65949
65952
65953
66000
66045
67703
67705
68296
68297
71487
71488
73685
73714
92988
92992
92997
92998
113820
113821
118784
119030
119040
119079
119081
119141
119146
119149
119171
119173
119180
119210
119214
119273
119296
119362
119365
119366
119552
119639
120513
120514
120539
120540
120571
120572
120597
120598
120629
120630
120655
120656
120687
120688
120713
120714
120745
120746
120771
120772
120832
121344
121399
121403
121453
121461
121462
121476
121477
121479
123215
123216
123647
123648
126124
126125
126128
126129
126254
126255
126704
126706
126976
127020
127024
127124
127136
127151
127153
127168
127169
127184
127185
127222
127245
127406
127462
127491
127504
127548
127552
127561
127568
127570
127584
127590
127744
128728
128736
128749
128752
128765
128768
128884
128896
128985
128992
129004
129024
129036
129040
129096
129104
129114
129120
129160
129168
129198
129200
129202
129280
129401
129402
129484
129485
129620
129632
129646
129648
129653
129656
129659
129664
129671
129680
129705
129712
129719
129728
129731
129744
129751
129792
129939
129940
129995
END
