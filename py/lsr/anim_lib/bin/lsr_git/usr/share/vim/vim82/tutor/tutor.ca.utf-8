===============================================================================
=    B e n v i n g u t s   a l   t u t o r   d e l   V I M   -   Versió 1.5   =
===============================================================================

   El Vim és un editor potent i té moltes ordres, massa com per a
   explicar-les totes un tutor com aquest. Aquest tutor està pensat per a
   ensenyar les ordres bàsiques que us permetin fer servir el Vim com a
   editor de propòsit general.

   El temps aproximat de completar el tutor és d'uns 25 o 30 minuts
   depenent de quant temps dediqueu a experimentar.

   Feu una còpia d'aquest fitxer per a practicar-hi (si heu començat amb
   el programa vimtutor això que esteu llegint ja és una còpia).

   És important recordar que aquest tutor està pensat per a ensenyar
   practicant, és a dir que haureu d'executar les ordres si les voleu
   aprendre. Si només llegiu el text el més probable és que les oblideu.

   Ara assegureu-vos que la tecla de bloqueig de majúscules no està
   activada i premeu la tecla  j  per a moure el cursor avall, fins que la
   lliçó 1.1 ocupi completament la pantalla.
   
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Lliçó 1.1:  MOURE EL CURSOR


  ** Per a moure el cursor premeu les tecles h, j, k, l tal com s'indica. **
       ^
       k        Pista: La h és a l'esquerra i mou el cursor cap a l'esquerra.
  < h     l >          La l és a la dreta i mou el cursor cap a la dreta.
       j               La j sembla una fletxa cap avall.
       v
  1. Moveu el cursor per la pantalla fins que us sentiu confortables.

  2. Mantingueu premuda la tecla avall (j) una estona.
---> Ara ja sabeu com moure-us fins a la següent lliçó.

  3. Usant la tecla avall, aneu a la lliçó 1.2.

Nota: Si no esteu segurs de la tecla que heu premut, premeu <ESC> per a
      tornar al mode Normal. Llavors torneu a teclejar l'ordre que volíeu.

Nota: Les tecles de moviment del cursor (fletxes) també funcionen. Però
      usant hjkl anireu més ràpid un cop us hi hagueu acostumant.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Lliçó 1.2: ENTRAR I SORTIR DEL VIM


  !! NOTA: Abans de seguir els passos següents llegiu *tota* la lliçó!!

  1. Premeu <ESC> (per a estar segurs que esteu en el mode Normal).

  2. Teclegeu:                  :q! <ENTRAR>.

---> Amb això sortireu de l'editor SENSE desar els canvis que hagueu pogut
     fer. Si voleu desar els canvis teclegeu:
                                :wq  <ENTRAR>

  3. Quan vegeu l'introductor de l'intèrpret escriviu l'ordre amb la
     qual heu arribat a aquest tutor. Podria ser:   vimtutor <ENTRAR>
                                            O bé:   vim tutor <ENTRAR>

---> 'vim' és l'editor vim, i 'tutor' és el fitxer que voleu editar.

  4. Si heu memoritzat les ordres, feu els passos anteriors, de l'1 al 3,
     per a sortir i tornar a entrar a l'editor. Llavors moveu el cursor
     avall fins a la lliçó 1.3.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     Lliçó 1.3: EDITAR TEXT - ESBORRAR


  ** En mode Normal premeu  x  per a esborrar el caràcter sota el cursor. **

  1. Moveu el cursor fins a la línia que hi ha més avall senyalada amb --->.

  2. Poseu el cursor a sobre el caràcter que cal esborrar per a corregir
     els errors.

  3. Premeu la tecla  x  per a esborrar el caràcter.

  4. Repetiu els passos 2 i 3 fins que la frase sigui correcta.

---> Unna vaaca vva salttar perr sobbree la llluna.

  5. Ara que la línia és correcta, aneu a la lliçó 1.4.

NOTA: Mentre aneu fent no tracteu de memoritzar, practiqueu i prou.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Lliçó 1.4: EDITAR TEXT - INSERIR


           ** En mode Normal premeu  i  per a inserir text. **

  1. Moveu el cursor avall fins la primera línia senyalada amb --->.

  2. Per a fer la primera línia igual que la segona poseu el cursor sobre
     el primer caràcter POSTERIOR al text que s'ha d'inserir.

  3. Premeu la tecla  i  i escriviu el text que falta.

  4. Quan hageu acabat premeu <ESC> per tornar al mode Normal. Repetiu
     els passos 2, 3 i 4 fins a corregir la frase.

---> Falten carctrs en aquesta .
---> Falten alguns caràcters en aquesta línia.

  5. Quan us trobeu còmodes inserint text aneu al sumari de baix.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                              LLIÇÓ 1 SUMARI


  1. El cursor es mou amb les fletxes o bé amb les tecles hjkl.
         h (esquerra)   j (avall)       k (amunt)    l (dreta)

  2. Per a entrar al Vim (des de l'intèrpret) escriviu:  vim FITXER <ENTRAR>

  3. Per a sortir teclegeu:  <ESC> :q! <ENTRAR>  per a descartar els canvis.
             O BÉ teclegeu:  <ESC> :wq <ENTRAR>  per a desar els canvis.

  4. Per a esborrar el caràcter de sota el cursor en el mode Normal premeu: x

  5. Per a inserir text on hi ha el cursor, en mode Normal, premeu:
         i     escriviu el text    <ESC>

NOTA: La tecla <ESC> us porta al mode Normal o cancel·la una ordre que
      estigui a mitges.

Ara continueu a la lliçó 2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Lliçó 2.1: ORDRES PER ESBORRAR


      ** Teclegeu  dw  per a esborrar fins al final d'una paraula. **

  1. Premeu  <ESC>  per estar segurs que esteu en mode normal.

  2. Moveu el cursor avall fins a la línia senyalada amb --->.

  3. Moveu el cursor fins al principi de la paraula que s'ha d'esborrar.

  4. Teclegeu  dw  per a fer desaparèixer la paraula.

NOTA: Les lletres dw apareixeran a la línia de baix de la pantalla mentre
      les aneu escrivint. Si us equivoqueu premeu <ESC> i torneu a començar.

---> Hi ha algunes paraules divertit que no pertanyen paper a aquesta frase.

  5. Repetiu el passos 3 i 4 fins que la frase sigui correcta i continueu
     a la lliçó 2.2.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Lliçó 2.2: MÉS ORDRES PER ESBORRAR


       ** Escriviu  d$  per a esborrar fins al final de la línia. **

  1. Premeu  <ESC>  per a estar segurs que esteu en el mode Normal.

  2. Moveu el cursor avall fins a la línia senyalada amb --->.

  3. Moveu el cursor fins al final de la línia correcta
     (DESPRÉS del primer . ).

  4. Teclegeu  d$  per a esborrar fins al final de la línia.

---> Algú ha escrit el final d'aquesta línia dos cops. línia dos cops.

  5. Aneu a la lliçó 2.3 per a entendre què està passant.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                    Lliçó 2.3: SOBRE ORDRES I OBJECTES


  El format de l'ordre d'esborrar  d  és el següent:

         [nombre]   d   objecte     O BÉ     d   [nombre]   objecte
  On:
    nombre  - és el nombre de cops que s'ha d'executar (opcional, omissió=1).
    d       - és l'ordre d'esborrar.
    objecte - és la cosa amb la qual operar (llista a baix).

  Una petita llista d'objectes:
    w - des del cursor fins al final de la paraula, incloent l'espai.
    e - des del cursor fins al final de la paraula, SENSE incloure l'espai.
    $ - des del cursor fins al final de la línia.

NOTA: Per als aventurers: si teclegeu només l'objecte, en el mode Normal,
      sense cap ordre, el cursor es mourà tal com està descrit a la llista
      d'objectes.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Lliçó 2.4: UNA EXCEPCIÓ A 'ORDRE-OBJECTE'


             ** Teclegeu  dd  per a esborrar tota la línia. **

  Com que molt sovint s'han d'eliminar línies senceres, els programadors
  del Vi van creure que seria més convenient teclejar  dd  per a esborrar
  tota la línia.

  1. Moveu el cursor a la segona línia de la frase de baix.
  2. Teclegeu  dd  per a esborrar la línia.
  3. Ara aneu a la quarta línia.
  4. Teclegeu  2dd  per a esborrar dues línies (recordeu nombre-ordre-objecte).

      1)  Les roses són vermelles,
      2)  El fang és divertit,
      3)  Les violetes són blaves,
      4)  Tinc un cotxe,
      5)  Els rellotges diuen l'hora,
      6)  El sucre és dolç,
      7)  Igual que tu.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                          Lliçó 2.5: L'ORDRE DESFER


 ** Premeu  u  per a desfer els canvis,  U  per a restaurar tota la línia. **

  1. Moveu el cursor sobre el primer error de línia de baix senyalada amb --->
  2. Premeu  x  per a esborrar el caràcter no desitjat.
  3. Ara premeu  u  per a desfer l'última ordre executada.
  4. Aquest cop corregiu tots els errors de la línia amb l'ordre  x.
  5. Ara premeu  U  per a restablir la línia al seu estat original.
  6. Ara premeu  u  uns quants cops per a desfer  U  i les ordres anteriors.
  7. Ara premeu  CONTROL-R  (les dues tecles al mateix temps) uns quants cops
     per a refer les ordres.

---> Correegiu els errors d'aqquesta línia i dessfeu-los aamb desfer.

  8. Aquestes ordres són molt útils. Ara aneu al sumari de la lliçó 2.





~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               LLIÇÓ 2 SUMARI


  1. Per a esborrar del cursor al final de la paraula teclegeu:     dw

  2. Per a esborrar del cursor al final de la línia teclegeu:       d$

  3. Per a esborrar una línia sencera teclegeu:     dd

  4. El format de qualsevol ordre del mode Normal és:

       [nombre]   ordre   objecte     O BÉ     ordre    [nombre]   objecte
     on:
       nombre  - és quants cops repetir l'ordre
       ordre   - és què fer, com ara  d  per esborrar
       objecte - és amb què s'ha d'actuar, com ara  w  (paraula),
                 $ (fins a final de línia), etc.

  5. Per a desfer les accions anteriors premeu:           u
     Per a desfer tots el canvis en una línia premeu:     U
     Per a desfer l'ordre desfer premeu:                  CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Lliçó 3.1: L'ORDRE 'POSAR'


       ** Premeu  p  per a inserir l'última cosa que heu esborrat
                           després del cursor. **


  1. Moveu el cursor a la primera línia de llista de baix.

  2. Teclegeu  dd  per a esborrar la línia i desar-la a la memòria.

  3. Moveu el cursor a la línia ANTERIOR d'on hauria d'anar.

  4. En mode Normal, premeu  p  per a inserir la línia.

  5. Repetiu els passos 2, 3 i 4 per a ordenar les línies correctament.

     d) Pots aprendre tu?
     b) Les violetes són blaves,
     c) La intel·ligència s'aprèn,
     a) Les roses són vermelles,

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                       Lliçó 3.2: L'ORDRE SUBSTITUIR


          ** Premeu  r  i un caràcter per a substituir el caràcter
                             de sota el cursor. **

  1. Moveu el cursor a la primera línia de sota senyalada amb --->.

  2. Moveu el cursor a sobre del primer caràcter equivocat.

  3. Premeu  r  i tot seguit el caràcter correcte per a corregir l'error.

  4. Repetiu els passos 2 i 3 fins que la línia sigui correcta.

--->  Quen van escroure aquerta línia, algh va prémer tikles equivocades!
--->  Quan van escriure aquesta línia, algú va prémer tecles equivocades!

  5. Ara continueu a la lliçó 3.2.

NOTA: Recordeu que heu de practicar, no memoritzar.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Lliçó 3.3: L'ORDRE CANVIAR


       ** Per a canviar una part o tota la paraula, escriviu  cw . **

  1. Moveu el cursor a la primera línia de sota senyalada amb --->.

  2. Poseu el cursor sobre la u de 'lughc'.

  3. Teclegeu  cw  i corregiu la paraula (en aquest cas, escrivint 'ínia'.)

  4. Premeu <ESC> i aneu al següent error.

  5. Repetiu els passos 3 i 4 fins que les dues frases siguin iguals.

---> Aquesta lughc té algunes paradskl que s'han de cdddf.
---> Aquesta línia té algunes paraules que s'han de canviar.

Noteu que  cw  no només canvia la paraula, també us posa en mode d'inserció.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Lliçó 3.4: MÉS CANVIS AMB c


 ** L'ordre canviar s'usa amb els mateixos objectes que l'ordre esborrar. **

  1. L'ordre canviar funciona igual que la d'esborrar. El format és:

       [nombre]   c   objecte     O BÉ      c   [nombre]   objecte

  2. Els objectes són els mateixos,  w  (paraula), $ (final de línia), etc.

  3. Moveu el cursor fins la primera línia senyalada amb --->.

  4. Avanceu fins al primer error.

  5. Premeu  c$  per fer la línia igual que la segona i premeu <ESC>.

---> El final d'aquesta línia necessita canvis per ser igual que la segona.
---> El final d'aquesta línia s'ha de corregir amb l'ordre c$.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               LLIÇÓ 3 SUMARI


  1. Per a tornar a posar el text que heu esborrat, premeu  p . Això posa
     el text esborrat DESPRÉS del cursor (si heu esborrat una línia anirà
     a parar a la línia SEGÜENT d'on hi ha el cursor).

  2. Per a substituir el caràcter de sota el cursor, premeu  r  i tot
     seguit el caràcter que ha de reemplaçar l'original.

  3. L'ordre canviar permet canviar l'objecte especificat, des del cursor
     fins el final de l'objecte. Per exemple,  cw  canvia el que hi ha des
     del cursor fins al final de la paraula, i  c$  fins al final de
     línia.

  4. El format de l'ordre canviar és:

         [nombre]   c   objecte       O BÉ      c   [nombre]   objecte

Ara aneu a la següent lliçó.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Lliçó 4.1: SITUACIÓ I ESTAT DEL FITXER


  ** Premeu CTRL-g per a veure la situació dins del fitxer i el seu estat.
     Premeu SHIFT-g per a anar a una línia determinada. **

  Nota: No proveu res fins que hagueu llegit TOTA la lliçó!!

  1. Mantingueu premuda la tecla Control i premeu  g . A la part de baix
     de la pàgina apareixerà un línia amb el nom del fitxer i la línia en
     la qual us trobeu. Recordeu el número de la línia pel Pas 3.

  2. Premeu Shift-g per a anar al final de tot del fitxer.

  3. Teclegeu el número de la línia on éreu i després premeu Shift-g. Això
     us tornarà a la línia on éreu quan heu premut per primer cop Ctrl-g.
     (Quan teclegeu el número NO es veurà a la pantalla.)

  4. Ara executeu els passos de l'1 al 3.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Lliçó 4.2: L'ORDRE CERCAR


           ** Premeu  /  seguit de la frase que vulgueu cercar. **

  1. En el mode Normal premeu el caràcter  / . Noteu que el cursor apareix
     a la part de baix de la pantalla igual que amb l'ordre : .

  2. Ara escriviu 'errroor' <ENTRAR>. Aquesta és la paraula que voleu
     cercar.

  3. Per a tornar a cercar la mateixa frase, premeu  n .  Per a cercar la
     mateixa frase en direcció contraria, premeu Shift-n .

  4. Si voleu cercar una frase en direcció ascendent, useu l'ordre  ?  en
     lloc de /.

---> "errroor" no és com s'escriu error; errroor és un error.

Nota: Quan la cerca arribi al final del fitxer continuarà a l'inici.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                      Lliçó 4.3: CERCA DE PARÈNTESIS


          ** Premeu  %  per cercar el ), ], o } corresponent. **

  1. Poseu el cursor a qualsevol (, [, o { de la línia senyalada amb --->.

  2. Ara premeu el caràcter  % .

  3. El cursor hauria d'anar a la clau o parèntesis corresponent.

  4. Premeu  %  per a tornar el cursor al primer parèntesi.

---> Això ( és una línia amb caràcters (, [ ] i { } de prova. ))

Nota: Això és molt útil per a trobar errors en programes informàtics!






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Lliçó 4.4: UNA MANERA DE CORREGIR ERRORS


      ** Escriviu  :s/vell/nou/g  per a substituir 'vell' per 'nou'. **

  1. Moveu el cursor a la línia de sota senyalada amb --->.

  2. Escriviu :s/laa/la <ENTRAR>. Aquesta ordre només canvia la primera
     coincidència que es trobi a la línia.

  3. Ara escriviu :s/laa/la/g per a fer una substitució global. Això
     canviarà totes les coincidències que es trobin a la línia.

---> laa millor època per a veure laa flor és laa primavera.

  4. Per a canviar totes les coincidències d'una cadena entre dues línies,
     escriviu  :#,#s/vell/nou/g  on #,# són els nombres de les línies.
     Escriviu  :%s/vell/nou/g  per a substituir la cadena a tot el fitxer.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               LLIÇÓ 4 SUMARI


  1. Ctrl-g mostra la posició dins del fitxer i l'estat del mateix.
     Shift-g us porta al final del fitxer. Un número seguit de Shift-g us
     porta a la línia corresponent.

  2. L'ordre  /  seguida d'una frase cerca la frase cap ENDAVANT.
     L'ordre  ?  seguida d'una frase cerca la frase cap ENDARRERE.
     Després d'una cerca premeu  n  per a trobar la pròxima coincidència en
     la mateixa direcció, o  Shift-n  per a cercar en la direcció contrària.

  3. L'ordre  %  quan el cursor es troba en un (, ), [, ], {, o } troba la
     parella corresponent.

  4. Per a substituir el primer 'vell' per 'nou' en una línia :s/vell/nou
     Per a substituir tots els 'vell' per 'nou' en una línia  :s/vell/nou/g
     Per a substituir frases entre les línies # i #           :#,#s/vell/nou/g
     Per a substituir totes les coincidències en el fitxer    :%s/vell/nou/g
     Per a demanar confirmació cada cop afegiu 'c'            :%s/vell/nou/gc


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Lliçó 5.1: COM EXECUTAR UNA ORDRE EXTERNA


      ** Teclegeu  :!  seguit d'una ordre externa per a executar-la. **

  1. Premeu el familiar  :  per a col·locar el cursor a la part de baix de
     la pantalla.  Això us permet entrar una ordre.

  2. Ara teclegeu el caràcter  !  (signe d'exclamació). Això us permet
     executar qualsevol ordre de l'intèrpret del sistema.

  3. Per exemple, escriviu  ls  i tot seguit premeu <ENTRAR>. Això us
     mostrarà el contingut del directori, tal com si estiguéssiu a la
     línia d'ordres. Proveu  :!dir  si ls no funciona.

Nota:  D'aquesta manera és possible executar qualsevol ordre externa.

Nota:  Totes les ordres  :  s'han d'acabar amb la tecla <ENTRAR>




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
               Lliçó 5.2: MÉS SOBRE L'ESCRIPTURA DE FITXERS


         ** Per a desar els canvis fets, escriviu  :w  FITXER. **

  1. Escriviu  :!dir  o bé  :!ls  per a obtenir un llistat del directori.
     Ja sabeu que heu de prémer <ENTRAR> després d'això.

  2. Trieu un nom de fitxer que no existeixi, com ara PROVA.

  3. Ara feu:  :w PROVA  (on PROVA és el nom que heu triat.)

  4. Això desa el text en un fitxer amb el nom de PROVA. Per a comprovar-ho
     escriviu  :!dir  i mireu el contingut del directori.

Note: Si sortiu del Vim i entreu una altra vegada amb el fitxer PROVA, el
      fitxer serà una còpia exacta del tutor que heu desat.

  5. Ara esborreu el fitxer teclejant (MS-DOS):   :!del PROVA
                                   o bé (Unix):   :!rm PROVA


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                Lliçó 5.3: UNA ORDRE SELECTIVA PER A DESAR


       ** Per a desar una part del fitxer, escriviu  :#,# w FITXER **

  1. Un altre cop, feu  :!dir  o  :!ls  per a obtenir un llistat del
     directori i trieu un nom de fitxer adequat com ara PROVA.

  2. Moveu el cursor a dalt de tot de la pàgina i premeu  Ctrl-g  per
     saber el número de la línia.  RECORDEU AQUEST NÚMERO!

  3. Ara aneu a baix de tot de la pàgina i torneu a prémer  Ctrl-g.
     RECORDEU AQUEST NÚMERO TAMBÉ!

  4. Per a desar NOMÉS una secció en un fitxer, escriviu  :#,# w PROVA  on
     #,# són els dos números que heu recordat (dalt, baix) i PROVA el nom
     del fitxer.

  5. Comproveu que el fitxer nou hi sigui amb  :!dir  però no l'esborreu.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                   Lliçó 5.4: OBTENIR I AJUNTAR FITXERS


         ** Per a inserir el contingut d'un fitxer, feu  :r FITXER **

  1. Assegureu-vos, amb l'ordre  :!dir , que el fitxer PROVA encara hi és.

  2. Situeu el cursor a dalt de tot d'aquesta pàgina.

NOTA: Després d'executar el Pas 3 veureu la lliçó 5.3. Tireu cap avall
      fins a aquesta lliçó un altre cop.

  3. Ara obtingueu el fitxer PROVA amb l'ordre  :r PROVA  on PROVA és el
     nom del fitxer.

NOTA:  El fitxer que obtingueu s'insereix en el lloc on hi hagi el cursor.

  4. Per a comprovar que s'ha obtingut el fitxer tireu enrere i mireu com
     ara hi ha dues còpies de la lliçó 5.3, l'original i la del fitxer.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               LLIÇÓ 5 SUMARI


  1.  :!ordre  executa una ordre externa.

      Alguns exemples útils:
         (MS-DOS)         (Unix)
          :!dir            :!ls          -  mostra un llistat del directori
          :!del FITXER     :!rm FITXER   -  esborra el fitxer FITXER

  2.  :w FITXER  escriu el fitxer editat al disc dur, amb el nom FITXER.

  3.  :#,#w FITXER  desa les línies de # a # en el fitxer FITXER.

  4.  :r FITXER  llegeix el fitxer FITXER del disc dur i l'insereix en el
      fitxer editat a la posició on hi ha el cursor.






~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                         Lliçó 6.1: L'ORDRE OBRIR


        ** Premeu  o  per a obrir una línia i entrar en mode inserció. **

  1. Moveu el cursor a la línia de sota senyalada amb --->.

  2. Premeu o (minúscula) per a obrir una línia a BAIX del cursor i
     situar-vos en mode d'inserció.

  3. Copieu la línia senyalada amb ---> i premeu <ESC> per a tornar al mode
     normal.

---> Després de prémer  o  el cursor se situa a la línia nova en mode inserció.

  4. Per a obrir una línia a SOBRE del cursor, premeu la  O  majúscula, en lloc
     de la minúscula. Proveu-ho amb la línia de sota.
Obriu una línia sobre aquesta prement Shift-o amb el cursor en aquesta línia.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Lliçó 6.2: L'ORDRE AFEGIR


            ** Premeu  a  per a afegir text DESPRÉS del cursor. **

  1. Moveu el cursor al final de la primera línia de sota senyalada
     amb ---> prement  $  en el mode Normal.

  2. Premeu la lletra  a  (minúscula) per a afegir text DESPRÉS del caràcter
     sota el cursor.  (La  A  majúscula afegeix text al final de la línia.)

Nota: Així s'evita haver de prémer  i , l'últim caràcter, el text a inserir,
      la tecla <ESC>, cursor a la dreta, i finalment  x , només per afegir
      text a final de línia.

  3. Ara completeu la primera línia. Tingueu en compte que aquesta ordre
     és exactament igual que la d'inserir, excepte pel que fa al lloc on
     s'insereix el text.

---> Aquesta línia us permetrà practicar
---> Aquesta línia us permetrà practicar afegir text a final de línia.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                 Lliçó 6.3: UNA ALTRA MANERA DE SUBSTITUIR


      ** Teclegeu una  R  majúscula per a substituir més d'un caràcter. **

  1. Moveu el cursor a la línia de sota senyalada amb --->.

  2. Poseu el cursor al principi de la primera paraula que és diferent
     respecte a la segona línia senyalada amb ---> (la paraula "l'última").

  3. Ara premeu  R  i substituïu el que queda de text a la primera línia
     escrivint sobre el text vell, per a fer-la igual que la segona.

---> Per a fer aquesta línia igual que l'última useu les tecles.
---> Per a fer aquesta línia igual que la segona, premeu R i el text nou.

  4. Tingueu en compte que en prémer <ESC> per a sortir, el text que no
     s'hagi alterat es manté.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                        Lliçó 6.4: ESTABLIR OPCIONS

      ** Feu que les ordres cercar o substituir ignorin les diferències
                     entre majúscules i minúscules **

  1. Cerqueu la paraula 'ignorar' amb: /ignorar
     Repetiu-ho uns quants cops amb la tecla n.

  2. Establiu l'opció 'ic' (ignore case) escrivint:
     :set ic

  3. Ara cerqueu 'ignorar' un altre cop amb la tecla n.
     Repetiu-ho uns quants cops més.

  4. Establiu les opcions 'hlsearch' i 'incsearch':
     :set hls is

  5. Ara torneu a executar una ordre de cerca, i mireu què passa:
     /ignorar

  6. Per a treure el ressaltat dels resultats, feu:
     :nohlsearch
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                               LLIÇÓ 6 SUMARI


  1. L'ordre  o  obre una línia a SOTA la del cursor i mou el cursor a la nova
     línia, en mode Inserció.
     La  O  majúscula obre la línia a SOBRE la que hi ha el cursor.

  2. Premeu una  a  per a afegir text DESPRÉS del caràcter a sota del cursor.
     La  A  majúscula afegeix automàticament el text a final de línia.

  3. L'ordre  R  majúscula us posa en mode substitució fins que premeu <ESC>.

  4. Escriviu ":set xxx" per a establir l'opció "xxx"









~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                          LLIÇÓ 7: ORDRES D'AJUDA


                 ** Utilitzeu el sistema intern d'ajuda **

  El Vim té un extens sistema d'ajuda. Per a llegir una introducció proveu una
  d'aquestes tres coses:
        - premeu la tecla <AJUDA> (si la teniu)
        - premeu la tecla <F1> (si la teniu)
        - escriviu   :help <ENTRAR>

  Teclegeu  :q <ENTRAR>  per a tancar la finestra d'ajuda.

  Podeu trobar ajuda sobre pràcticament qualsevol tema passant un argument
  a l'ordre ":help". Proveu el següent (no oblideu prémer <ENTRAR>):

        :help w
        :help c_<T
        :help insert-index
        :help user-manual


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
                     LLIÇÓ 8: CREAR UN SCRIPT D'INICI

                   ** Activeu funcions automàticament **

 El Vim té moltes més funcions que el Vi, però moltes estan desactivades
 per defecte. Per a començar a utilitzar més funcions heu de crear un
 fitxer "vimrc".

  1. Comenceu a editar el fitxer "vimrc", depenent del sistema
        :edit ~/.vimrc               per Unix
        :edit ~/_vimrc               per MS-Windows

  2. Llegiu el fitxer "vimrc" d'exemple:

        :read $VIMRUNTIME/vimrc_example.vim

  3. Deseu el fitxer amb:

        :write

 El pròxim cop que executeu el Vim usarà ressaltat de sintaxi.  Podeu
 afegir els ajustos que vulgueu en aquest fitxer "vimrc".
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Aquí conclou el Tutor del Vim. És una breu introducció a l'editor Vim,
  suficient perquè el pugueu començar a fer servir. No és complet perquè
  el Vim té moltes ordres. Per a llegir el manual de l'usuari, feu:
  ":help user-manual".

  Per a un estudi més a fons us recomanem el següent llibre:
        Vim - Vi Improved - de Steve Oualline
        Editorial: New Riders
  És el primer llibre dedicat completament al Vim, especialment útil per a
  usuaris novells. Conté molts exemples i diagrames.
  Vegeu http://iccf-holland.org/click5.html

  Aquest altre és més vell i tracta més sobre el Vi que sobre el Vim:
        Learning the Vi Editor - de Linda Lamb
        Editorial: O'Reilly & Associates Inc.
  És un bon llibre per a aprendre qualsevol cosa que desitgeu sobre el Vi.
  La sisena edició també inclou informació sobre el Vim.

  Aquest tutorial ha estat escrit per Michael C. Pierce i Robert K. Ware,
  Colorado School of Mines amb la col·laboració de Charles Smith, Colorado
  State University. E-mail: <EMAIL>.

  Modificat pel Vim per Bram Moolenaar.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
