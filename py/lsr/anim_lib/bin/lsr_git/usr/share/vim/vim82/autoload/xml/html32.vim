let g:xmldata_html32 = {
\ 'vimxmlentities': ['AElig', '<PERSON><PERSON>ute', '<PERSON>ci<PERSON>', '<PERSON><PERSON>', 'Aring', 'Atilde', 'Auml', '<PERSON><PERSON>il', 'ETH', 'Eacute', '<PERSON>circ', '<PERSON>grave', 'Euml', 'Iacute', 'Icirc', 'Igrave', 'Iuml', 'Ntilde', 'Oacute', 'Ocirc', 'Ograve', 'Oslash', 'Otilde', 'Ouml', 'THORN', 'Uacute', 'Ucirc', 'Ugrave', 'Uuml', 'Yacute', 'aacute', 'acirc', 'acute', 'aelig', 'agrave', 'amp', 'aring', 'atilde', 'auml', 'brvbar', 'ccedil', 'cedil', 'cent', 'copy', 'curren', 'deg', 'divide', 'eacute', 'ecirc', 'egrave', 'eth', 'euml', 'frac12', 'frac14', 'frac34', 'gt', 'iacute', 'icirc', 'iexcl', 'igrave', 'iquest', 'iuml', 'laquo', 'lt', 'macr', 'micro', 'middot', 'nbsp', 'not', 'ntilde', 'oacute', 'ocirc', 'ograve', 'ordf', 'ordm', 'oslash', 'otilde', 'ouml', 'para', 'plusmn', 'pound', 'raquo', 'reg', 'sect', 'shy', 'sup1', 'sup2', 'sup3', 'szlig', 'thorn', 'times', 'uacute', 'ucirc', 'ugrave', 'uml', 'uuml', 'yacute', 'yen', 'yuml'],
\ 'vimxmlroot': ['html'],
\ 'a': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'rel': [], 'href': [], 'name': [], 'rev': [], 'title': []}
\ ],
\ 'address': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p'],
\ { }
\ ],
\ 'applet': [
\ ['param', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'width': [], 'vspace': [], 'alt': [], 'align': ['top', 'middle', 'bottom', 'left', 'right'], 'name': [], 'height': [], 'hspace': [], 'codebase': [], 'code': []}
\ ],
\ 'area': [
\ [],
\ { 'alt': [], 'coords': [], 'nohref': ['BOOL'], 'href': [], 'shape': ['rect', 'circle', 'poly']}
\ ],
\ 'b': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'base': [
\ [],
\ { 'href': []}
\ ],
\ 'basefont': [
\ [],
\ { 'size': []}
\ ],
\ 'big': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'blockquote': [
\ ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table', 'address'],
\ { }
\ ],
\ 'body': [
\ ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table', 'address'],
\ { 'link': [], 'vlink': [], 'background': [], 'alink': [], 'bgcolor': [], 'text': []}
\ ],
\ 'br': [
\ [],
\ { 'clear': ['none', 'left', 'all', 'right', 'none']}
\ ],
\ 'caption': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['top', 'bottom']}
\ ],
\ 'center': [
\ ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table', 'address'],
\ { }
\ ],
\ 'cite': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'code': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'dd': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table'],
\ { }
\ ],
\ 'dfn': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'dir': [
\ ['li'],
\ { 'compact': ['BOOL']}
\ ],
\ 'div': [
\ ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table', 'address'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'dl': [
\ ['dt', 'dd'],
\ { 'compact': ['BOOL']}
\ ],
\ 'dt': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'em': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'font': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'size': [], 'color': []}
\ ],
\ 'form': [
\ ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'isindex', 'hr', 'table', 'address'],
\ { 'enctype': ['application/x-www-form-urlencoded'], 'action': [], 'method': ['GET', 'POST']}
\ ],
\ 'h1': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'h2': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'h3': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'h4': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'h5': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'h6': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'head': [
\ ['title', 'isindex', 'base', 'script', 'style', 'meta', 'link'],
\ { }
\ ],
\ 'hr': [
\ [],
\ { 'width': [], 'align': ['left', 'right', 'center'], 'size': [], 'noshade': ['BOOL']}
\ ],
\ 'html': [
\ ['head', 'body', 'plaintext'],
\ { 'version': ['-//W3C//DTD HTML 3.2 Final//EN']}
\ ],
\ 'i': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'img': [
\ [],
\ { 'width': [], 'vspace': [], 'alt': [], 'align': ['top', 'middle', 'bottom', 'left', 'right'], 'usemap': [], 'ismap': ['BOOL'], 'src': [], 'height': [], 'border': [], 'hspace': []}
\ ],
\ 'input': [
\ [],
\ { 'maxlength': [], 'align': ['top', 'middle', 'bottom', 'left', 'right'], 'value': [], 'src': [], 'name': [], 'size': [], 'checked': ['BOOL'], 'type': ['TEXT', 'PASSWORD', 'CHECKBOX', 'RADIO', 'SUBMIT', 'RESET', 'FILE', 'IMAGE']}
\ ],
\ 'isindex': [
\ [],
\ { 'prompt': []}
\ ],
\ 'kbd': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'li': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table'],
\ { 'value': [], 'type': []}
\ ],
\ 'link': [
\ [],
\ { 'rel': [], 'href': [], 'rev': [], 'title': []}
\ ],
\ 'listing': [
\ [],
\ { }
\ ],
\ 'map': [
\ ['area'],
\ { 'name': []}
\ ],
\ 'menu': [
\ ['li'],
\ { 'compact': ['BOOL']}
\ ],
\ 'meta': [
\ [],
\ { 'http-equiv': [], 'name': [], 'content': []}
\ ],
\ 'ol': [
\ ['li'],
\ { 'compact': ['BOOL'], 'type': [], 'start': []}
\ ],
\ 'option': [
\ [''],
\ { 'value': [], 'selected': ['BOOL']}
\ ],
\ 'p': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'align': ['left', 'center', 'right']}
\ ],
\ 'param': [
\ [],
\ { 'value': [], 'name': []}
\ ],
\ 'plaintext': [
\ [],
\ { }
\ ],
\ 'pre': [
\ ['tt', 'i', 'b', 'u', 'strike', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'applet', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { 'width': ['#implied']}
\ ],
\ 'samp': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'script': [
\ [],
\ { }
\ ],
\ 'select': [
\ ['option'],
\ { 'name': [], 'size': [], 'multiple': ['BOOL']}
\ ],
\ 'small': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'strike': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'strong': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'style': [
\ [],
\ { }
\ ],
\ 'sub': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'sup': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'table': [
\ ['caption', 'tr'],
\ { 'width': [], 'align': ['left', 'center', 'right'], 'border': [], 'cellspacing': [], 'cellpadding': []}
\ ],
\ 'td': [
\ ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table', 'address'],
\ { 'width': [], 'align': ['left', 'center', 'right'], 'nowrap': ['BOOL'], 'valign': ['top', 'middle', 'bottom'], 'height': [], 'rowspan': ['1'], 'colspan': ['1']}
\ ],
\ 'textarea': [
\ [''],
\ { 'name': [], 'rows': [], 'cols': []}
\ ],
\ 'th': [
\ ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea', 'p', 'ul', 'ol', 'dir', 'menu', 'pre', 'xmp', 'listing', 'dl', 'div', 'center', 'blockquote', 'form', 'isindex', 'hr', 'table', 'address'],
\ { 'width': [], 'align': ['left', 'center', 'right'], 'nowrap': ['BOOL'], 'valign': ['top', 'middle', 'bottom'], 'height': [], 'rowspan': ['1'], 'colspan': ['1']}
\ ],
\ 'title': [
\ [''],
\ { }
\ ],
\ 'tr': [
\ ['th', 'td'],
\ { 'align': ['left', 'center', 'right'], 'valign': ['top', 'middle', 'bottom']}
\ ],
\ 'tt': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'u': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'ul': [
\ ['li'],
\ { 'compact': ['BOOL'], 'type': ['disc', 'square', 'circle']}
\ ],
\ 'var': [
\ ['tt', 'i', 'b', 'u', 'strike', 'big', 'small', 'sub', 'sup', 'em', 'strong', 'dfn', 'code', 'samp', 'kbd', 'var', 'cite', 'a', 'img', 'applet', 'font', 'basefont', 'br', 'script', 'map', 'input', 'select', 'textarea'],
\ { }
\ ],
\ 'xmp': [
\ [],
\ { }
\ ],
\ 'vimxmlattrinfo' : {
\ 'accept' : ['ContentType', ''],
\ 'accesskey' : ['Character', ''],
\ 'action' : ['*URI', ''],
\ 'align' : ['String', ''],
\ 'alt' : ['*Text', ''],
\ 'archive' : ['UriList', ''],
\ 'axis' : ['CDATA', ''],
\ 'border' : ['Pixels', ''],
\ 'cellpadding' : ['Length', ''],
\ 'cellspacing' : ['Length', ''],
\ 'char' : ['Character', ''],
\ 'charoff' : ['Length', ''],
\ 'charset' : ['LangCode', ''],
\ 'checked' : ['Bool', ''],
\ 'class' : ['CDATA', ''],
\ 'codetype' : ['ContentType', ''],
\ 'cols' : ['*Number', ''],
\ 'colspan' : ['Number', ''],
\ 'content' : ['*CDATA', ''],
\ 'coords' : ['Coords', ''],
\ 'data' : ['URI', ''],
\ 'datetime' : ['DateTime', ''],
\ 'declare' : ['Bool', ''],
\ 'defer' : ['Bool', ''],
\ 'dir' : ['String', ''],
\ 'disabled' : ['Bool', ''],
\ 'enctype' : ['ContentType', ''],
\ 'for' : ['ID', ''],
\ 'headers' : ['IDREFS', ''],
\ 'height' : ['Number', ''],
\ 'href' : ['*URI', ''],
\ 'hreflang' : ['LangCode', ''],
\ 'id' : ['ID', ''],
\ 'ismap' : ['Bool', ''],
\ 'label' : ['*Text', ''],
\ 'lang' : ['LangCode', ''],
\ 'longdesc' : ['URI', ''],
\ 'maxlength' : ['Number', ''],
\ 'media' : ['MediaDesc', ''],
\ 'method' : ['String', ''],
\ 'multiple' : ['Bool', ''],
\ 'name' : ['CDATA', ''],
\ 'nohref' : ['Bool', ''],
\ 'onblur' : ['Script', ''],
\ 'onchange' : ['Script', ''],
\ 'onclick' : ['Script', ''],
\ 'ondblclick' : ['Script', ''],
\ 'onfocus' : ['Script', ''],
\ 'onkeydown' : ['Script', ''],
\ 'onkeypress' : ['Script', ''],
\ 'onkeyup' : ['Script', ''],
\ 'onload' : ['Script', ''],
\ 'onmousedown' : ['Script', ''],
\ 'onmousemove' : ['Script', ''],
\ 'onmouseout' : ['Script', ''],
\ 'onmouseover' : ['Script', ''],
\ 'onmouseup' : ['Script', ''],
\ 'onreset' : ['Script', ''],
\ 'onselect' : ['Script', ''],
\ 'onsubmit' : ['Script', ''],
\ 'onunload' : ['Script', ''],
\ 'profile' : ['URI', ''],
\ 'readonly' : ['Bool', ''],
\ 'rel' : ['LinkTypes', ''],
\ 'rev' : ['LinkTypes', ''],
\ 'rows' : ['*Number', ''],
\ 'rules' : ['String', ''],
\ 'scheme' : ['CDATA', ''],
\ 'selected' : ['Bool', ''],
\ 'shape' : ['Shape', ''],
\ 'size' : ['CDATA', ''],
\ 'span' : ['Number', ''],
\ 'src' : ['*URI', ''],
\ 'standby' : ['Text', ''],
\ 'style' : ['StyleSheet', ''],
\ 'summary' : ['*Text', ''],
\ 'tabindex' : ['Number', ''],
\ 'title' : ['Text', ''],
\ 'type' : ['*ContentType', ''],
\ 'usemap' : ['URI', ''],
\ 'valign' : ['String', ''],
\ 'valuetype' : ['String', ''],
\ 'width' : ['Number', ''],
\ 'xmlns' : ['URI', '']
\ },
\ 'vimxmltaginfo': {
\ 'area': ['/>', ''],
\ 'base': ['/>', ''],
\ 'basefont': ['/>', ''],
\ 'br': ['/>', ''],
\ 'hr': ['/>', ''],
\ 'img': ['/>', ''],
\ 'input': ['/>', ''],
\ 'isindex': ['/>', ''],
\ 'link': ['/>', ''],
\ 'meta': ['/>', ''],
\ 'param': ['/>', ''],
\ }
\ }
