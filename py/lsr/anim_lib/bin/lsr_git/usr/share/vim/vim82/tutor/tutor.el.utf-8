===============================================================================
=    Κ αλ ω σ ή ρ θ α τ ε    σ τ ο   V I M   T u t o r    -    Έκδοση 1.5     =
===============================================================================

     Ο Vim είναι ένας πανίσχυρος συντάκτης που έχει πολλές εντολές, πάρα
     πολλές για να εξηγήσουμε σε μία περιήγηση όπως αυτή. Αυτή η περιήγηση
     σχεδιάστηκε για να περιγράψει ικανοποιητικά τις εντολές που θα σας
     κάνουν να χρησιμοποιείτε εύκολα τον Vim σαν έναν γενικής χρήσης συντάκτη.

     Ο κατά προσέγγιση χρόνος που απαιτείται για να ολοκληρώσετε την περιήγηση
     είναι 25-30 λεπτά, εξαρτώντας από το πόσο χρόνο θα ξοδέψετε για
     πειραματισμούς.

     Οι εντολές στα μαθήματα θα τροποποιήσουν το κείμενο. Δημιουργήστε ένα
     αντίγραφο αυτού του αρχείου για να εξασκηθείτε (αν ξεκινήσατε το
     "Vimtutor" αυτό είναι ήδη ένα αντίγραφο).

     Είναι σημαντικό να θυμάστε ότι αυτή η περιήγηση είναι οργανωμένη έτσι
     ώστε να διδάσκει μέσω της χρήσης. Αυτό σημαίνει ότι χρειάζεται να
     εκτελείτε τις εντολές για να τις μάθετε σωστά. Αν διαβάζετε μόνο το
     κείμενο, θα τις ξεχάσετε!

     Τώρα, βεβαιωθείτε ότι το πλήκτρο Caps-Lock ΔΕΝ είναι πατημένο και
     πατήστε το πλήκτρο j αρκετές φορές για να μετακινήσετε τον δρομέα έτσι
     ώστε το Μάθημα 1.1 να γεμίσει πλήρως την οθόνη.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Μάθημα 1.1:  ΜΕΤΑΚΙΝΟΝΤΑΣ ΤΟΝ ΔΡΟΜΕΑ

  ** Για να κινήσετε τον δρομέα, πατήστε τα πλήκτρα h,j,k,l όπως δείχνεται. **
	     ^
	     k	      Hint: Το πλήκτρο h είναι αριστερά και κινεί στ' αριστερά.
       < h	 l >	    Το πλήκτρο l είναι δεξιά και κινεί στα δεξιά.
	     j		    Το πλήκτρο j μοιάζει με βελάκι προς τα κάτω.
	     v

  1. Μετακινείστε τον δρομέα τριγύρω στην οθόνη μέχρι να νοιώθετε άνετα.

  2. Κρατήστε πατημένο το κάτω πλήκτρο (j) μέχρι να επαναληφθεί.
---> Τώρα ξέρετε πώς να μετακινηθείτε στο επόμενο μάθημα.

  3. Χρησιμοποιώντας το κάτω πλήκτρο, μετακινηθείτε στο Μάθημα 1.2.

Σημείωση: Αν αμφιβάλλετε για κάτι που πατήσατε, πατήστε <ESC> για να βρεθείτε
	  στην Κανονική Κατάσταση. Μετά πατήστε ξανά την εντολή που θέλατε.

Σημείωση: Τα πλήκτρα του δρομέα θα πρέπει επίσης να δουλεύουν. Αλλά με τα hjkl
	  θα μπορείτε να κινηθείτε πολύ γρηγορότερα, μόλις τα συνηθίσετε.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		Μάθημα 1.2:  ΜΠΑΙΝΟΝΤΑΣ ΚΑΙ ΒΓΑΙΝΟΝΤΑΣ ΣΤΟΝ VIM

  !! ΣΗΜΕΙΩΣΗ: Πριν εκτελέσετε κάποιο από τα βήματα, διαβάστε όλο το μάθημα!!

  1. Πατήστε το πλήκτρο <ESC> (για να είστε σίγουρα στην Κανονική Κατάσταση).

  2. Πληκτρολογήστε:		:q! <ENTER>.

---> Αυτό εξέρχεται από τον συντάκτη ΧΩΡΙΣ να σώσει όποιες αλλαγές έχετε κάνει.
     Αν θέλετε να σώσετε τις αλλαγές και να εξέρθετε πληκτρολογήστε:
	      :wq <ENTER>

  3. Όταν δείτε την προτροπή του φλοιού, πληκτρολογήστε την εντολή με την οποία
     μπήκατε σε αυτήν την περιήγηση. Μπορεί να είναι:	vimtutor <ENTER>
     Κανονικά θα χρησιμοποιούσατε:			vim tutor <ENTER>

---> 'vim' σημαίνει εισαγωγή στον συντάκτη vim, 'tutor' είναι το αρχείο που
     θέλουμε να διορθώσουμε.

  4. Αν έχετε απομνημονεύσει αυτά τα βήματα και έχετε αυτοπεποίθηση, εκτελέστε
     τα βήματα 1 έως 3 για να βγείτε και να μπείτε ξανά στον συντάκτη. Μετά
     μετακινήστε τον δρομέα κάτω στο Μάθημα 1.3.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Μάθημα 1.3:  ΔΙΟΡΘΩΣΗ ΚΕΙΜΕΝΟΥ - ΔΙΑΓΡΑΦΗ

   ** Όσο είστε στην Κανονική Κατάσταση πατήστε  x  για να διαγράψετε τον
      χαρακτήρα κάτω από τον δρομέα. **

  1. Μετακινείστε τον δρομέα στην παρακάτω γραμμή σημειωμένη με --->.

  2. Για να διορθώσετε τα λάθη, κινείστε τον δρομέα μέχρι να είναι πάνω από
     τον χαρακτήρα που θα διαγραφεί.

  3. Πατήστε το πλήκτρο x για να διαγράψετε τον ανεπιθύμητο χαρακτήρα.

  4. Επαναλάβετε τα βήματα 2 μέχρι 4 μέχρι η πρόταση να είναι σωστή.

---> The ccow jumpedd ovverr thhe mooon.

  5. Τώρα που η γραμμή είναι σωστή, πηγαίντε στο Μάθημα 1.4.

ΣΗΜΕΙΩΣΗ: Καθώς διατρέχετε αυτήν την περιήγηση, προσπαθήστε να μην
	  απομνημονεύετε, μαθαίνετε με τη χρήση.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Μάθημα 1.4: ΔΙΟΡΘΩΣΗ ΚΕΙΜΕΝΟΥ - ΠΑΡΕΜΒΟΛΗ

 ** Όσο είστε σε Κανονική Κατάσταση πατήστε  i  για να παρεμβάλλετε κείμενο. **

  1. Μετακινείστε τον δρομέα μέχρι την πρώτη γραμμή παρακάτω σημειωμένη με --->.

  2. Για να κάνετε την πρώτη γραμμή ίδια με την δεύτερη, μετακινείστε τον
     δρομέα πάνω στον πρώτο χαρακτήρα ΜΕΤΑ από όπου θα παρεμβληθεί το κείμενο.

  3. Πατήστε το  i  και πληκτρολογήστε τις απαραίτητες προσθήκες.

  4. Καθώς διορθώνετε κάθε λάθος πατήστε <ESC> για να επιστρέψετε στην
     Κανονική Κατάσταση. Επαναλάβετε τα βήματα 2 μέχρι 4 για να διορθώσετε
     την πρόταση.

---> There is text misng this .
---> There is some text missing from this line.

  5. Όταν είστε άνετοι με την παρεμβολή κειμένου μετακινηθείτε στην
     παρακάτω περίληψη.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       ΜΑΘΗΜΑ 1 ΠΕΡΙΛΗΨΗ


  1. Ο δρομέας κινείται χρησιμοποιώντας είτε τα πλήκτρα δρομέα ή τα hjkl.
	 h (αριστέρα)	j (κάτω)	k (πάνω)	l (δεξιά)

  2. Για να μπείτε στον Vim (από την προτροπή %) γράψτε:  vim ΑΡΧΕΙΟ <ENTER>

  3. Για να βγείτε γράψτε:  <ESC>   :q!   <ENTER>   για απόρριψη των αλλαγών.
		 Ή γράψτε:  <ESC>   :wq   <ENTER>   για αποθήκευση των αλλαγών.

  4. Για να διαγράψετε έναν χαρακτήρα κάτω από τον δρομέα σε
     Κανονική Κατάσταση πατήστε:  x

  5. Για να εισάγετε κείμενο στον δρομέα όσο είστε σε Κανονική Κατάσταση γράψτε:
	 i     πληκτρολογήστε το κείμενο	<ESC>

ΣΗΜΕΙΩΣΗ: Πατώντας <ESC> θα τοποθετηθείτε στην Κανονική Κατάσταση ή θα
	  ακυρώσετε μία ανεπιθύμητη και μερικώς ολοκληρωμένη εντολή.

Τώρα συνεχίστε με το Μάθημα 2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Μάθημα 2.1: ΕΝΤΟΛΕΣ ΔΙΑΓΡΑΦΗΣ

	 ** Γράψτε  dw  για να διαγράψετε μέχρι το τέλος μίας λέξης. **

  1. Πατήστε  <ESC>  για να βεβαιωθείτε ότι είστε στην Κανονική Κατάσταση.

  2. Μετακινείστε τον δρομέα στην παρακάτω γραμμή σημειωμένη με --->.

  3. Πηγαίνετε τον δρομέα στην αρχή της λέξης που πρέπει να διαγραφεί.

  4. Γράψτε  dw  για να κάνετε την λέξη να εξαφανιστεί.

ΣΗΜΕΙΩΣΗ: Τα γράμματα dw θα εμφανιστούν στην τελευταία γραμμή της οθόνης όσο
	  τα πληκτρολογείτε. Αν γράψατε κάτι λάθος, πατήστε  <ESC>  και
	  ξεκινήστε από την αρχή.

---> There are a some words fun that don't belong paper in this sentence.

  5. Επαναλάβετε τα βήματα 3 και 4 μέχρι η πρόταση να είναι σωστή και
     πηγαίνετε στο Μάθημα 2.2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Μάθημα 2.2: ΠΕΡΙΣΣΟΤΕΡΕΣ ΕΝΤΟΛΕΣ ΔΙΑΓΡΑΦΗΣ

    ** Πληκτρολογήστε  d$  για να διαγράψετε μέχρι το τέλος της γραμμής. **

  1. Πατήστε  <ESC>  για να βεβαιωθείτε ότι είστε στην Κανονική Κατάσταση.

  2. Μετακινείστε τον δρομέα στην παρακάτω γραμμή σημειωμένη με --->.

  3. Μετακινείστε τον δρομέα στο τέλος της σωστής γραμμής (ΜΕΤΑ την πρώτη . ).

  4. Πατήστε   d$   για να διαγράψετε μέχρι το τέλος της γραμμής.

---> Somebody typed the end of this line twice. end of this line twice.

  5. Πηγαίνετε στο Μάθημα 2.3 για να καταλάβετε τι συμβαίνει.







~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Μάθημα 2.3: ΠΕΡΙ ΕΝΤΟΛΩΝ ΚΑΙ ΑΝΤΙΚΕΙΜΕΝΩΝ


Η μορφή της εντολής διαγραφής  d  είναι ως εξής:

	 [αριθμός]   d   αντικείμενο	Ή	d   [αριθμός]   αντικείμενο
  Όπου:
    αριθμός - πόσες φορές θα εκτελεστεί η εντολή (προαιρετικό, εξ' ορισμού=1).
    d - η εντολή της διαγραφής.
    αντικείμενο - πάνω σε τι θα λειτουργήσει η εντολή (παρακάτω λίστα).

  Μία μικρή λίστα από αντικείμενα:
    w - από τον δρομέα μέχρι το τέλος της λέξης, περιλαμβάνοντας το διάστημα.
    e - από τον δρομέα μέχρι το τέλος της λέξης, ΧΩΡΙΣ το διάστημα.
    $ - από τον δρομέα μέχρι το τέλος της γραμμής.

ΣΗΜΕΙΩΣΗ:  Για τους τύπους της περιπέτειας, πατώντας απλώς το αντικείμενο όσο
	   είστε στην Κανονική Κατάσταση χωρίς κάποια εντολή θα μετακινήσετε
	   τον δρομέα όπως καθορίζεται στην λίστα αντικειμένων.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	       Μάθημα 2.4: ΜΙΑ ΕΞΑΙΡΕΣΗ ΣΤΗΝ 'ΕΝΤΟΛΗ-ΑΝΤΙΚΕΙΜΕΝΟ'

	   ** Πληκτρολογήστε  dd  για να διαγράψετε όλη τη γραμμή. **

  Εξαιτίας της συχνότητας της διαγραφής ολόκληρης γραμμής, οι σχεδιαστές
  του Vim αποφάσισαν ότι θα ήταν ευκολότερο να γράφετε απλώς δύο d στη
  σειρά για να διαγράψετε μία γραμμή.

  1. Μετακινείστε τον δρομέα στη δεύτερη γραμμή της παρακάτω φράσης.
  2. Γράψτε  dd  για να διαγράψετε τη γραμμή.
  3. Τώρα μετακινηθείτε στην τέταρτη γραμμή.
  4. Γράψτε  2dd  (θυμηθείτε  αριθμός-εντολή-αντικείμενο) για να
     διαγράψετε δύο γραμμές.

      1)  Roses are red,
      2)  Mud is fun,
      3)  Violets are blue,
      4)  I have a car,
      5)  Clocks tell time,
      6)  Sugar is sweet
      7)  And so are you.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Μάθημα 2.5: Η ΕΝΤΟΛΗ ΑΝΑΙΡΕΣΗΣ

	    ** Πατήστε  u  για να αναιρέσετε τις τελευταίες εντολές,
	       U για να διορθώσετε όλη τη γραμμή. **

  1. Μετακινείστε τον δρομέα στην παρακάτω γραμμή σημειωμένη με ---> και
     τοποθετήστε τον πάνω στο πρώτο λάθος.
  2. Πατήστε  x  για να διαγράψετε τον πρώτο ανεπιθύμητο χαρακτήρα.
  3. Τώρα πατήστε  u  για να αναιρέσετε την τελευταία εκτελεσμένη εντολή.
  4. Αυτή τη φορά διορθώστε όλα τα λάθη στη γραμμή χρησιμοποιώντας την εντολή x.
  5. Τώρα πατήστε ένα κεφαλαίο  U  για να επιστρέψετε τη γραμμή στην αρχική
     της κατάσταση.
  6. Τώρα πατήστε  u  μερικές φορές για να αναιρέσετε την  U  και
     προηγούμενες εντολές.
  7. Τώρα πατήστε CTRL-R (κρατώντας πατημένο το πλήκτρο CTRL καθώς πατάτε το R)
     μερικές φορές για να επαναφέρετε τις εντολές (αναίρεση των αναιρέσεων).

---> Fiix the errors oon thhis line and reeplace them witth undo.

  8. Αυτές είναι πολύ χρήσιμες εντολές.  Τώρα πηγαίνετε στην
     Περίληψη του Μαθήματος 2.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       ΜΑΘΗΜΑ 2 ΠΕΡΙΛΗΨΗ


  1. Για να διαγράψετε από τον δρομέα μέχρι το τέλος λέξης γράψτε:      dw

  2. Για να διαγράψετε από τον δρομέα μέχρι το τέλος γραμμής γράψτε:    d$

  3. Για να διαγράψετε ολόκληρη τη γραμμή γράψτε:    dd

  4. Η μορφή για μία εντολή στην Κανονική Κατάσταση είναι:

      [αριθμός]   εντολή   αντικείμενο    Ή    εντολή   [αριθμός]   αντικείμενο
     όπου:
       αριθμός - πόσες φορές να επαναληφθεί η εντολή
       εντολή - τι να γίνει, όπως η  d  για διαγραφή
       αντικείμενο - πάνω σε τι να ενεργήσει η εντολή, όπως  w  (λέξη),
		     $ (τέλος της γραμμής), κτλ.

  5. Για να αναιρέσετε προηγούμενες ενέργειες, πατήστε:        u   (πεζό u)
     Για να αναιρέσετε όλες τις αλλαγές στη γραμμή, πατήστε:  U  (κεφαλαίο U)
     Για να αναιρέσετε τις αναιρέσεις, πατήστε:               CTRL-R

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Μάθημα 3.1: Η ΕΝΤΟΛΗ ΤΟΠΟΘΕΤΗΣΗΣ


  ** Πατήστε  p  για να τοποθετήσετε την τελευταία διαγραφή μετά τον δρομέα.  **

  1. Μετακινείστε τον δρομέα στην πρώτη γραμμή της παρακάτω ομάδας.

  2. Πατήστε  dd  για να διαγράψετε τη γραμμή και να την αποθηκεύσετε σε
     προσωρινή μνήμη του Vim.

  3. Μετακινείστε τον δρομέα στη γραμμή ΠΑΝΩ από εκεί που θα πρέπει να πάει
     η διαγραμμένη γραμμή.

  4. Όσο είστε σε Κανονική Κατάσταση, πατήστε  p  για να βάλετε τη γραμμή.

  5. Επαναλάβετε τα βήματα 2 έως 4 για να βάλετε όλες τις γραμμές στη
     σωστή σειρά.

     d) Can you learn too?
     b) Violets are blue,
     c) Intelligence is learned,
     a) Roses are red,

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		      Μάθημα 3.2: Η ΕΝΤΟΛΗ ΑΝΤΙΚΑΤΑΣΤΑΣΗΣ


    ** Πατήστε  r  και χαρακτήρα για να αλλάξετε αυτόν που είναι
       κάτω από τον δρομέα. **

  1. Μετακινείστε τον δρομέα στην πρώτη γραμμή παρακάτω σημειωμένη με --->.

  2. Μετακινείστε τον δρομέα έτσι ώστε να είναι πάνω στο πρώτο λάθος.

  3. Πατήστε  r  και μετά τον χαρακτήρα ο οποίος διορθώνει το λάθος.

  4. Επαναλάβετε τα βήματα 2 και 3 μέχρι να είναι σωστή η πρώτη γραμμή.

--->  Whan this lime was tuoed in, someone presswd some wrojg keys!
--->  When this line was typed in, someone pressed some wrong keys!

  5. Τώρα πηγαίνετε στο Μάθημα 3.2.

ΣΗΜΕΙΩΣΗ: Να θυμάστε ότι πρέπει να μαθαίνετε με τη χρήση, και όχι με
	  την απομνημόνευση.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Μάθημα 3.3: Η ΕΝΤΟΛΗ ΑΛΛΑΓΗΣ

	   ** Για να αλλάξετε τμήμα ή όλη τη λέξη, πατήστε  cw  . **

  1. Μετακινείστε τον δρομέα στην πρώτη γραμμή παρακάτω σημειωμένη με --->.

  2. Τοποθετήστε τον δρομέα πάνω στο u της λέξης lubw.

  3. Πατήστε  cw  και τη σωστή λέξη (στην περίπτωση αυτή, γράψτε  'ine'.)

  4. Πατήστε <ESC> και πηγαίνετε στο επόμενο λάθος (στον πρώτο
     χαρακτήρα προς αλλαγή).

  5. Επαναλάβετε τα βήματα 3 και 4 μέχρις ότου η πρώτη πρόταση να είναι
     ίδια με τη δεύτερη.

---> This lubw has a few wptfd that mrrf changing usf the change command.
---> This line has a few words that need changing using the change command.

Παρατηρείστε ότι η  cw  όχι μόνο αντικαθιστάει τη λέξη, αλλά σας εισάγει
επίσης σε παρεμβολή.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Μάθημα 3.4: ΠΕΡΙΣΣΟΤΕΡΕΣ ΑΛΛΑΓΕΣ ΜΕ c


  ** Η εντολή αλλαγής χρησιμοποιείται με τα ίδια αντικείμενα της διαγραφής. **


  1. Η εντολή αλλαγής δουλεύει με τον ίδιο τρόπο όπως η διαγραφή. Η μορφή είναι:

       [αριθμός]   c   αντικείμενο     Ή     c   [αριθμός]   αντικείμενο

  2. Τα αντικείμενα είναι πάλι τα ίδια, όπως w (λέξη), $ (τέλος γραμμής), κτλ.

  3. Μετακινηθείτε στην πρώτη γραμμή παρακάτω σημειωμένη με --->.

  4. Μετακινείστε τον δρομέα στο πρώτο λάθος.

  5. Γράψτε  c$  για να κάνετε το υπόλοιπο της γραμμής ίδιο με τη δεύτερη
     και πατήστε <ESC>.

---> The end of this line needs some help to make it like the second.
---> The end of this line needs to be corrected using the  c$  command.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       ΜΑΘΗΜΑ 3 ΠΕΡΙΛΗΨΗ


  1. Για να τοποθετήσετε κείμενο που μόλις έχει διαγραφεί, πατήστε  p .
     Αυτό τοποθετεί το διαγραμμένο κείμενο ΜΕΤΑ τον δρομέα (αν διαγράφτηκε
     γραμμή θα πάει μετά στη γραμμή κάτω από τον δρομέα.

  2. Για να αντικαταστήσετε τον χαρακτήρα κάτω από τον δρομέα, πατήστε  r
     και μετά τον χαρακτήρα που θα αντικαταστήσει τον αρχικό.

  3. Η εντολή αλλαγής σας επιτρέπει να αλλάξετε το καθορισμένο αντικείμενο
     από τον δρομέα μέχρι το τέλος του αντικείμενο. Π.χ. γράψτε  cw  για να
     αλλάξετε από τον δρομέα μέχρι το τέλος της λέξης, c$ για να αλλάξετε
     μέχρι το τέλος γραμμής.

  4. Η μορφή για την αλλαγή είναι:

	 [αριθμός]   c   αντικείμενο     Ή     c   [αριθμός]   αντικείμενο

Τώρα συνεχίστε με το επόμενο μάθημα.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Μάθημα 4.1: ΘΕΣΗ ΚΑΙ ΚΑΤΑΣΤΑΣΗ ΑΡΧΕΙΟΥ


 ** Πατήστε CTRL-g για να εμφανιστεί η θέση σας στο αρχείο και η κατάστασή του.
    Πατήστε SHIFT-G για να πάτε σε μία γραμμή στο αρχείο. **

  Σημείωση: Διαβάστε ολόκληρο το μάθημα πριν εκτελέσετε κάποιο από τα βήματα!!

  1. Κρατήστε πατημένο το πλήκτρο Ctrl και πατήστε  g . Μία γραμμή κατάστασης
     θα εμφανιστεί στο κάτω μέρος της σελίδας με το όνομα αρχείου και τη
     γραμμή που είστε. Θυμηθείτε τον αριθμό γραμμής για το Βήμα 3.

  2. Πατήστε shift-G για να μετακινηθείτε στο τέλος του αρχείου.

  3. Πατήστε τον αριθμό της γραμμής που ήσασταν και μετά shift-G. Αυτό θα
     σας επιστρέψει στη γραμμή που ήσασταν πριν πατήσετε για πρώτη φορά Ctrl-g.
     (Όταν πληκτρολογείτε τους αριθμούς, ΔΕΝ θα εμφανίζονται στην οθόνη).

  4. Αν νοιώθετε σίγουρος για αυτό, εκτελέστε τα βήματα 1 έως 3.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Μάθημα 4.2: Η ΕΝΤΟΛΗ ΑΝΑΖΗΤΗΣΗΣ


	  ** Πατήστε   /   ακολουθούμενο από τη φράση που ψάχνετε. **

  1. Σε Κανονική Κατάσταση πατήστε τον χαρακτήρα  / . Παρατηρήστε ότι αυτός και
     ο δρομέας εμφανίζονται στο κάτω μέρος της οθόνης όπως με την εντολή  : .

  2. Τώρα γράψτε 'errroor' <ENTER>. Αυτή είναι η λέξη που θέλετε να ψάξετε.

  3. Για να ψάξετε ξανά για την ίδια φράση, πατήστε απλώς  n .
     Για να ψάξετε την ίδια φράση στην αντίθετη κατεύθυνση, πατήστε  Shift-N .

  4. Αν θέλετε να ψάξετε για μία φράση προς τα πίσω, χρησιμοποιήστε την εντολή  ?  αντί της  / .

---> Όταν η αναζήτηση φτάσει στο τέλος του αρχείου θα συνεχίσει από την αρχή.

  "errroor" is not the way to spell error;  errroor is an error.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Μάθημα 4.3: ΕΥΡΕΣΗ ΤΑΙΡΙΑΣΤΩΝ ΠΑΡΕΝΘΕΣΕΩΝ


	  ** Πατήστε   %   για να βρείτε την αντίστοιχη ), ], ή } . **

  1. Τοποθετήστε τον δρομέα σε κάποια (, [, ή { στην παρακάτω γραμμή
     σημειωμένη με --->.

  2. Τώρα πατήστε τον χαρακτήρα  % .

  3. Ο δρομέας θα πρέπει να είναι στην αντίστοιχη παρένθεση ή αγκύλη.

  4. Πατήστε  %  για να μετακινήσετε τον δρομέα πίσω στην πρώτη αγκύλη
    (του ζευγαριού).

---> This ( is a test line with ('s, ['s ] and {'s } in it. ))

ΣΗΜΕΙΩΣΗ: Αυτό είναι πολύ χρήσιμο στην αποσφαλμάτωση ενός προγράμματος
	  με μη ταιριαστές παρενθέσεις!



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		    Μάθημα 4.4: ΕΝΑΣ ΤΡΟΠΟΣ ΓΙΑ ΑΛΛΑΓΗ ΛΑΘΩΝ


       ** Γράψτε  :s/old/new/g  για να αλλάξετε το 'new' με το 'old'. **

  1. Μετακινείστε τον δρομέα στην παρακάτω γραμμή σημειωμένη με --->.

  2. Γράψτε  :s/thee/the <ENTER> . Σημειώστε ότι αυτή η εντολή αλλάζει μόνο
     την πρώτη εμφάνιση στη γραμμή.

  3. Τώρα γράψτε   :s/thee/the/g    εννοώντας γενική αντικατάσταση στη
     γραμμή. Αυτό αλλάζει όλες τις εμφανίσεις επί της γραμμής.

---> thee best time to see thee flowers is in thee spring.

  4. Για να αλλάξετε κάθε εμφάνιση μίας συμβολοσειράς μεταξύ δύο γραμμών,
     γράψτε   :#,#s/old/new/g   όπου #,# οι αριθμοί των δύο γραμμών.
     Γράψτε   :%s/old/new/g     για να αλλάξετε κάθε εμφάνιση σε όλο το αρχείο.




~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       ΜΑΘΗΜΑ 4 ΠΕΡΙΛΗΨΗ


  1. Το  Ctrl-g  εμφανίζει τη θέση σας στο αρχείο και την κατάστασή του.
     Το  Shift-G  πηγαίνει στο τέλος του αρχείου. Ένας αριθμός γραμμής
     ακολουθούμενος από  Shift-G  πηγαίνει σε εκείνη τη γραμμή.

  2. Γράφοντας  /  ακολουθούμενο από μία φράση ψάχνει προς τα ΜΠΡΟΣΤΑ για
     τη φράση. Γράφοντας  ?  ακολουθούμενο από μία φράση ψάχνει προς τα ΠΙΣΩ
     για τη φράση. Μετά από μία αναζήτηση πατήστε  n  για να βρείτε την
     επόμενη εμφάνιση προς την ίδια κατεύθυνση ή  Shift-N  για να ψάξετε
     προς την αντίθετη κατεύθυνση.

  3. Πατώντας  %  όσο ο δρομέας είναι πάνω σε μία (,),[,],{, ή }  εντοπίζει
     το αντίστοιχο ταίρι του ζευγαριού.

  4. Για αντικατάσταση με new του πρώτου old στη γραμμή γράψτε  :s/old/new
     Για αντικατάσταση με new όλων των 'old' στη γραμμή γράψτε  :s/old/new/g
     Για αντικατάσταση φράσεων μεταξύ δύο # γραμμών γράψτε      :#,#s/old/new/g
     Για αντικατάσταση όλων των εμφανίσεων στο αρχείο γράψτε    :%s/old/new/g
     Για ερώτηση επιβεβαίωσης κάθε φορά προσθέστε ένα 'c'       "%s/old/new/gc

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Μάθημα 5.1: ΠΩΣ ΕΚΤΕΛΩ ΜΙΑ ΕΞΩΤΕΡΙΚΗ ΕΝΤΟΛΗ


** Γράψτε  :!  ακολουθούμενο από μία εξωτερική εντολή για να την εκτελέσετε. **

  1. Πατήστε την οικεία εντολή  :  για να θέσετε τον δρομέα στο κάτω μέρος
     της οθόνης. Αυτό σας επιτρέπει να δώσετε μία εντολή.

  2. Τώρα πατήστε  το  !  (θαυμαστικό). Αυτό σας επιτρέπει να εκτελέσετε
     οποιαδήποτε εξωτερική εντολή του φλοιού.

  3. Σαν παράδειγμα γράψτε  ls  μετά από το ! και πατήστε <ENTER>. Αυτό θα
     σας εμφανίσει μία λίστα του καταλόγου σας, ακριβώς σαν να ήσασταν στην
     προτροπή του φλοιού. Ή χρησιμοποιήστε  :!dir  αν το ls δεν δουλεύει.

---> Σημείωση: Είναι δυνατόν να εκτελέσετε οποιαδήποτε εξωτερική εντολή
     με αυτόν τον τρόπο.

---> Σημείωση: Όλες οι εντολές  :  πρέπει να τερματίζονται πατώντας το <ENTER>.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		 Μάθημα 5.2: ΠΕΡΙΣΣΟΤΕΡΑ ΠΕΡΙ ΕΓΓΡΑΦΗΣ ΑΡΧΕΙΩΝ


   ** Για να σώσετε τις αλλάγες που κάνατε στο αρχείο, γράψτε  :w ΑΡΧΕΙΟ.  **

  1. Γράψτε  :!dir  ή  :!ls  για να πάρετε μία λίστα του καταλόγου σας.
     Ήδη ξέρετε ότι πρέπει να πατήσετε <ENTER> μετά από αυτό.

  2. Διαλέξτε ένα όνομα αρχείου που δεν υπάρχει ακόμα, όπως το TEST.

  3. Τώρα γράψτε:  :w TEST  (όπου TEST είναι το όνομα αρχείου που διαλέξατε).

  4. Αυτό σώζει όλο το αρχείο (vim Tutor) με το όνομα TEST. Για να το
     επαληθεύσετε, γράψτε ξανά  :!dir για να δείτε τον κατάλογό σας.

---> Σημειώστε ότι αν βγαίνατε από τον Vim και μπαίνατε ξανά με το όνομα
     αρχείου TEST, το αρχείο θα ήταν ακριβές αντίγραφο του tutor όταν το σώσατε.

  5. Τώρα διαγράψτε το αρχείο γράφοντας (MS-DOS):      :!del TEST



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		     Μάθημα 5.3: ΕΠΙΛΕΚΤΙΚΗ ΕΝΤΟΛΗ ΕΓΓΡΑΦΗΣ


	  ** Για να σώσετε τμήμα του αρχείου, γράψτε  :#,# w ΑΡΧΕΙΟ **

  1. Άλλη μια φορά, γράψτε  :!dir  ή  :!ls  για να πάρετε μία λίστα από τον
     κατάλογό σας και διαλέξτε ένα κατάλληλο όνομα αρχείου όπως το TEST.

  2. Μετακινείστε τον δρομέα στο πάνω μέρος αυτής της σελίδας και πατήστε
     Ctrl-g  για να βρείτε τον αριθμό αυτής της γραμμής.
     ΝΑ ΘΥΜΑΣΤΕ ΑΥΤΟΝ ΤΟΝ ΑΡΙΘΜΟ!

  3. Τώρα πηγαίνετε στο κάτω μέρος της σελίδας και πατήστε  Ctrl-g  ξανά.
     ΝΑ ΘΥΜΑΣΤΕ ΚΑΙ ΑΥΤΟΝ ΤΟΝ ΑΡΙΘΜΟ!

  4. Για να σώσετε ΜΟΝΟ ένα τμήμα σε αρχείο, γράψτε   :#,# w TEST
     όπου #,# οι δύο αριθμοί που απομνημονεύσατε (πάνω,κάτω) και TEST το
     όνομα του αρχείου σας.

  5. Ξανά, δείτε ότι το αρχείο είναι εκεί με την  :!dir αλλά ΜΗΝ το διαγράψετε.


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  Μάθημα 5.4: ΑΝΑΚΤΩΝΤΑΣ ΚΑΙ ΕΝΩΝΟΝΤΑΣ ΑΡΧΕΙΑ


     ** Για να εισάγετε τα περιεχόμενα ενός αρχείου, γράψτε   :r ΑΡΧΕΙΟ **

  1. Γράψτε  :!dir  για να βεβαιωθείτε ότι το TEST υπάρχει από πριν.

  2. Τοποθετήστε τον δρομέα στο πάνω μέρος της σελίδας.

ΣΗΜΕΙΩΣΗ:  Αφότου εκτελέσετε το Βήμα 3 θα δείτε το Μάθημα 5.3.
	   Μετά κινηθείτε ΚΑΤΩ ξανά προς το μάθημα αυτό.

  3. Τώρα ανακτήστε το αρχείο σας TEST χρησιμοποιώντας την εντολή  :r TEST
     όπου TEST είναι το όνομα του αρχείου.

ΣΗΜΕΙΩΣΗ:  Το αρχείο που ανακτάτε τοποθετείται ξεκινώντας εκεί που βρίσκεται
	   ο δρομέας.

  4. Για να επαληθεύσετε ότι το αρχείο ανακτήθηκε, πίσω τον δρομέα και
     παρατηρήστε ότι υπάρχουν τώρα δύο αντίγραφα του Μαθήματος 5.3, το
     αρχικό και η έκδοση του αρχείου.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       ΜΑΘΗΜΑ 5 ΠΕΡΙΛΗΨΗ


  1.  :!εντολή  εκτελεί μία εξωτερική εντολή.

      Μερικά χρήσιμα παραδείγματα είναι (MS-DOS):
      :!dir            - εμφάνιση λίστας ενός καταλόγου.
      :!del ΑΡΧΕΙΟ     - διαγράφει το ΑΡΧΕΙΟ.

  2.  :w ΑΡΧΕΙΟ   γράφει το τρέχων αρχείο του Vim στο δίσκο με όνομα ΑΡΧΕΙΟ.

  3.  :#,#w ΑΡΧΕΙΟ   σώζει τις γραμμές από # μέχρι # στο ΑΡΧΕΙΟ.

  4.  :r ΑΡΧΕΙΟ  ανακτεί το αρχείο δίσκου ΑΡΧΕΙΟ και το παρεμβάλλει μέσα
      στο τρέχον αρχείο μετά από τη θέση του δρομέα.







~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Μάθημα 6.1: Η ΕΝΤΟΛΗ ΑΝΟΙΓΜΑΤΟΣ


      ** Πατήστε  o  για να ανοίξετε μία γραμμή κάτω από τον δρομέα και να
	 βρεθείτε σε Κατάσταση Κειμένου. **

  1. Μετακινείστε τον δρομέα στην παρακάτω γραμμή σημειωμένη με --->.

  2. Πατήστε  o (πεζό) για να ανοίξετε μία γραμμή ΚΑΤΩ από τον δρομέα και να
     βρεθείτε σε Κατάσταση Κειμένου.

  3. Τώρα αντιγράψτε τη σημειωμένη με ---> γραμμή  και πατήστε <ESC> για να
     βγείτε από την Κατάσταση Κειμένου.

---> After typing  o  the cursor is placed on the open line in Insert mode.

  4. Για να ανοίξετε μία γραμμή ΠΑΝΩ από τον δρομέα, πατήστε απλά ένα κεφαλαίο
     O, αντί για ένα πεζό  o.  Δοκιμάστε το στην παρακάτω γραμμή.
Ανοίγετε γραμμή πάνω από αυτήν πατώντας Shift-O όσο ο δρομέας είναι στη γραμμή



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			 Μάθημα 6.2: Η ΕΝΤΟΛΗ ΠΡΟΣΘΗΚΗΣ

	  ** Πατήστε   a   για να εισάγετε κείμενο ΜΕΤΑ τον δρομέα. **

  1. Μετακινείστε τον δρομέα στο τέλος της πρώτης γραμμής παρακάτω
     σημειωμένη με ---> πατώντας  $  στην Κανονική Κατάσταση.

  2. Πατήστε ένα  a  (πεζό) για να προσθέσετε κείμενο ΜΕΤΑ από τον χαρακτήρα
     που είναι κάτω από τον δρομέα.  (Το κεφαλαίο  A  προσθέτει στο τέλος
     της γραμμής).

Σημείωση: Αυτό αποφεύγει το πάτημα του  i , τον τελευταίο χαρακτήρα, το
	  κείμενο της εισαγωγής, <ESC>, δρομέα-δεξιά, και τέλος, x, μόνο και
	  μόνο για να προσθέσετε στο τέλος της γραμμής!

  3. Συμπληρώστε τώρα την πρώτη γραμμή. Σημειώστε επίσης ότι η προσθήκη είναι
     ακριβώς ίδια στην Κατάσταση Κειμένου με την Κατάσταση Εισαγωγής, εκτός
     από τη θέση που εισάγεται το κείμενο.

---> This line will allow you to practice
---> This line will allow you to practice appending text to the end of a line.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		   Μάθημα 6.3: ΑΛΛΗ ΕΚΔΟΣΗ ΤΗΣ ΑΝΤΙΚΑΤΑΣΤΑΣΗΣ


 ** Πατήστε κεφαλαίο  R  για να αλλάξετε περισσότερους από έναν χαρακτήρες. **

  1. Μετακινείστε τον δρομέα στην πρώτη γραμμή παρακάτω σημειωμένη με --->.

  2. Τοποθετήστε τον δρομέα στην αρχή της πρώτης λέξης που είναι διαφορετική
     από τη δεύτερη γραμμή σημειωμένη με ---> (η λέξη 'last').

  3. Πατήστε τώρα  R   και αλλάξτε το υπόλοιπο του κειμένου στην πρώτη γραμμή
     γράφοντας πάνω από το παλιό κείμενο ώστε να κάνετε την πρώτη γραμμή ίδια
     με τη δεύτερη.

---> To make the first line the same as the last on this page use the keys.
---> To make the first line the same as the second, type R and the new text.

  4. Σημειώστε ότι όταν πατάτε <ESC> για να βγείτε, παραμένει οποιοδήποτε
     αναλλοίωτο κείμενο.



~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			  Μάθημα 6.4: ΡΥΘΜΙΣΗ ΕΠΙΛΟΓΗΣ


   ** Ρυθμίστε μία επιλογή έτσι ώστε η αναζήτηση ή η αντικατάσταση να αγνοεί
      τη διαφορά πεζών-κεφαλαίων **

  1. Ψάξτε για 'ignore' εισάγοντας:
     /ignore
     Συνεχίστε αρκετές φορές πατώντας το πλήκτρο n.

  2. Θέστε την επιλογή 'ic' (Ignore case) γράφοντας:
     :set ic

  3. Ψάξτε τώρα ξανά για 'ignore' πατώντας: n
     Συνεχίστε την αναζήτηση μερικές ακόμα φορές πατώντας το πλήκτρο n

  4. Θέστε τις επιλογές 'hlsearch' και 'incsearch':
     :set hls is

  5. Εισάγετε τώρα ξανά την εντολή αναζήτησης, και δείτε τι συμβαίνει
     /ignore

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			       ΜΑΘΗΜΑ 6 ΠΕΡΙΛΗΨΗ


  1. Πατώντας  o  ανοίγει μία γραμμή ΚΑΤΩ από τον δρομέα και τοποθετεί τον
     δρομέα στην ανοιχτή γραμμή σε Κατάσταση Κειμένου.

  2. Πατήστε  a  για να εισάγετε κείμενο ΜΕΤΑ τον χαρακτήρα στον οποίο είναι
     ο δρομέας. Πατώντας κεφαλαίο  A  αυτόματα προσθέτει κείμενο στο τέλος
     της γραμμής.

  3. Πατώντας κεφαλαίο  R  εισέρχεται στην Κατάσταη Αντικατάστασης μέχρι να
     πατηθεί το <ESC> και να εξέλθει.

  4. Γράφοντας ":set xxx" ρυθμίζει την επιλογή "xxx".








~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		       ΜΑΘΗΜΑ 7: ON-LINE ΕΝΤΟΛΕΣ ΒΟΗΘΕΙΑΣ


		** Χρησιμοποιήστε το on-line σύστημα βοήθειας **

  Ο Vim έχει ένα περιεκτικό on-line σύστημα βοήθειας. Για να ξεκινήσει,
  δοκιμάστε κάποιο από τα τρία:
	- πατήστε το πλήκτρο <HELP> (αν έχετε κάποιο)
	- πατήστε το πλήκτρο <F1> (αν έχετε κάποιο)
	- γράψτε   :help <ENTER>

  Γράψτε  :q <ENTER>   για να κλείσετε το παράθυρο της βοήθειας.

  Μπορείτε να βρείτε βοήθεια πάνω σε κάθε αντικείμενο, δίνοντας μία παράμετρο
  στην εντολή ":help".  Δοκιμάστε αυτά (μην ξεχνάτε να πατάτε <ENTER>):

	:help w
	:help c_<T
	:help insert-index
	:help user-manual


~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		  ΜΑΘΗΜΑ 8: ΔΗΜΙΟΥΡΓΗΣΤΕ ΕΝΑ SCRIPT ΕΚΚΙΝΗΣΗΣ

		   ** Ενεργοποιήστε χαρακτηριστικά του Vim **

  Ο Vim έχει πολλά περισσότερα χαρακτηριστικά απ' ό,τι ο Vi, αλλά τα
  περισσότερα είναι αρχικά απενεργοποιημένα. Για να αρχίσετε να χρησιμοποιείτε
  περισσότερα χαρακτηριστικά πρέπει να φτιάξετε ένα αρχείο "vimrc".

  1. Αρχίστε διορθώνοντας το αρχείο "vimrc", αυτό εξαρτάται από το σύστημά σας:
	:edit ~/.vimrc            για Unix
	:edit ~/_vimrc            για MS-Windows

  2. Τώρα εισάγετε το κείμενο παραδείγματος για αρχείο "vimrc":
	:read $VIMRUNTIME/vimrc_example.vim

  3. Γράψτε το αρχείο με την:
	:write

  Την επόμενη φορά που θα ξεκινήσετε τον Vim θα χρησιμοποιήσει φωτισμό
  σύνταξης.  Μπορείτε να προσθέσετε όλες τις προτιμώμενες επιλογές σ' αυτό
  το αρχείο "vimrc".

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

  Εδώ ολοκληρώνεται το Vim Tutor. Σκοπός του ήταν να δώσει μία σύντομη
  περίληψη του συντάκτη Vim, τουλάχιστον τόση ώστε να σας επιτρέψει να
  χρησιμοποιήσετε τον συντάκτη αρκετά εύκολα. Απέχει πολύ από μία
  ολοκληρωμένη παρουσίαση καθώς ο Vim έχει πάρα πολλές εντολές. Διαβάστε
  κατόπιν το εγχειρίδιο χρήσης:
	":help user-manual".

  Για περαιτέρω διάβασμα και μελέτη, συστήνεται αυτό το βιβλίο:
	Vim - Vi Improved - by Steve Oualline
	Publisher: New Riders
	Το πρώτο βιβλίο πλήρως αφιερωμένο στον Vim.
	Ιδιαίτερα χρήσιμο για αρχάριους.
	Υπάρχουν πολλά παραδείγματα και εικόνες.
	Δείτε την http://iccf-holland.org/click5.html

  Αυτό το βιβλίο είναι παλιότερο και περισσότερο για τον Vi παρά για τον Vim,
  αλλά επίσης συνιστώμενο:
	Learning the Vi Editor - by Linda Lamb
	Publisher: O'Reilly & Associates Inc.
	Είναι ένα καλό βιβλίο για να μάθετε σχεδόν τα πάντα που θέλετε
	να κάνετε με τον Vi.
	Η έκτη έκδοση περιέχει ακόμα πληροφορίες για τον Vim.

  Αυτή η περιήγηση γράφτηκε από τους Michael C. Pierce και Robert K. Ware,
  Colorado School of Mines χρησιμοποιώντας ιδέες από τον Charles Smith,
  Colorado State University.  E-mail: <EMAIL>.

  Προσαρμογή για τον Vim από τον Bram Moolenaar.

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
