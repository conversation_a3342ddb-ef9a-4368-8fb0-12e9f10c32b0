" Vim Keymap file for French standard keyboard layout (without AltGr keys as
" they don't work reliably in all version of vim)

" Maintainer:   <PERSON><PERSON><PERSON> <<EMAIL>>
" Last Changed: 2020-07-18

scriptencoding utf-8

let b:keymap_name = "fr"

loadkeymap
1	& ASCII AMPERSAND
2	é LATIN SMALL LETTER E WITH ACUTE
3	" ASCII DOUBLE QUOTES
4	' ASCII SINGLE QUOTE
5	( ASCII LEFT PARENTHESIS
6	- ASCII MINUS
7	è LATIN SMALL LETTER E WITH GRAVE
8	_ ASCII UNDERSCORE
9	ç LATIN SMALL LETTER C WITH CEDILLA
0	à LATIN SMALL LETTER A WITH GRAVE
-	) ASCII RIGHT PARENTHESIS
! 1 ASCII DIGIT 1
@ 2 ASCII DIGIT 2
# 3 ASCII DIGIT 3
$ 4 ASCII DIGIT 4
% 5 ASCII DIGIT 5
^ 6 ASCII DIGIT 6
& 7 ASCII DIGIT 7
* 8 ASCII DIGIT 8
( 9 ASCII DIGIT 9
) 0 ASCII DIGIT 0
_ ° DEGREE SIGN
q	a LATIN SMALL LETTER A
a	q LATIN SMALL LETTER Q
z	w LATIN SMALL LETTER W
w	z LATIN SMALL LETTER Z
Q	A LATIN CAPITAL LETTER A
A	Q LATIN CAPITAL LETTER Q
Z	W LATIN CAPITAL LETTER W
W	Z LATIN CAPITAL LETTER Z
[[ ^ ASCII CIRCUMFLEX
[q â LATIN SMALL LETTER A WITH CIRCUMFLEX
[e ê LATIN SMALL LETTER E WITH CIRCUMFLEX
[u û LATIN SMALL LETTER U WITH CIRCUMFLEX
[i î LATIN SMALL LETTER I WITH CIRCUMFLEX
[o ô LATIN SMALL LETTER O WITH CIRCUMFLEX
[Q Â LATIN CAPITAL LETTER A WITH CIRCUMFLEX
[E Ê LATIN CAPITAL LETTER E WITH CIRCUMFLEX
[U Û LATIN CAPITAL LETTER U WITH CIRCUMFLEX
[I Î LATIN CAPITAL LETTER I WITH CIRCUMFLEX
[O Ô LATIN CAPITAL LETTER O WITH CIRCUMFLEX
{{ ¨ UMLAUT
{q ä LATIN SMALL LETTER A WITH UMLAUT
{e ë LATIN SMALL LETTER E WITH UMLAUT
{y ÿ LATIN SMALL LETTER Y WITH UMLAUT
{u ü LATIN SMALL LETTER U WITH UMLAUT
{i ï LATIN SMALL LETTER I WITH UMLAUT
{o ö LATIN SMALL LETTER O WITH UMLAUT
{Q Ä LATIN CAPITAL LETTER A WITH UMLAUT
{E Ë LATIN CAPITAL LETTER E WITH UMLAUT
{Y Ÿ LATIN CAPITAL LETTER Y WITH UMLAUT
{U Ü LATIN CAPITAL LETTER U WITH UMLAUT
{I Ï LATIN CAPITAL LETTER I WITH UMLAUT
{O Ö LATIN CAPITAL LETTER O WITH UMLAUT
] $ ASCII GRAVE
} £ POUND SIGN
;	m LATIN SMALL LETTER M
: M LATIN CAPITAL LETTER M
'	ù LATIN SMALL LETTER U WITH GRAVE
\" % ASCII PERCENT
\\ * ASCII ASTERISK
| µ GREEK LETTER MU
m , ASCII COMMA
M ? ASCII QUESTION MARK
, ; ASCII SEMICOLON 
< . ASCII DOT
. : ASCII COLON 
> / ASCII SLASH
/ ! ASCII EXCLAMATION MARK
? §  SECTION SIGN
