## Syntax highlighting for RPM spec files.

syntax spec "\.spec(\.[^/]+)?$"
comment "#"

# Main tags.
color brightblue "\<(Name|Version|Release|Summary|Group|URL|Url|Epoch|Icon|Serial)[[:space:]]*:"
color brightblue "\<(BuildArch(itectures)?|Exclusive(Arch|OS)|Exclude(Arch|OS))[[:space:]]*:"
color brightblue "\<(Provides|Requires(\(.*\))?|Obsoletes|Conflicts|Recommends|Suggests|Supplements|Enhances|PreReq)[[:space:]]*:"
color brightblue "\<(BuildRoot|BuildRequires|BuildConflicts|Prefix|RemovePathPostfixes)[[:space:]]*:"
color brightblue "\<(AutoReq|AutoProv|AutoReqProv)[[:space:]]*:"
color brightblue "\<(License|Copyright|Distribution|Vendor|Packager)[[:space:]]*:"
color brightblue "\<((Source|Patch)[0-9]*|Nosource|Nopatch)[[:space:]]*:"
# Architectures.
color brightred "\<(i386|i486|i586|i686|athlon|ia64|alpha|alphaev5|alphaev56|alphapca56|alphaev6|alphaev67|sparc|sparcv9|sparc64armv3l|armv4b|armv4lm|ips|mipsel|ppc|ppciseries|ppcpseries|ppc64|m68k|m68kmint|Sgi|rs6000|i370|s390x?|noarch)\>"
# Architecture and OS conditionals.
color brightred "%(ifarch|elifarch|ifnarch|ifos|elifos|ifnos)\>"
# %* strings.
color green "%([A-Z_a-z_0-9_]*)"
color magenta "%_([A-Z_a-z_0-9_]*)"
color yellow start="%__" end="\ "
color magenta start="%\{" end="\}"
color yellow start="%\{__" end="\}"
# Sections.
color red "^%((prep|build|install|check|clean)$|(description|files|package|changelog)\>)"
color red "^%((pre|post)(trans|un)?|trigger(prein|in|un|postun)?)\>"
color red "^%(trans)?filetrigger(in|un|postun)\>"
color red "^%(sourcelist|patchlist|generate_buildrequires|verifyscript)\>"
# Conditionals and defines.
color brightred "%(if|elif|else|endif|define|global|undefine)\>"

# Comments.
color cyan "(#|%dnl[[:space:]]).*"
# Special case: "# norootforbuild" is handled as main tag.
color brightblue "^# norootforbuild"
# For %changelog entries: first the author, then just the date.
color yellow "^\* .+>"
color brightyellow "^\* [[:alnum:] ]+ [0-9]{4} "

# Trailing whitespace.
color ,green "[[:space:]]+$"
