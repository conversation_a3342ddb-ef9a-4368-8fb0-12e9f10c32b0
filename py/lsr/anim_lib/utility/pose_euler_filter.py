import maya.cmds as cmds
from lsr.maya.nodezoo.node import Node


def apply_euler_filter(data, ctrls_mapping, start_frame):
    """ Apply euler filter """
    cmds.currentTime(start_frame)
    collect_value_dict = {}
    for key_dict in data:
        json_obj = key_dict["name"]
        if not json_obj in ctrls_mapping.keys():
            continue

        attr_data = key_dict.get("attrs", dict())
        rx = attr_data.get('rotateX', None)
        ry = attr_data.get('rotateY', None)
        rz = attr_data.get('rotateZ', None)

        rx = rx["v"] if rx is not None else None
        ry = ry["v"] if ry is not None else None
        rz = rz["v"] if rz is not None else None

        if rx is not None and ry is not None and rz is not None:
            scene_ctrl = Node(ctrls_mapping[json_obj])
            ctrl_rx = scene_ctrl.rx.value
            ctrl_ry = scene_ctrl.ry.value
            ctrl_rz = scene_ctrl.rz.value
            collect_value_dict[json_obj] = [ctrl_rx, ctrl_ry, ctrl_rz, rx, ry, rz]

    euler_data = []
    for key, value in collect_value_dict.items():
        euler_data.extend(value)

    correct_list = cmds.dotnetEulerFilterCmd(data=euler_data, no=len(collect_value_dict))
    if not correct_list:
        return RuntimeError("Euler filter failed: {}".format(euler_data))

    correct_data_dict = {}
    i = 0
    for key, value in collect_value_dict.items():
        correct_data_dict[key] = correct_list[i + 3:i + 6]
        i += 6

    for key, value in correct_data_dict.items():
        for key_dict in data:
            if key == key_dict["name"]:
                attr_data = key_dict.get("attrs", dict())
                attr_data["rotateX"]["v"] = value[0]
                attr_data["rotateY"]["v"] = value[1]
                attr_data["rotateZ"]["v"] = value[2]
