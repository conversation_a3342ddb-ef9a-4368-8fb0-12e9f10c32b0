#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
common ui line divider
"""


from Qt import QtWidgets, QtCore


class LSRDivider(QtWidgets.QWidget):
    """
    modified mdivider from
    https://github.com/phenom-films/dayu_widgets
    by <PERSON><PERSON>
    """
    _alignment_map = {
        QtCore.Qt.AlignCenter: 50,
        QtCore.Qt.AlignLeft: 20,
        QtCore.Qt.AlignRight: 80,
    }

    def __init__(self, text='', orientation=QtCore.Qt.Horizontal, alignment=QtCore.Qt.AlignCenter, parent=None):
        super(LSRDivider, self).__init__(parent)
        self._orient = orientation
        self._text_label = QtWidgets.QLabel()
        self._left_frame = QtWidgets.QFrame()
        self._right_frame = QtWidgets.QFrame()
        self._main_lay = QtWidgets.QHBoxLayout()
        self._main_lay.setContentsMargins(0, 0, 0, 0)
        self._main_lay.setSpacing(0)
        self._main_lay.addWidget(self._left_frame)
        self._main_lay.addWidget(self._text_label)
        self._main_lay.addWidget(self._right_frame)
        self.setLayout(self._main_lay)

        if orientation == QtCore.Qt.Horizontal:
            self._left_frame.setFrameShape(QtWidgets.QFrame.HLine)
            self._left_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
            self._right_frame.setFrameShape(QtWidgets.QFrame.HLine)
            self._right_frame.setFrameShadow(QtWidgets.QFrame.Sunken)
        else:
            self._text_label.setVisible(False)
            self._right_frame.setVisible(False)
            self._left_frame.setFrameShape(QtWidgets.QFrame.VLine)
            self._left_frame.setFrameShadow(QtWidgets.QFrame.Plain)
            self.setFixedWidth(2)
        self._main_lay.setStretchFactor(self._left_frame,
                                        self._alignment_map.get(alignment, 50))
        self._main_lay.setStretchFactor(self._right_frame,
                                        100 - self._alignment_map.get(alignment, 50))
        self._text = None
        self.set_text(text)

    def set_dash_line(self, *args, **kwargs):
        self._right_frame.setLineWidth(0)
        self._right_frame.setMidLineWidth(0)
        self._left_frame.setLineWidth(0)
        self._left_frame.setMidLineWidth(0)

    def set_text(self, value, *args, **kwargs):
        """
        Set the divider's text.
        When text is empty, hide the text_label and right_frame to ensure the divider not has a gap.

        :param value: six.string_types
        :return: None
        """
        self._text = value
        self._text_label.setText(value)
        if self._orient == QtCore.Qt.Horizontal:
            self._text_label.setVisible(bool(value))
            self._right_frame.setVisible(bool(value))

    def get_text(self, *args, **kwargs):
        """
        Get current text
        :return: six.string_types
        """
        return self._text

    @classmethod
    def left(cls, text='', *args, **kwargs):
        """Create a horizontal divider with text at left."""
        return cls(text, alignment=QtCore.Qt.AlignLeft)

    @classmethod
    def right(cls, text='', *args, **kwargs):
        """Create a horizontal divider with text at right."""
        return cls(text, alignment=QtCore.Qt.AlignRight)

    @classmethod
    def center(cls, text='', *args, **kwargs):
        """Create a horizontal divider with text at center."""
        return cls(text, alignment=QtCore.Qt.AlignCenter)

    @classmethod
    def vertical(cls, *args, **kwargs):
        """Create a vertical divider"""
        return cls(orientation=QtCore.Qt.Vertical)

