"""
allow user to pick namespace during controller data apply stage
"""
# Studio package module imports
from Qt import QtWidgets, QtCore
from functools import partial

# Tool package module imports
from lsr.anim_lib.ui.sub_ui.base import AnimLibBaseSubUI
from lsr.anim_lib.utility.pyside import copy_maya_font_style
from lsr.anim_lib.ui.widget.listWidget import AnimLibListWidget


class MappingFixDialog(AnimLibBaseSubUI):

    def __init__(self, data_block, namespace_list, parent):
        super(MappingFixDialog, self).__init__(parent)
        self._data = data_block
        if namespace_list:
            namespace_list.sort()
            namespace_list += [""]
        self._scene_namespace = namespace_list
        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()

    def _init_layout(self, *args, **kwargs):
        super(MappingFixDialog, self)._init_layout()
        # create widget
        self.listWidget_missing = AnimLibListWidget("Missing", self._data[0])
        self.listWidget_found = AnimLibListWidget("Found", [d[1] for d in self._data[1]])
        if self._data[2]:
            current_ns = self._data[2][0].split("|")[0].rpartition(":")[0]
        else:
            current_ns = None
        self.listWidget_replace_namespace = AnimLibListWidget("Replace [%s]" % current_ns, self._data[2])
        self.continue_btn = QtWidgets.QPushButton("Continue")
        self.cancel_btn = QtWidgets.QPushButton("Cancel")
        self.lineEdit_name_replacer = QtWidgets.QLineEdit()
        self.combo_namespace_picker_replace = QtWidgets.QComboBox()
        self.combo_namespace_picker = QtWidgets.QComboBox()

        # layout
        hlayout_listwidget_missing = QtWidgets.QVBoxLayout()
        hlayout_listwidget_missing.setContentsMargins(0, 0, 0, 0)
        hlayout_listwidget_missing.addWidget(self.listWidget_missing)

        hlayout_listwidget_found = QtWidgets.QHBoxLayout()
        hlayout_listwidget_found.setContentsMargins(0, 0, 0, 0)
        hlayout_listwidget_found.addWidget(self.listWidget_found)

        self.listWidget_replace_namespace._layout.addWidget(self.combo_namespace_picker_replace)
        hlayout_listwidget_replace_ns = QtWidgets.QHBoxLayout()
        hlayout_listwidget_replace_ns.setContentsMargins(0, 0, 0, 0)
        hlayout_listwidget_replace_ns.addWidget(self.listWidget_replace_namespace)

        hlayout_ok_cancel_button = QtWidgets.QHBoxLayout()
        hlayout_ok_cancel_button.addStretch()
        hlayout_ok_cancel_button.addWidget(self.cancel_btn)
        hlayout_ok_cancel_button.addWidget(self.continue_btn)

        main_layout = QtWidgets.QVBoxLayout(self)
        grid_layout = QtWidgets.QGridLayout(self)
        grid_layout.addLayout(hlayout_listwidget_missing, 1, 1)
        grid_layout.addLayout(hlayout_listwidget_found, 1, 2)
        grid_layout.addLayout(hlayout_listwidget_replace_ns, 1, 3)
        main_layout.addLayout(grid_layout)
        main_layout.addLayout(hlayout_ok_cancel_button)

        self.setLayout(main_layout)

    def _customized_view(self, *args, **kwargs):
        super(MappingFixDialog, self)._customized_view()
        self.setWindowTitle("fix name mapping")
        self.setWindowModality(QtCore.Qt.WindowModal)

        self.combo_namespace_picker_replace.addItems(self._scene_namespace)
        self.combo_namespace_picker.addItems(self._scene_namespace)
        copy_maya_font_style(self.combo_namespace_picker_replace, size=9)
        copy_maya_font_style(self.combo_namespace_picker, size=9)
        self._hide_unused_pannel()

        self.continue_btn.setStyleSheet("background-color: green")

    def _hide_unused_pannel(self, *args, **kwargs):
        if not self._data[0]:
            self.listWidget_missing.setHidden(True)
        if not self._data[1]:
            self.listWidget_found.setHidden(True)
        if not self._data[2]:
            self.listWidget_replace_namespace.setHidden(True)

    def _do_signal_slot_connection(self, *args, **kwargs):
        super(MappingFixDialog, self)._do_signal_slot_connection()
        self.continue_btn.clicked.connect(partial(self.accept))
        self.cancel_btn.clicked.connect(partial(self.reject))

    def _replace_namespace_for_obj(self, old_name, ns, *args, **kwargs):
        final_name = ""
        for part in old_name.split("|"):
            if ":" in part:
                part = part.rpartition(":")[-1]
            final_name += "%s:%s|" % (ns, part)
        return final_name

    def _add_to_ui_input(self, target_ns, *args, **kwargs):
        # self._data : (missing_object, existed_object, replace_namespace_object)
        final_mapping = {}
        # for obj in self._data[0]:
        #   final_mapping[obj] = obj
        for obj in self._data[1]:
            final_mapping[obj[0]] = self._replace_namespace_for_obj(obj[0], target_ns)
        for obj in self._data[2]:
            final_mapping[obj] = self._replace_namespace_for_obj(obj, target_ns)
        return final_mapping

    def exec_(self, out_data, *args, **kwargs):
        result = super(MappingFixDialog, self).exec_()
        if result == QtWidgets.QDialog.Accepted:
            out_data.update(self._add_to_ui_input(self.combo_namespace_picker_replace.currentText()))
            return True
        else:
            return False
