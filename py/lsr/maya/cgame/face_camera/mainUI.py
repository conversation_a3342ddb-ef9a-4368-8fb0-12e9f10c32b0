#!/usr/bin/env ptuyhon
# -*- coding: utf-8 -*-
"""
@FileName : main.py
@Contact  : <EMAIL>
@Datetime : 2025/2/26 15:37
@Software : PyCharm
@Version  : 1.0
"""

import os
from functools import partial
# from Qt import QtWidgets, QtGui, QtCore
from PySide2 import QtWidgets, Qt<PERSON>ore, QtGui
from maya import cmds

from lsr.maya.cgame.face_camera import create_face_camera
from lsr.qt.core.base_main_window import get_window_class

# get the base main window class
base_class = get_window_class(app_name='Face Camera Tool v1.0')


class CameraWindow(base_class):
    """
    A window for creating and deleting face cameras.
    """
    _REUSE_SINGLETON = False
    clip_num = QtCore.Signal(str)

    def __init__(self):
        """ Creates and initializes this window. """
        super(<PERSON>Window, self).__init__(
            banner_widget=True, has_art=True, top=True,
            email_address='shengxz<PERSON>@tencent.com')
        self.resize(400, 200)
        self.update_ui()
        self.change_callback_id = None
        self._connect_slot()

    def setup_ui(self):
        """ Create ui"""
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)

        self.main_widget = QtWidgets.QWidget(self)
        self.main_lay = QtWidgets.QVBoxLayout(self.main_widget)
        self.main_lay.setContentsMargins(5, 5, 5, 5)
        self.setCentralWidget(self.main_widget)

        # Create button
        self.create_bt = QtWidgets.QPushButton('Create Camera', self)
        self.create_bt.setGeometry(50, 50, 200, 50)
        self.create_bt.setStyleSheet("background-color: green; color: white;")
        self.create_bt.setIcon(QtGui.QIcon('path/to/create_icon.png'))
        self.create_bt.setToolTip("Create Face Camera")

        # comb
        self.culumn_layout = QtWidgets.QHBoxLayout(self)
        self.culumn_layout.setContentsMargins(0, 0, 0, 0)
        self.culumn_layout.setSpacing(5)
        self.label = QtWidgets.QLabel("Camera List:", self)
        self.label.setStyleSheet("color: yellow; font-weight: bold;")
        self.culumn_layout.addWidget(self.label)
        self.cam_lw = QtWidgets.QListWidget(self)
        self.cam_lw.setVisible(False)
        self.label.setVisible(False)
        self.culumn_layout.addWidget(self.cam_lw)

        # Delete button
        self.delete_button = QtWidgets.QPushButton('Delete Camera', self)
        self.delete_button.setGeometry(50, 120, 200, 50)
        self.delete_button.setStyleSheet("background-color: red; color: white;")
        self.delete_button.setIcon(QtGui.QIcon('path/to/delete_icon.png'))
        self.delete_button.setToolTip("Delete Face Camera")

        self.main_lay.addWidget(self.create_bt)
        self.main_lay.addLayout(self.culumn_layout)
        self.main_lay.addWidget(self.delete_button)

    def _setToolTip(self, *args, **kwargs):
        """
        set widget tooltip
        Args:
            *args:
            **kwargs:

        Returns:

        """
        self.create_bt.setToolTip("Create Face Camera")
        self.delete_button.setToolTip("Delete Face Camera")

    def _connect_slot(self, *args, **kwargs):
        """"""
        self.create_bt.clicked.connect(partial(self.on_create_bt_clicked))
        self.delete_button.clicked.connect(partial(self.on_delete_bt_clicked))

    @QtCore.Slot()
    def on_create_bt_clicked(self, *args, **kwargs):
        """add bt clicked"""
        create_face_camera.create_face_camera()
        self.update_ui()

    @QtCore.Slot()
    def on_delete_bt_clicked(self, *args, **kwargs):
        """delete bt clicked"""

        concurrent_items = self.cam_lw.selectedItems()
        if concurrent_items:
            for i in concurrent_items:
                create_face_camera.delete_face_camera(i.text())

        elif self.cam_lw.count() == 0:
            msg = "No camera found."
            QtWidgets.QMessageBox.warning(self, "Warning", msg)
        else:
            msg = "Please select a camera to delete. \nOr select multiple cameras."
            QtWidgets.QMessageBox.warning(self, "Warning", msg)
        self.update_ui()

    def open_help(self):
        """
        Returns:
        """

    @QtCore.Slot()
    def pop_context_menu(self, *args, **kwargs):
        """Creates the right click context menu."""
        pass

    def save_settings(self):
        """Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(CameraWindow, self).save_settings()

        settings.beginGroup('ui_setting')

        settings.endGroup()

        settings.sync()
        return settings

    def load_settings(self):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(CameraWindow, self).load_settings()

        settings.beginGroup('ui_setting')

        settings.endGroup()

        return settings

    def update_ui(self, *args, **kwargs):
        """
        update ui
        Args:
        Returns:

        """
        cameras = create_face_camera.get_followHead_camera()
        if cameras and len(cameras) >= 1:
            self.cam_lw.clear()
            self.cam_lw.addItems(cameras)
            self.cam_lw.setVisible(True)
            self.label.setVisible(True)
            self.cam_lw.setSelectionMode(QtWidgets.QAbstractItemView.MultiSelection)

            # Alternate item colors
            for index in range(self.cam_lw.count()):
                item = self.cam_lw.item(index)
                if index % 2 == 0:

                    item.setForeground(QtGui.QColor('#87CEFA'))  # Light sky blue
                else:
                    item.setForeground(QtGui.QColor('#DDA0DD'))  # Plum

            self.cam_lw.setStyleSheet("QListWidget::item:selected { background-color: rgb(57, 67, 255); }")
        else:
            self.cam_lw.setVisible(False)
            self.label.setVisible(False)

    def _set_current_camera(self, *args, **kwargs):
        """
        set current camera
        Args:
            *args:
            **kwargs:

        Returns:

        """
        sel = cmds.ls(selection=True)

    def closeEvent(self, event):
        """Stop the current thread before closing."""
        return super(CameraWindow, self).closeEvent(event)

    def change_callback(self, *args, **kwargs):
        """
        change callback
        Args:
            *args:
            **kwargs:

        Returns:

        """
        self.change_callback_id = cmds.scriptJob(event=["SelectionChanged", self.update_ui], runOnce=True)

    def kill_change_callback(self, *args, **kwargs):
        """
        kill change callback
        Args:
            *args:
            **kwargs:

        Returns:

        """
        if self.change_callback_id:
            cmds.scriptJob(kill=self.change_callback_id, force=True)


def launch(*args, **kwargs):
    """ Launch the UI. """
    main_window = CameraWindow()
    main_window.launch()

    return main_window


if __name__ == '__main__':
    # module_cleanup("lsr.maya.cgame")
    from lsr.maya.cgame.face_camera import mainUI

    window = mainUI.launch()
