<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>442</width>
    <height>55</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>442</width>
    <height>55</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>442</width>
    <height>55</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Curve</string>
  </property>
  <widget class="QWidget" name="gridLayoutWidget">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>441</width>
     <height>53</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QPushButton" name="reset_2">
      <property name="text">
       <string>&lt;&lt;</string>
      </property>
     </widget>
    </item>
    <item row="0" column="4">
     <widget class="QPushButton" name="buffer">
      <property name="text">
       <string>Revert</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QSlider" name="slider">
      <property name="minimum">
       <number>-1</number>
      </property>
      <property name="maximum">
       <number>100</number>
      </property>
      <property name="value">
       <number>50</number>
      </property>
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
     </widget>
    </item>
    <item row="0" column="3">
     <widget class="QPushButton" name="reset">
      <property name="text">
       <string>&gt;&gt;</string>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QComboBox" name="combo">
        <item>
         <property name="text">
          <string>...</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Dampen</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Butterworth</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Smooth</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Twinner</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Simplify</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
