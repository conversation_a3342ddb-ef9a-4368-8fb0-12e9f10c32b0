# -*- coding: utf-8 -*-

import os
import time
import copy
import json
from collections import OrderedDict

from maya import cmds
from maya import OpenMaya
from maya import mel

from six import string_types

from lsr.python.core.signal import Signal
import lsr.maya.scene as sutil
import lsr.maya.ma_parser as mp
import lsr.maya.maya_math as mmath
from lsr.maya.nodezoo.node import Node
from lsr.maya.nodezoo.node import Joint
from lsr.maya.nodezoo.node import HIKCharacterNode
from lsr.maya.nodezoo.node import TransformConstraint

import lsr.maya.anim.retarget.constants as const
import lsr.maya.anim.retarget.utils as util
import lsr.maya.anim.retarget.bind_pose as bp
import lsr.maya.rig.joint_utils as jutil
import lsr.maya.rig.ikfk.quadruped_api as ikfk_api

from lsr.maya.animtools.hik_exporter import joint_export as hik_jot_exp
from lsr.maya.animtools.hik_exporter import hik_fbx_retargeting as hik_fbx
from lsr.maya.animtools.util_widgets import widgets as util_widgets
from lsr.maya.cgame.retargeter import src_one_diff_axis
# from lsr.maya.rig.ikfk.api import auto_key_control_wrapper

DEFAULT_CONFIG_NAME = 'retarget_config.json'
from lsr.maya.anim.retarget import retargeter_hik as rt


def get_joints_by_depth(namespace, *args):
    """
    Get joints depth

    Args:
        namespace (str): the namespace to retrieve the skeleton chain

    Returns:
        list
    """

    dagIterator = OpenMaya.MItDag(OpenMaya.MItDag.kDepthFirst, OpenMaya.MFn.kJoint)
    joint_depth = OrderedDict()
    while not dagIterator.isDone():
        dagPath = OpenMaya.MDagPath()
        dagIterator.getPath(dagPath)

        if dagPath.hasFn(OpenMaya.MFn.kJoint):
            if dagPath.partialPathName().startswith(namespace):
                dag_depth = dagIterator.depth()
                joint_depth[dagPath.partialPathName()] = dag_depth

        dagIterator.next()

    joint_depth_sorted = OrderedDict(sorted(joint_depth.items(), key=lambda x: x[1]))
    partial_joints = [node_name for node_name in joint_depth_sorted.keys()]

    return partial_joints


class Retargeter(rt.Retargeter):
    """
    Animation retargeter class.
    """
    def _get_joint_ctrl_map_to_hik_template(self):
        if not self._anim_rig_joints_map:
            return self._hik_joint_ctrl_map
        joint_ctrl_map = {}
        for anim_jot, rig_jot in self._anim_rig_joints_map.items():
            if rig_jot in self._hik_joint_ctrl_map:
                joint_ctrl_map[anim_jot] = self._hik_joint_ctrl_map[rig_jot]
        return joint_ctrl_map

    def _src_hik_to_tgt_hik(self):
        """Todo doc..."""
        # Determine whether there is hik data in the target binding file, otherwise exit
        hik_nodes = self._get_hik_nodes_by_namespace(self.__namespace)
        if not hik_nodes:
            return False
        else:
            # Todo If there are multiple hik nodes, how to choose the correct one is mainly to deal with
            #  the hik template file, because the hik of the custom controller cannot pass the finger animation
            self._tgt_hik_node = self._get_valid_hik_node(hik_nodes)
            if not self._tgt_hik_node:
                content = '"%s": Not valid humanIK node!!!' % self.__tgt_rig
                cmds.warning(content)
                popup = util_widgets.CountDownDialog(
                    parent=None, title='Target Rig not valid humanIK node: ',
                    content=content, timeout=10, width=500, height=150)
                popup.exec_()
                return False

        rig_hik_node = HIKCharacterNode(self._tgt_hik_node)
        rig_root_bone = rig_hik_node.get_joint('Reference')
        # if not rig_root_bone:
        #     rig_root_bone = rig_hik_node.get_joint('Hips')

        # Determine whether the animation file is successfully loaded, otherwise exit
        namespace = ''
        if self.__anim_ref:
            namespace = self.__anim_ref.namespace
        else:
            src_ns = ':' + const.NAMESPACE_SRC
            if cmds.namespace(exists=src_ns):
                namespace = const.NAMESPACE_SRC
        if not namespace:
            return False
        tmp_hik_jot_obj = hik_fbx.HikAnimRetargeting()
        frame_rate = tmp_hik_jot_obj.get_fbx_frame_rate(self.__anim_file)
        if frame_rate:
            tmp_hik_jot_obj.set_anim_frame_rate(frame_rate)

        min_time, max_time = self._get_time_range_by_namespace(namespace)
        min_time = int(min_time)
        max_time = int(max_time)
        cmds.playbackOptions(edit=True, animationStartTime=min_time, minTime=min_time)
        cmds.playbackOptions(edit=True, animationEndTime=max_time, maxTime=max_time)
        self._anim_time_range = (min_time, max_time)

        # Determine whether the target binding needs to create a hik template
        ex_bake_objs = None
        if not self._hik_joint_ctrl_map:
            self._hik_joint_ctrl_map = \
                tmp_hik_jot_obj.get_joint_ctrl_map_by_hik_node(self._tgt_hik_node)

        template_namespace = tmp_hik_jot_obj.get_new_tmp_namespace()

        ctrl_matrix_data = OrderedDict()

        if self._additive_joint_map:
            for mst_jot, sla_jot in self._additive_joint_map.items():
                sla_name = self._find_namespace_joints(self.__namespace, sla_jot)
                if sla_name:
                    sla_name = sla_name[0]
                if sla_name:
                    if Node.object_exist(sla_name):
                        sla_node = Node(sla_name)

                        if self._hik_joint_ctrl_map.get(sla_jot):
                            ctrl = self._find_namespace_joints(self.__namespace, self._hik_joint_ctrl_map[sla_jot])[0]
                            ctrl = Node(ctrl)
                        else:
                            ctrl = jutil.get_ctrl(sla_node)

                        if ctrl:
                            ctrl = Node(ctrl)
                            ctrl_matrix_data[ctrl] = ctrl.get_matrix(space='world')

        ctrl_hik_node = None

        if self._hik_joint_ctrl_map:
            # Initialize the state of ik fk
            tmp_hik_jot_obj.init_ik_fk_value(
                namespace=self.__namespace,
                ik_fk_setting=self._ik_fk_setting,
                hik_template_ik_fk_setting=self._hik_template_ik_fk_setting
            )

            joint_ctrl_map = self._get_joint_ctrl_map_to_hik_template()
            new_joint_ctrl_map = tmp_hik_jot_obj.create_hik_template_by_hik_node(
                joint_ctrl_map,
                hik_node=self._tgt_hik_node,
                namespace=template_namespace,
                additive_joint_map=self._additive_joint_map,
                is_connect_reference=False
            )

            # set pose to stance
            ctrl_hik_node = tmp_hik_jot_obj.get_tmp_hik_node()

            mel.eval('hikSetInactiveStanceInput( "%s" );' % ctrl_hik_node)
            mel.eval('hikSetStanceInput( "%s" );' % ctrl_hik_node)

            ex_bake_objs = list(new_joint_ctrl_map.values())
            self._new_joint_ctrl_map = new_joint_ctrl_map
            self._tgt_hik_node = tmp_hik_jot_obj.get_tmp_hik_node()

        # Situation 1, there is a hik node in the animation,
        # and the hik animation data is directly transmitted
        hik_nodes = self._get_hik_nodes_by_namespace(namespace)
        hik_node = self._get_valid_hik_node(hik_nodes)

        cns_m_nodes = []
        if self._additive_joint_map:

            # If the bone is not animated, use this dictionary to restore
            anim_jnt_local_data = OrderedDict()

            src_jnt_data = self._get_scr_rig_joints_data()
            all_constraint_nodes = []
            temp_locs = []
            for jot, ctrl in self._hik_joint_ctrl_map.items():
                anim_jnt = self._find_namespace_bones(namespace, jot)
                if not anim_jnt:
                    continue
                anim_jnt_basename = anim_jnt.split(':')[-1]

                if anim_jnt_basename in src_jnt_data.keys():
                    mtx = src_jnt_data[anim_jnt_basename]

                    # Determine whether the bone contains animation
                    anim_jnt_node = Node(anim_jnt)
                    anim_curves = anim_jnt_node.list_connections(type='animCurve',
                                                                 source=True,
                                                                 destination=False,
                                                                 plugs=False)
                    if not anim_curves:
                        anim_jnt_local_data[anim_jnt_node] = anim_jnt_node.get_matrix(space='object')

                    loc = Node.create('transform', name='{}_POSLOC'.format(anim_jnt_basename))
                    loc.set_matrix(mtx, space='world')
                    temp_locs.append(loc)
                    all_constraint_nodes.extend(cmds.parentConstraint(loc, anim_jnt))

            mst_ctrl_data = OrderedDict()
            ctrl_constraint_nodes = []
            ctrl_temp_locs = []

            for mst_jot, sla_jot in self._additive_joint_map.items():

                mst_name = self._find_namespace_joints(namespace, mst_jot)
                sla_name = self._find_namespace_joints(self.__namespace, sla_jot)

                if mst_name:
                    mst_name = mst_name[0]
                if sla_name:
                    sla_name = sla_name[0]

                if sla_name and mst_name:
                    if Node.object_exist(mst_name) and Node.object_exist(sla_name):
                        mst_node = Node(mst_name)
                        sla_node = Node(sla_name)

                        ctrl = None
                        if self._hik_joint_ctrl_map.get(sla_jot):
                            ctrl = self._find_namespace_joints(self.__namespace, self._hik_joint_ctrl_map[sla_jot])[0]
                            ctrl = Node(ctrl)

                        if not ctrl:
                            ctrl = Node(jutil.get_ctrl(sla_node))

                        if ctrl:
                            const_nodes = ctrl.list_connections(type='parentConstraint',
                                                                source=True,
                                                                destination=False,
                                                                plugs=False)
                            const_nodes = list(set(const_nodes))
                            if const_nodes:
                                cmds.delete(const_nodes)
                                # print('{} -> {} -> {}'.format(mst_name, sla_name, ctrl.name))

                            if ctrl in ctrl_matrix_data.keys():
                                loc = Node.create('transform', name='{}_POSLOC'.format(ctrl))
                                loc.set_matrix(ctrl_matrix_data[ctrl], space='world')
                                ctrl_temp_locs.append(loc)
                                ctrl_constraint_nodes.extend(cmds.parentConstraint(loc, ctrl))

                            mst_ctrl_data[mst_node] = ctrl
                    else:
                        cmds.warning('Not found {} or {}'.format(mst_name, sla_name))

            cmds.delete(ctrl_constraint_nodes)
            cmds.delete(ctrl_temp_locs)

            for mst_node, ctrl in mst_ctrl_data.items():
                cns_m = TransformConstraint.create(mst_node, ctrl, constraint_attr='tr')
                if ctrl not in ex_bake_objs:
                    ex_bake_objs.append(ctrl)
                # print('{} -> {}'.format(mst_node.name, ctrl.name))
                cns_m_nodes.append(cns_m)
                # cns_m.offsetMatrix.identity()

            cmds.delete(temp_locs)

            for node in all_constraint_nodes:
                if cmds.objExists(node):
                    cmds.delete(node)

            for anim_jnt_node, mtx in anim_jnt_local_data.items():
                anim_jnt_node.set_matrix(mtx, space='object')

        if hik_node:
            self._anim_file_hik_node = hik_node
            self._anim_file_hik_data = self._get_data_by_hik_node(hik_node)

            try:
                self._hik_jot_obj.apply_tmp_hik_anim_to_hik_node(
                    hik_node=self._tgt_hik_node, tmp_hik_node=self._anim_file_hik_node,
                    min_time=min_time, max_time=max_time, ex_bake_objs=ex_bake_objs)
            except BaseException as e:
                print(e.message)
                return False
            finally:
                if cns_m_nodes:
                    cmds.delete(cns_m_nodes)

            if self._tgt_hik_node == tmp_hik_jot_obj.get_tmp_hik_node():
                tmp_hik_jot_obj.remove_tmp_hik_joint()
                tmp_hik_jot_obj.ik_fk_switch(min_time, max_time)

            return True

        # Situation 2, there is no hik node in the animation,
        # and the source binding file needs to be loaded,
        # Then read the hik node,
        # let the animation file skeleton constrain the hik data of the source binding file, and then pass it
        tmp_hik_node = None
        if self.__src_rig and os.path.isfile(self.__src_rig) and not self._src_hik_node:
            tmp_ref_node, tmp_namespace = self._ref_tmp_file(self.__src_rig, '_tmp_src_rig_ref')

            hik_nodes = self._get_hik_nodes_by_namespace(tmp_namespace)
            # hik_node = self._get_valid_hik_node(hik_nodes)
            hik_node = hik_nodes[0] if hik_nodes else None

            if hik_node:
                self._src_hik_node = hik_node
                self._src_hik_data = self._get_data_by_hik_node(hik_node)
            self._remove_ref_tmp_file(tmp_ref_node, tmp_namespace)

        temp_kwargs = {'ctrl_namespace': template_namespace}

        if rig_root_bone:
            temp_kwargs['root_bone'] = rig_root_bone

        if self._src_hik_node:
            tmp_hik_node = self._anim_jot_constraint_tmp_hik_jot(
                self._src_hik_data, namespace, **temp_kwargs)

        # Situation 3, if reading the source binding hik data fails,
        # inherit the hik data of the previous animation file
        if self._anim_file_hik_node and not tmp_hik_node:
            tmp_hik_node = self._anim_jot_constraint_tmp_hik_jot(
                self._anim_file_hik_data, namespace, **temp_kwargs)

        if ctrl_hik_node:
            self.set_hik_to_none(ctrl_hik_node)

        # Situation 4, both the hik data of the last animation and
        # the hik data of the source binding file failed to be read, exit

        if self._hik_properties_data and ctrl_hik_node:
            ctrl_hik_zoo_node = HIKCharacterNode(ctrl_hik_node)
            property_node = ctrl_hik_zoo_node.hik_property_node
            if property_node:
                for attr, value in self._hik_properties_data.items():
                    getattr(property_node, attr).value = value
        else:
            util.log_info('hik_properties_data is None')

        if tmp_hik_node:
            self._hik_jot_obj.apply_tmp_hik_anim_to_hik_node(
                hik_node=self._tgt_hik_node, tmp_hik_node=tmp_hik_node,
                min_time=min_time, max_time=max_time, ex_bake_objs=ex_bake_objs
            )

            self._hik_jot_obj.remove_tmp_hik_joint()
            if self._tgt_hik_node == tmp_hik_jot_obj.get_tmp_hik_node():
                tmp_hik_jot_obj.remove_tmp_hik_joint()
                tmp_hik_jot_obj.ik_fk_switch(min_time, max_time)

            try:
                if cns_m_nodes:
                    for cns_m in cns_m_nodes:
                        if cmds.objExists(cns_m):
                            cmds.delete(cns_m_nodes)
            except BaseException as e:
                pass

            return True

        content = '"%s": Not valid humanIK node!!!' % self.__anim_file
        cmds.warning(content)
        popup = util_widgets.CountDownDialog(
            parent=None, title='Anim file not valid humanIK node: ',
            content=content, timeout=10, width=500, height=150)
        popup.exec_()

        return False

    def _src_one_diff_axis(self, disable_auto_key=True):
        """
        Retarget one to one, but the axes are different

        Returns:
            bool
        """
        state = cmds.autoKeyframe(state=True, query=True)
        if state:
            cmds.autoKeyframe(state=False)

        if not self.source_rig_jnt_data:
            self.source_rig_jnt_data = self._get_scr_rig_joints_data()

        src_jnt_data = self.source_rig_jnt_data
        tmp_hik_jot_obj = hik_fbx.HikAnimRetargeting()

        if not self._hik_joint_ctrl_map:
            hik_nodes = self._get_hik_nodes_by_namespace(self.__namespace)
            if not hik_nodes:
                cmds.warning('Configure the config files of the joint and ctrl map first!')
                return False
            else:
                self._tgt_hik_node = hik_nodes[0]
            self._hik_joint_ctrl_map = tmp_hik_jot_obj.get_joint_ctrl_map_by_hik_node(
                self._tgt_hik_node)

        if not self._hik_joint_ctrl_map:
            cmds.warning('Configure the config files of the joint and ctrl map first!')
            return False

        # anim_namespace is anim file namespace, referenced
        # self.__namespace is rig file namespace, like const.NAMESPACE_SRC
        anim_namespace = ''
        if self.__anim_ref:
            anim_namespace = self.__anim_ref.namespace
        else:
            src_ns = ':' + const.NAMESPACE_SRC
            if cmds.namespace(exists=src_ns):
                anim_namespace = const.NAMESPACE_SRC

        if not anim_namespace:
            cmds.warning(u'The animation file failed to load!')
            return False

        frame_rate = tmp_hik_jot_obj.get_fbx_frame_rate(self.__anim_file)
        if frame_rate:
            tmp_hik_jot_obj.set_anim_frame_rate(frame_rate)
        min_time, max_time = self._get_time_range_by_namespace(anim_namespace)
        min_time = int(min_time)
        max_time = int(max_time)
        self._anim_time_range = (min_time, max_time)

        src_one_diff_axis.main(
            anim_namespace=anim_namespace,
            tgt_namespace=self.__namespace,
            disable_auto_key=disable_auto_key,
            anim_rig_joints_map=self._anim_rig_joints_map,
            src_jnt_data=self.source_rig_jnt_data,
            hik_joint_ctrl_map=self._hik_joint_ctrl_map,
            ik_fk_setting=self._ik_fk_setting,
            hik_template_ik_fk_setting=self._hik_template_ik_fk_setting,
            min_time=min_time, max_time=max_time,
            force_ik=self._force_ik, ik_ctrl_info=self._ik_ctrl_info
        )
        return True
