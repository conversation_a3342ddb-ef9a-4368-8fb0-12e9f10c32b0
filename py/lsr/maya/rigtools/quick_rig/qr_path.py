import maya.cmds as cmds
import lsr.python.core.logger as logger
from Qt import QtWidgets
from pathlib import Path

__EXPORTER_TEMPLATE = 'assets/bodyRigAssets/temp.json'
__EXPORTER_CTRL = 'assets/bodyRigAssets/ctrl.json'
__EXPORTER_RIGMESH = 'assets/bodyRigAssets/rigmesh/rig_mesh.ma'
__EXPORTER_SKIN = 'assets/bodyRigAssets/deformer/skin.gnzd'
__EXPORTER_RENAME = 'assets/bodyRigAssets/rename.json'
__EXPORTER_SDK = 'assets/bodyRigAssets/anim/driven_keys.json'
__EXPORTER_NESTING = 'assets/bodyRigAssets/connections/nesting_constraint.json'
__EXPORTER_SCRIPT = 'assets/bodyRigAssets/post_custom.py'

__EXPORTER_MAYA_REBUILD = 'scenes/rig/referenced/lsr_rebuild.ma'
__EXPORTER_MAYA_RENAME = 'scenes/rig/referenced/lsr_rename.ma'
__EXPORTER_MAYA_SKINMESH = 'scenes/rig/referenced/lsr_skin_mesh.ma'

def get_current_maya_file():
    """
    Get the current maya file
    """
    maya_file = Path(cmds.file(q=True, sn=True))
    # if not maya_file.is_file():
    #     logger.error("No scene file found. {}".format(maya_file))
    #     return False
    return maya_file


def get_project_path():
    """
    Get the current project path
    """
    return cmds.workspace(q=True, rd=True)


# check if the current scene is in the project path
def is_in_project_path():
    """
    Check if the current scene is in the project path
    """
    maya_file = get_current_maya_file()
    if not (r'scenes\rig\referenced' in str(maya_file)):
        logger.error('The Maya file is not a valid lsr file path.')
        return False

    project_path = get_project_path()
    project_file = Path(project_path) / 'scenes/rig/referenced' / maya_file.name
    if maya_file != project_file:
        logger.error('Maya file and project paths are inconsistent.')

        current_file_project_path = maya_file.parent.parent.parent.parent

        msg = QtWidgets.QMessageBox()
        msg.setIcon(QtWidgets.QMessageBox.Question)

        msg.setText(" " * 150)
        msg.setInformativeText(
            "Set the current maya file path '{}' as workspace".format(current_file_project_path))
        msg.setWindowTitle("Warning")
        msg.setStandardButtons(QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No)
        retval = msg.exec_()
        if retval == QtWidgets.QMessageBox.Yes:
            cmds.workspace(current_file_project_path, openWorkspace=True)
            print("Yes")
            return True

        return False

    return True

def __create_folder_if_not_exist(file):
    """
    Create folder if not exist
    """
    if not file.parent.exists():
        file.parent.mkdir(parents=True)
    return file


def get_template_file():
    """
    Get the template file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_TEMPLATE)


def get_ctrl_file():
    """
    Get the control file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_CTRL)


def get_rigmesh_file():
    """
    Get the rig mesh file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_RIGMESH)


def get_skin_file():
    """
    Get the skin file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_SKIN)


def get_rename_file():
    """
    Get the rename file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_RENAME)


def get_sdk_file():
    """
    Get the SDK file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_SDK)


def get_maya_rebuild_file():
    """
    Get the Maya rebuild file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_MAYA_REBUILD)


def get_maya_rename_file():
    """
    Get the Maya rename file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_MAYA_RENAME)


def get_maya_skin_mesh_file():
    """
    Get the Maya skin mesh file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_MAYA_SKINMESH)


def get_nesting_file():
    """
    Get the nesting file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_NESTING)


def get_maya_script_file():
    """
    Get the Maya script file
    """
    return __create_folder_if_not_exist(Path(get_project_path()) / __EXPORTER_SCRIPT)
