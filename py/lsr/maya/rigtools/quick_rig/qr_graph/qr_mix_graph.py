import maya.cmds as cmds
import lsr.python.core.logger as logger
from lsr.maya.rigtools.quick_rig import qr_utils
from lsr.maya.rigtools.quick_rig import qr_data as qr_data
from lsr.maya.rigtools.quick_rig import qr_constant as qrc
from lsr.maya.rigtools.quick_rig import qr_path
from lsr.maya.rigtools.quick_rig.qr_interface.qr_interface_graph import IGraph

class QRMixGraph(IGraph):
    """
    The advanced graph for quick rig.
    """
    def __init__(self, fun):
        self.__export_data_fun = fun
        self.node2data = {qrc.IMPORT_CTRL_SHAPES: qr_data.QRCtrl, qrc.RENAME_SKIN_SKELETON: qr_data.QRRename,
                          qrc.IMPORT_DEFORMERS: qr_data.QRigSkin, qrc.RIG_MESH: qr_data.QRSkinMesh,
                          qrc.TEMPLATE: qr_data.QRTemplate, qrc.IMPORT_SDK: qr_data.QRSDK}

    def get_node_data(self):
        """
        Get the data of the nodes in the graph.
        """
        graph_builder = qr_utils.get_graph_builder_from_scene()
        if not graph_builder:
            logger.error('No graph in the scene.')
            return []

        all_basic_nodes = [qrc.TEMPLATE, qrc.IMPORT_CTRL_SHAPES, qrc.RENAME_SKIN_SKELETON,
                           qrc.IMPORT_DEFORMERS, qrc.IMPORT_SDK, qrc.NESTING_CONSTRAINT, qrc.RIG_MESH]

        nodes = [self.node2data[node]() for node in all_basic_nodes if
                 graph_builder.find_node_by_type(node) and self.node2data.get(node, None)]
        return nodes

    def override_limbs_setting(self, graph_builder):
        """
        Override the setting of the limbs.
        args:
            graph_builder: the graph builder.

        """
        act = graph_builder.find_node_by_type(qrc.RIG_MESH)
        if act:
            file = qr_path.get_rigmesh_file()
            skin_mesh_file = file.parent / 'skin_mesh.ma'
            act.set_param('mesh_file', str(skin_mesh_file))

            if act.has_param('clean_history'):
                act.set_param('clean_history', False)
            else:
                act.add_param({"type": "bool", "name": "clean_history", "script_enabled": False, "value": False})

    def build(self):
        """
        Build the graph.
        """

        if not qr_path.is_in_project_path():
            return False

        graph_builder = qr_utils.get_graph_builder_from_scene()
        if not graph_builder:
            logger.error('No graph in the scene.')
            return False

        file = qr_path.get_maya_rebuild_file()

        lsr_root = graph_builder.find_node_by_type(qrc.ROOT)
        lsr_root_parent = lsr_root.parent_node
        graph_builder.remove_node(lsr_root, recursive=True)

        limbs = qr_utils.get_all_limb_in_scene()
        qr_utils.add_limbs_to_graph(graph_builder, limbs)

        graph_builder.caculate_parent_node_by_param()

        # set the parent node
        lsr_root = graph_builder.find_node_by_type(qrc.ROOT)
        lsr_root.set_parent_node(lsr_root_parent)

        # set the rig_cleanup node to the end of the graph
        qr_utils.set_cleanup_to_end(graph_builder)

        qr_utils.update_limbs_setting(graph_builder)
        self.override_limbs_setting(graph_builder)
        self.__export_data_fun()

        graph_builder.refresh_graph()
        # graph_builder.save_graph(str(file.parent / "lsr_builder_graph_create.agraph"))
        graph_builder.execute()

        cmds.file(rename=str(file))
