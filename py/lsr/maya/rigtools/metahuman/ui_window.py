from PySide2 import QtWidgets

import lsr.maya.rigtools.metahuman.ui_widget as ui_widget


class UIWindow(QtWidgets.QMainWindow, ui_widget.UIWidget):

    # UI name that to find the UI instance - used in showUI()
    ui_name = "window"

    def closeEvent(self, event):
        """called when the UI is closed"""

        # gets called on cleanup
        self.cleanupOnClose()

    def cleanupOnClose(self):
        """implement any cleaup code here"""
        print("cleaning up on close")
