from Qt import QtWidgets
from functools import partial


class JointDefinition(QtWidgets.QGroupBox):
    """Joint Definition QtWidgets"""

    def __init__(self, parent=None):
        super(JointDefinition, self).__init__(parent=parent)
        self.parent_widget = parent
        self.setTitle('Joint Definition Settings')
        joints_vbox = QtWidgets.QVBoxLayout(self)

        self.jnt_def_widget = QtWidgets.QWidget(parent)
        grid_layout = QtWidgets.QGridLayout(self)
        self.start_jnt_btn = QtWidgets.QPushButton(
            'Set Start Joint', parent=self)
        self.end_jnt_btn = QtWidgets.QPushButton(
            'Set End Joint', parent=self)
        grid_layout.addWidget(self.start_jnt_btn, 0, 0)
        grid_layout.addWidget(self.end_jnt_btn, 0, 1)
        self.jnt_def_widget.setLayout(grid_layout)
        joints_vbox.addWidget(self.jnt_def_widget)

        self.create_connections()

    def create_connections(self):
        """create connections"""
        self.start_jnt_btn.clicked.connect(partial(self.parent_widget.set_joint, True))

        self.end_jnt_btn.clicked.connect(partial(self.parent_widget.set_joint, False))
