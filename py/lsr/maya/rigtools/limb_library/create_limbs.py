import os
from collections import OrderedDict

import maya.OpenMaya as OpenMaya
from maya import cmds

from lsr.protostar.lib import ActionLibrary as alib
from lsr.maya.nodezoo.node import Node
from lsr.maya.rigtools.limb_library.data import utils
from lsr.maya.utils import undoable

import lsr.maya.rig.constants as const
import lsr.maya.rigtools.limb_library.paths as paths_util


def get_rig_limb_types(*args, **kwargs):
    """
    get rig limb types

    Returns:
        OrderedDict
    """
    paths = os.getenv('LSR_LIMBLIB_PATH').split(os.path.pathsep)
    limb_types = paths_util.get_limbs_types(paths_util.resolve_paths(paths))

    return limb_types


def convert_limb_name(s):
    """
    convert limb name
    Args:
        s (str): string

    Returns:

    """
    result = []
    to_upper = False
    for char in s:
        if char == '_':
            to_upper = True
        else:
            if to_upper:
                result.append(char.upper())
                to_upper = False
            else:
                result.append(char)
    return ''.join(result)


class RigLimb_Creation(object):
    """
    RigLimb Creation
    """
    _root_limb_name = 'Root_M_Limb'
    LIMB_TYPES = get_rig_limb_types()

    alib.refresh()

    def __init__(self, name, *args, **kwargs):
        """

        Args:
            name (str): name
            *args ():
            **kwargs ():
        """
        self.name = convert_limb_name(name)
        self.action = kwargs.get('action', None)

        if not self.action:
            self.action_type = kwargs.get('action_type', None)

            if self.action_type.find(':') == -1:
                self.action_type = 'lsr:{}'.format(self.action_type)

            if self.action_type not in self.LIMB_TYPES:
                OpenMaya.MGlobal.displayError('The action type is not in the LIMB_TYPES')
                return

            self.action = alib.create_action(self.action_type)

        if not self.action:
            OpenMaya.MGlobal.displayError('The action attribute is None')
            return

        self.limb_data_path = kwargs.get('limb_data_path', self.LIMB_TYPES[self.action_type])

        self._node = None

    @classmethod
    def create(cls, *args, **kwargs):
        """
        create root limb
        Returns:
            self(RigLimb_Creation)
        """
        rig_limb = cls(*args, **kwargs)
        rig_limb.create_transform()
        return rig_limb

    @property
    def node(self):
        """
        type: LSRLimbTransform
        Returns:
            LSRLimbTransform
        """
        return self._node

    @node.setter
    def node(self, value):
        self._node = value

    @undoable
    def create_transform(self, null_mode=False):
        """
        Create LSRLimbTransform Node
        Returns:
            self.node(LSRLimbTransform): Limb Transform
        """
        if not self.action:
            OpenMaya.MGlobal.displayError('The action attribute is None')
            return

        limb_name = self.name

        limb_info = None
        if os.path.exists(self.limb_data_path):
            data_temp = utils.Generate_JointTemplate(file_path=self.limb_data_path)
            limb_info = data_temp.data

        if not limb_name:
            if not limb_info:
                OpenMaya.MGlobal.displayWarning('Please specify the part name of the limb')
                return
            else:
                limb_name = limb_info['name']

        limb_name = limb_name.split('_')[0]

        if os.path.exists(self.limb_data_path) and not null_mode:
            joint_temp = utils.Generate_JointTemplate(file_path=self.limb_data_path,
                                                      name_part=limb_name)
            self.node = joint_temp.create()
            if hasattr(self.node, 'part'):
                self.node.part.value = limb_name

            _start = self.node.startJoint.value[0]
            if self.node.input_skeleton_type == const.InputSkelType.single_joint:
                joint_chain = [_start]
            elif self.node.input_skeleton_type == const.InputSkelType.multi_chain:
                joint_chain = []
            else:
                _end = self.node.endJoint.value[0]
                joint_chain = _start.get_chain(_end)

        else:
            raise IOError(
                "User-defined json.data not found.")

        if not self.node.has_attr(const.ATTR_SOURCE):
            attr = self.node.add_attr('message', name=const.ATTR_SOURCE, multi=True)
        else:
            attr = self.node.attr(const.ATTR_SOURCE)

        for jnt_node in joint_chain:
            i = attr.num_elements
            cmds.connectAttr(
                '{}.message'.format(jnt_node),
                '{}.{}[{}]'.format(self.node, const.ATTR_SOURCE, i))

        if self.node.name != self._root_limb_name:
            self.node.set_parent(self.limb_root.startJoint.value[0])

        return self.node

    @property
    def limb_root(self):
        """
        Get the LIMB_ROOT in the current scene, which is unique.
        If it does not exist, it will be created automatically,
        so all of LSRLimbTransform will be placed under the node
        Returns:
            limb_root(LSRLimbTransform)
        """
        if Node.object_exist(self._root_limb_name):
            return Node(self._root_limb_name)
        else:
            limb_name = 'Root'
            limb_data_path = paths_util.get_lib_paths()[0]
            limb_data_path += '/common/RootLimb/data.json'
            joint_temp = utils.Generate_JointTemplate(file_path=limb_data_path,
                                                      name_part=limb_name)
            node = joint_temp.create()
            return node

    def refresh_transform(self):
        """
        Set LSRLimbTransform 'parent_limb' and 'parent_joint' attributes
        Automatically connecting according to the hierarchy it is in

        Returns:
            None
        """
        self.node.set_parent_messages()

    def set_joint(self, start=True):
        """set start/end joint"""
        if self.node:
            jnts = cmds.ls(selection=True, type='joint')
            if not jnts:
                return
            jnt = Node(jnts[0])
            if start:
                self.node.startJoint = jnt
            else:
                self.node.endJoint = jnt
        cmds.select(self.node, replace=True)

