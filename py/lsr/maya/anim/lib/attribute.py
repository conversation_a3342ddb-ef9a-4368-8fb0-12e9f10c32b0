"""
This class is used for query/modify attribute data

"""
from lsr.maya.nodezoo.attribute import Attribute as Attr<PERSON>pi
from lsr.maya.anim.lib.base import DataBase
import maya.OpenMaya as OpenMaya
from lsr.maya.nodezoo.node.anim_curve import AnimCurve
import maya.cmds as cmds


class Attribute(object):
    def __init__(self, attribute):
        self.__attribute = AttrApi(attribute)

    @property
    def is_static(self):
        """
        Check if there's input connection to this attribute. if not,
        this attribute is of static value
        Returns:
            bool: If this attribute is static
        """
        return not self.source_node

    @property
    def source_node(self):
        return self.__attribute.source_node

    @property
    def value(self):
        return self.__attribute.value

    @value.setter
    def value(self, v):
        if self.__attribute.is_free_to_change:
            self.__attribute.value = v
        else:
            OpenMaya.MGlobal.displayWarning("{} is not free to change. Skipped".format(self.__attribute.short_name))

    @property
    def name(self):
        return self.__attribute.name

    @property
    def short_name(self):
        return self.__attribute.short_name

    def remove_animation(self):
        cmds.cutKey(self.short_name)

    def export(self, static=False, bake=False, *args, **kwargs):
        """
        Data exporter.
        Args:
            static(bool): If export static value only at current frame
            bake(bool): If bake values for each frame
            *args:
            **kwargs:

        Returns:
            AttributeData
        """

        data = AttributeData()
        if static or self.is_static:
            data['v'] = self.value
            return data
        source_node = self.source_node

        # Only check time based anim curves
        if source_node.type_name.startswith('animCurveT'):
            anim_data = dict()
            # slice animation on export
            anim_data['keys'] = source_node.get_keys_data(*args, **kwargs)
            anim_data['preInfinity'] = source_node.get_attr('preInfinity')
            anim_data['postInfinity'] = source_node.get_attr('postInfinity')
            data['anim'] = anim_data
        else:
            # If there's no animation input, save it as static data
            data['v'] = self.value
        return data
        # TODO: Support baking data for constraint

    def load(self, data, offset_frames=0, merge=False, *args, **kwargs):
        """
        Load data to this attribute instance
        Args:
            data:
            offset_frames:
            merge(bool): If merge keys or replace original keys
            *args:
            **kwargs:

        Returns:

        """

        anim_data = data.get("anim", dict())
        keys_data = anim_data.get("keys", dict())
        pre_infinity = anim_data.get("preInfinity", 0)
        post_infinity = anim_data.get("postInfinity", 0)

        source_node = self.source_node
        if not source_node:
            # Create  anim curve on this attribute
            source_node = AnimCurve.create_on_attribute(self.__attribute)
        elif not source_node.type_name.startswith('animCurveT'):
            OpenMaya.MGlobal.displayWarning(
                'Existing connection on {},'
                'skip setting animation'.format(self.short_name))
            return

        if not source_node:
            OpenMaya.MGlobal.displayWarning(
                "Failed to create anim curve on {},"
                " skipped.".format(self.__attribute))
            return

        final_key_data = {}
        for k, v in keys_data.items():
            final_key_data[str(float(k) + float(offset_frames))] = v

        # flin_test
        start_frame = kwargs.get("start_frame", 0)
        end_frame = kwargs.get("end_frame", 0)
        offset_frames = kwargs.get("end_frame", 0)

        #source_node.set_keys_data(final_key_data, merge=merge, new_range=[start_frame, end_frame])
        source_node.set_keys_data(final_key_data, merge=merge)
        source_node.preInfinity.value = pre_infinity
        source_node.postInfinity.value = post_infinity


class AttributeData(DataBase):
    @property
    def is_static(self):
        return 'v' in self

    @property
    def static_value(self):
        return self.get('v', 0)

    @property
    def keys_data(self):
        if 'anim' in self:
            anim_data = self.get('anim', dict())
            return anim_data.get('keys', dict())

    @keys_data.setter
    def keys_data(self, val):
        if 'v' in self:
            del self['v']

        if 'anim' not in self:
            self['anim'] = dict()
        self['anim']['keys'] = val

    def get_key_frames(self):
        keys_data = self.keys_data
        if not keys_data:
            return None
        keys = sorted([i for i in keys_data])
        return keys

    @property
    def pre_infinity(self):
        if 'anim' in self:
            anim_data = self.get('anim', dict())
            return anim_data.get('preInfinity', 0)
        return 0

    @property
    def post_infinity(self):
        if 'anim' in self:
            anim_data = self.get('anim', dict())
            return anim_data.get('postInfinity', 0)
        return 0

    def offset_keys(self, offset):
        if 'anim' not in self:
            # No need to offset if data is static
            return
        if offset == 0:
            return
        new_key_data = {float(k) + float(offset): v for k, v in self.keys_data.items()}
        self.keys_data = new_key_data
