from PySide2 import QtWidgets
import maya.cmds as cmds
import os
import json
import lsr.maya.startup.constants as constants
import lsr.maya.startup.action as action
from lsr.maya.ui.main_window import get_main_window

menu_library = list()


def init_menus():
    """
    Init the menus

    """
    print('[LSR] initializing menu...')

    main_window = get_main_window()
    if not main_window:
        return
    menu_bar = main_window.menuBar()
    actions = menu_bar.actions()
    lsr_menu = QtWidgets.QMenu('LSR', menu_bar)
    root_menu = 'LSR_menu'
    lsr_menu.setObjectName(root_menu)
    menu_bar.insertMenu(actions[-1], lsr_menu)

    resource_path_var = os.environ.get(constants.resource_var)
    resource_paths = resource_path_var.split(os.pathsep)
    for path in resource_paths:
        file_path = os.path.join(path, constants.menu_file_name)
        file_path = file_path.replace('\\', '/')
        if os.path.isfile(file_path):
            with open(file_path, 'r') as f:
                if not f:
                    continue
                data = json.load(f)
                if data and isinstance(data, list):
                    menu_library.extend(data)

    menu_suffix = {}
    for button_data in menu_library:
        action_name = button_data.pop('action')
        label = button_data.get('label')
        if not action_name:
            continue

        if 'parent' in button_data:
            parent = button_data.pop('parent')
            children = cmds.menu(root_menu, query=True, itemArray=True) or []
            for c_menu in children:
                suffix = c_menu.split('|')[-1]
                if suffix not in menu_suffix:
                    menu_suffix[suffix] = c_menu

            if parent not in menu_suffix:
                parent = cmds.menuItem(parent, label=parent, subMenu=True, parent=root_menu, tearOff=True)
            else:
                parent = menu_suffix[parent]
            cmds.setParent(parent, menu=True)

        else:
            cmds.setParent(root_menu, menu=True)

        button_data = action.action_library.get(action_name)
        if not button_data:
            continue
        command = button_data.pop('command')
        command, source_type = action.resolve_command_source(command)

        button_data = {str(key): val for key, val in button_data.items()}
        button_data['command'] = command
        button_data['sourceType'] = source_type

        if 'tag' in button_data:
            button_data.pop('tag')
        if 'icon' in button_data:
            button_data.pop('icon')

        if label:
            button_data['label'] = label

        annotation = button_data.get('annotation')
        if not annotation:
            annotation = button_data.get('annotation')

        if not annotation:
            annotation = action_name
        button_data['annotation'] = annotation
        
        cmds.menuItem(**button_data)
