from PySide2 import QtWidgets
from PySide2 import QtCore


class ContainerMixin(object):
    def __init__(self):
        super(ContainerMixin, self).__init__()

    def build_container(self, entities, constructor, stretch=False):
        # set up state
        self.constructor = constructor
        self.stretch = stretch
        self.items = list()
        self.items_dict = dict()

        # fill content
        self._add_widgets(entities)

        # If stretch add stretcher
        if self.stretch:
            self._add_stretch()

    def _add_widgets(self, entities):
        for entity in entities:
            widget = self.constructor(entity, self)
            self.items_dict[entity] = widget
            self.items.append(widget)
            self.layout.addWidget(widget)

    def replace_content(self, entities):
        item = self.layout.takeAt(0)
        while item:
            if item:
                # get widget at given item
                widget = item.widget()
                if widget:
                    # if widget has cleanup method call it
                    if hasattr(widget, 'clean_callbacks') and callable(
                            getattr(widget, 'clean_callbacks')):
                        widget.clean_callbacks()
                    # delete widget
                    self.layout.removeWidget(widget)
                    widget.deleteLater()
                # delete item
                del item
                item = self.layout.takeAt(0)

        # reset state
        self.items = list()
        self.items_dict = dict()

        # fill content
        self._add_widgets(entities)

        # If stretch add stretcher
        if self.stretch:
            self._add_stretch()

    def _add_stretch(self):
        if self.stretch:
            self.layout.addStretch()

    def add_item(self, entity, index=None):
        widget = self.constructor(entity, self)
        self.items_dict[entity] = widget
        if index is None:
            # ignore stretch element if exists
            index = self.layout.count() - self.stretch
        # insert as last or by index
        self.items.insert(index, widget)
        self.layout.insertWidget(index, widget)
        return widget

    def remove_item(self, entity):
        if entity in self.items_dict:
            widget = self.items_dict[entity]
            # if widget has cleanup method call it
            if hasattr(
                widget,
                'clean_callbacks') and callable(
                getattr(
                    widget,
                    'clean_callbacks')):
                widget.clean_callbacks()
            self.layout.removeWidget(widget)
            widget.deleteLater()
            self.items_dict[entity] = None
            del self.items_dict[entity]
            self.items.remove(widget)


class ScrolledContainer(ContainerMixin, QtWidgets.QScrollArea):
    def __init__(self, entities, constructor):
        super(ScrolledContainer, self).__init__()

        self.setWidgetResizable(True)
        self.setFocusPolicy(QtCore.Qt.NoFocus)
        self.setFrameShadow(QtWidgets.QFrame.Plain)

        # Widget
        inner = QtWidgets.QFrame(self)

        # Lay out
        self.layout = QtWidgets.QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(2)
        inner.setLayout(self.layout)

        # Set main widget
        self.setWidget(inner)

        self.build_container(entities, constructor, stretch=True)
