<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>626</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>1</number>
      </property>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>导出设置</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <widget class="Line" name="line_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0">
          <item>
           <widget class="QLabel" name="label">
            <property name="text">
             <string>最大骨骼数量</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QSpinBox" name="Max_SpinBox">
            <property name="value">
             <number>3</number>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>分配方式</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="method_ComboBox">
            <property name="currentText">
             <string>按比例分配</string>
            </property>
            <property name="currentIndex">
             <number>1</number>
            </property>
            <item>
             <property name="text">
              <string>平均分配</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>按比例分配</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_11">
          <item>
           <widget class="QCheckBox" name="exp_mode_CB">
            <property name="styleSheet">
             <string notr="true">color:rgb(30, 190, 20)</string>
            </property>
            <property name="text">
             <string>裙摆模式</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="skinWeight_mode_CB">
            <property name="styleSheet">
             <string notr="true">color:rgb(230, 30, 20)</string>
            </property>
            <property name="text">
             <string>Skin Wight</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="Line" name="line_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QScrollArea" name="scrollArea">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="scrollAreaWidgetContents_2">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>257</width>
             <height>110</height>
            </rect>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <widget class="QListWidget" name="Model_listWidget"/>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <widget class="QPushButton" name="Model_Add_BTN">
                <property name="text">
                 <string>添加模型</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="Model_Refresh_BTN">
                <property name="text">
                 <string>刷新模型</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="Model_ClearBTN">
                <property name="text">
                 <string>清空列表</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QPushButton" name="Export_BTN">
            <property name="text">
             <string>导出</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_2">
       <attribute name="title">
        <string>蒙皮检查</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_10">
        <item>
         <widget class="QGroupBox" name="SkinMax_GroupBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Ignored" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="baseSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="title">
           <string>1. 模型权重检查</string>
          </property>
          <property name="flat">
           <bool>false</bool>
          </property>
          <property name="checkable">
           <bool>false</bool>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_8">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_9">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,0">
               <item>
                <widget class="QLabel" name="skinMaxJoint_LB">
                 <property name="text">
                  <string>超出骨骼数量</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="check_SpinBox">
                 <property name="value">
                  <number>3</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="Line" name="line_4">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_8">
               <item>
                <widget class="QLabel" name="label_4">
                 <property name="text">
                  <string>该模型每个顶点最多的骨骼影响数量：</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="max_count_label">
                 <property name="font">
                  <font>
                   <family>Arial</family>
                   <pointsize>12</pointsize>
                   <weight>75</weight>
                   <italic>false</italic>
                   <bold>true</bold>
                   <underline>false</underline>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color:rgb(255, 0, 0)</string>
                 </property>
                 <property name="text">
                  <string>0</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="Line" name="line_5">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_9">
               <item>
                <widget class="QPushButton" name="Skin_Check_BTN">
                 <property name="text">
                  <string>检查</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="Skin_Select_BTN">
                 <property name="text">
                  <string>选择超出的顶点</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="Skin_Modify_BTN">
                 <property name="font">
                  <font>
                   <weight>75</weight>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color:rgb(30, 190, 20)</string>
                 </property>
                 <property name="text">
                  <string>修改超出权重</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="Section_GroupBox">
          <property name="title">
           <string>2. 检查Section骨骼数量</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_10">
               <item>
                <widget class="QLabel" name="Section_lb">
                 <property name="text">
                  <string>骨骼最大数量</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="Section_SpinBox">
                 <property name="maximum">
                  <number>999</number>
                 </property>
                 <property name="value">
                  <number>67</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QLabel" name="SectionInfo_lb">
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QScrollArea" name="scrollArea_2">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>150</height>
                </size>
               </property>
               <property name="widgetResizable">
                <bool>true</bool>
               </property>
               <widget class="QWidget" name="Scroll_Widget">
                <property name="geometry">
                 <rect>
                  <x>0</x>
                  <y>0</y>
                  <width>734</width>
                  <height>148</height>
                 </rect>
                </property>
                <layout class="QVBoxLayout" name="verticalLayout_6">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_12">
                   <item>
                    <widget class="QLabel" name="label_5">
                     <property name="text">
                      <string>材质名字</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="label_3">
                     <property name="text">
                      <string>骨骼数量</string>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <spacer name="verticalSpacer">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>20</width>
                     <height>107</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="section_check_BTN">
               <property name="text">
                <string>检查选择的模型</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="BindPose_GroupBox">
          <property name="title">
           <string>3. BindPose 检查</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <item>
            <widget class="QTextBrowser" name="textBrowser"/>
           </item>
           <item>
            <widget class="QPushButton" name="bind_check_BTN">
             <property name="text">
              <string>检查选择的模型</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
