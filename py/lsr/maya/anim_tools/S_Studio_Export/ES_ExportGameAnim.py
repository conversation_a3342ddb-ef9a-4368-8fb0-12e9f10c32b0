# -*- coding: utf-8 -*-

import os
import re
import codecs
import maya.cmds as cmds
import maya.mel as mel

try:
    import PySide2.QtCore as QtCore
    import PySide2.QtGui as QtGui
    import PySide2.QtWidgets as QtWidgets
    from shiboken2 import wrapInstance
except ImportError:
    cmds.error('Maya Version Lowed')

from lsr.maya.maya_ui.widgets.frame import SimpleFrame
from lsr.maya.maya_ui.widgets.dialog import SimpleDialog
from lsr.maya.maya_ui.widgets import button
from lsr.maya.maya_ui.widgets import checkbox
import lsr.maya.anim_tools.S_Studio_Export.CutAnimKey as CutAnimKey
from lsr.maya.anim_tools.bone_constraint import BoneConstraint
from lsr.maya.anim_tools.S_Studio_Export.UI_Class import ExportInfoUI


def SetFbxParameter():
    if not cmds.pluginInfo('fbxmaya', q=True, loaded=True):
        cmds.loadPlugin('fbxmaya')
    mel.eval('FBXResetExport')
    mel.eval('FBXExportFileVersion -v FBX201600')
    mel.eval('FBXExportUpAxis z')
    mel.eval('FBXExportShapes  -v false')
    mel.eval('FBXExportScaleFactor 1.0')
    mel.eval('FBXExportInAscii -v true')
    mel.eval('FBXExportConstraints -v false')
    mel.eval('FBXExportLights -v false')
    mel.eval('FBXExportSkins -v false')
    mel.eval('FBXExportSmoothingGroups -v false')
    mel.eval('FBXExportSmoothMesh -v false')
    mel.eval('FBXExportEmbeddedTextures -v false')
    mel.eval('FBXExportCameras -v false')
    mel.eval('FBXExportBakeResampleAnimation -v false')
    mel.eval('FBXExportSkeletonDefinitions -v true')
    mel.eval('FBXExportInputConnections -v false')
    mel.eval('FBXExportIncludeChildren -v false')
    mel.eval('FBXExportUseSceneName -v true')


class ES_ExportGameAnim(object):
    def __init__(
            self,
            dirName,
            fileList=None,
            fileMode=False,
            *args,
            **kwargs):
        SetFbxParameter()
        self.mayaFilesList = []
        self.project = kwargs.get("project", kwargs.get("p", None))

        if fileMode:
            self.mayaFilesList = fileList
            self.dirName = os.path.dirname(fileList[0])
        else:
            dirName = dirName.replace('\\', '/')
            if dirName.endswith("/"):
                dirName = dirName[:-1]
            self.dirName = dirName
            self.getfilelist(self.dirName)

        if 'referenceMode' in kwargs:
            if kwargs['referenceMode']:
                self.referenceMode = kwargs['referenceMode']

        if 'folderMode' in kwargs:
            if kwargs['folderMode']:
                self.folderMode = kwargs['folderMode']

        self.weaponCtrls = []
        if 'weaponCtrls' in kwargs:
            if kwargs['weaponCtrls']:
                self.weaponCtrls = kwargs['weaponCtrls']

    def getfilelist(self, filepath):
        filelist = os.listdir(filepath)

        mayaFileRe = r'.*\.(mb|ma)$'
        for num in range(len(filelist)):
            filename = filelist[num]
            if os.path.isdir(filepath + "/" + filename):
                self.getfilelist(filepath + "/" + filename)
            else:
                if re.match(mayaFileRe, filename):
                    self.mayaFilesList.append(filepath + '/' + filename)

    def batchReplace(self):
        open_file = cmds.file(q=True, sn=True)

        infoUI = ExportInfoUI(len(self.mayaFilesList), 'ES_ExportGameAnim_Progress')
        infoUI.step()
        for animationFile in self.mayaFilesList:
            self.exportFile(animationFile)
            infoUI.step()
        infoUI.delete()
        del (infoUI)

        if open_file:
            cmds.file(open_file, open=True, f=True, pmt=False)
        else:
            cmds.file(new=True, f=True)
        return

    def importReference(self):
        rnodes = cmds.ls(type='reference')
        for nd in rnodes:
            if nd.find(":") == -1:
                try:
                    refPath = cmds.referenceQuery(nd, filename=True)
                    cmds.file(refPath, importReference=True)
                except BaseException:
                    pass
        return

    def deleteUnuesdType(self, root):
        unused_type = ['mesh', 'locator']
        del_only_type = ['nurbsCurve']
        for utype in unused_type:
            nodes = cmds.ls(root, dag=True, type=utype)
            for node in nodes:
                try:
                    cmds.delete(
                        cmds.listRelatives(
                            node,
                            parent=True,
                            type='transform'))
                except BaseException:
                    pass

        for utype in del_only_type:
            nodes = cmds.ls(root, dag=True, type=utype)
            for node in nodes:
                try:
                    cmds.delete(node)
                except BaseException:
                    pass
        return

    def selectExportJoints(self, root):
        user_attrs = cmds.listAttr(root, userDefined=True)
        if user_attrs:
            export_sets = []
            for attr in user_attrs:
                d_nodes = cmds.listConnections(
                    '%s.%s' %
                    (root, attr), destination=True)
                if d_nodes:
                    for nd in d_nodes:
                        if cmds.nodeType(nd) == 'objectSet':
                            if cmds.getAttr('%s.%s' % (root, attr)):
                                export_sets.append(nd)

            export_jnts = []
            for myset in export_sets:
                jnts = cmds.sets(myset, q=True)
                if jnts:
                    for jnt in jnts:
                        export_jnts.append(jnt)

            return export_jnts
        else:
            return None

    def exportFile(self, animationFile):
        cmds.file(new=True, f=True)
        cmds.file(animationFile, open=True, f=True, pmt=False)

        fileBasename = os.path.basename(animationFile)
        fileDirname = os.path.dirname(animationFile)

        rootJnts = []
        if self.referenceMode == 'ua_expAnimMode_01':
            rootJnts = cmds.ls("*:Root")
        elif self.referenceMode == 'ua_expAnimMode_02':
            rootJnts = cmds.ls("Root")

        if not rootJnts:
            cmds.error(u'骨骼命名不合法')
            return

        rootJnts = list(set(rootJnts))
        if self.project:
            if self.project == 'cj':
                try:
                    BoneConstraint.FileConstraint.addConstraint()
                except BaseException:
                    pass

        # import reference
        self.importReference()

        # Delete Weapon Constraint
        if self.weaponCtrls:
            for ctrl in self.weaponCtrls:
                try:
                    constNode = [
                        x for x in cmds.listRelatives(
                            ctrl, c=True) if x.find("Constraint") > 0]
                    cmds.delete(constNode)
                    cmds.setAttr(ctrl + '.t', 0, 0, 0)
                    cmds.setAttr(ctrl + '.r', 0, 0, 0)
                except BaseException:
                    pass

        for exportJnt in rootJnts:
            splitText = exportJnt.split(":")
            Prefix = ':'.join(splitText[:-1])

            # cmds.select(exportJnt, r=True)
            exp_joints = self.selectExportJoints(exportJnt)

            # 针对之前的非Set集的导出方式进行BUG处理
            mel.eval('FBXExportIncludeChildren -v false')
            if not exp_joints:
                mel.eval('FBXExportIncludeChildren -v true')
                exp_joints = cmds.ls(exportJnt, dag=True, type='joint')

            minT = cmds.playbackOptions(min=True, q=True)
            maxT = cmds.playbackOptions(max=True, q=True)
            self.bakeKeys(exp_joints, minT, maxT)

            if exp_joints:
                cmds.keyTangent(exp_joints, itt='linear', ott='linear')

            # 删除没有用的模型 and Locator and Curve
            self.deleteUnuesdType(exportJnt)
            CutAnimKey.custom_cutKeys(exportJnt)

            cmds.select(exp_joints, r=True)
            self.select_CharacterNodes()
            SavePath = ''

            ext = re.search(r'.*(\.\D+)', fileBasename).group(1)

            prefix_name = ''
            if self.referenceMode == 'ua_expAnimMode_01':
                prefix_name = splitText[-2].replace('_high_rig', '')
                prefix_name = prefix_name.replace('_rig', '')

            if self.folderMode == 'ua_expFolderMode_01':
                if self.weaponCtrls or len(rootJnts) > 1:
                    SavePath = fileDirname + '/' + prefix_name + \
                        '@' + fileBasename.replace(ext, '.fbx')
                else:
                    SavePath = fileDirname + '/' + \
                        fileBasename.replace(ext, '.fbx')

            elif self.folderMode == 'ua_expFolderMode_02':
                fileDirname = fileDirname + '/FBX_Anim'
                if not os.path.exists(fileDirname):
                    os.makedirs(fileDirname)

                if self.weaponCtrls or len(rootJnts) > 1:
                    SavePath = fileDirname + '/' + prefix_name + \
                        '@' + fileBasename.replace(ext, '.fbx')
                else:
                    SavePath = fileDirname + '/' + \
                        fileBasename.replace(ext, '.fbx')

            elif self.folderMode == 'ua_expFolderMode_03':
                export_folder = self.dirName + '_AnimFBX'
                if not os.path.exists(export_folder):
                    os.makedirs(export_folder)

                if self.weaponCtrls or len(rootJnts) > 1:
                    SavePath = fileDirname + '/' + prefix_name + \
                        '@' + fileBasename.replace(ext, '.FBX')
                else:
                    SavePath = fileDirname + '/' + \
                        fileBasename.replace(ext, '.FBX')

                SavePath = SavePath.replace(self.dirName, export_folder)
                exp_dir_name = os.path.dirname(SavePath)
                if not os.path.exists(exp_dir_name):
                    os.makedirs(exp_dir_name)

            mel.eval('FBXExport -f "' + SavePath + '" -s')

            fileObject = codecs.open(
                SavePath, 'r', encoding='utf-8', errors='ignore')
            try:
                data = fileObject.read()
                fileObject.close()
                fileObject = codecs.open(
                    SavePath, 'w', encoding='utf-8', errors='ignore')
                data = data.replace("::" + Prefix + ":", "::")
                fileObject.write(data)
                print("Remove prefix ...." + Prefix)
            finally:
                fileObject.close()

    def select_CharacterNodes(self):
        cmds.select(cmds.ls(type='HIKCharacterNode'), add=True)

    def bakeKeys(self, nodeName, minTimeValue, maxTimeValue):
        cmds.bakeResults(nodeName,
                         t=(int(minTimeValue),
                            int(maxTimeValue)),
                         simulation=True,
                         hierarchy='none',
                         sampleBy=1,
                         disableImplicitControl=True,
                         preserveOutsideKeys=True,
                         sparseAnimCurveBake=False,
                         removeBakedAttributeFromLayer=False,
                         bakeOnOverrideLayer=False,
                         minimizeRotation=True,
                         controlPoints=False,
                         shape=True)


class ES_ExportGameAnim_GUI(SimpleDialog):
    def __init__(self):
        super(ES_ExportGameAnim_GUI, self).__init__()
        self.setObjectName('ES_Export_Animation_Window')
        self.setWindowTitle(u'ES 导出Game动画工具')

    def display(self):
        set_item_widget = ES_ExportGameAnim_QtFrame()
        splitter1 = QtWidgets.QSplitter(QtCore.Qt.Vertical)
        splitter1.addWidget(set_item_widget)
        self.layout().addWidget(splitter1)


class ES_ExportGameAnim_QtFrame(SimpleFrame):
    def __init__(self, *args, **kwargs):
        super(ES_ExportGameAnim_QtFrame, self).__init__(*args, **kwargs)

        self.project = kwargs.get("project", kwargs.get("p", None))
        self.select_start = None
        self.EAInstance = None

    def display(self):
        self.main_widget.setMinimumWidth(400)
        self.main_widget.setMinimumHeight(400)

        title_layout = QtWidgets.QHBoxLayout()
        self.main_widget.layout().addLayout(title_layout)

        self.title_line = QtWidgets.QLabel(u'ES游戏动画导出工具')
        self.title_line.setStyleSheet('background-color : rgb(37, 38, 37);\
                            padding-top : 5px;\
                            color : rgb(255, 150, 0);\
                            font-size : 24px;\
                            font-weight: bold;')
        self.title_line.setMinimumHeight(35)
        title_layout.addWidget(self.title_line)

        self.close_bttn = button.MFA_CloseButton('X')
        self.close_bttn.setObjectName('roundedButton')
        self.close_bttn.setFixedHeight(45)
        self.close_bttn.setFixedWidth(45)
        self.close_bttn.clicked.connect(self.closeWidget)
        self.close_bttn.setVisible(False)
        title_layout.addWidget(self.close_bttn)

        self.path_line = QtWidgets.QLineEdit(u'')
        self.path_line.setStyleSheet('background-color : rgb(37, 38, 37);\
                                            padding-left : 15px;\
                                            padding-bottom : 12px;\
                                            color : rgb(20, 150, 20);\
                                            font-size : 13px;\
                                            font-weight: bold;')
        self.main_widget.layout().addWidget(self.path_line)

        group_box = QtWidgets.QGroupBox(u'导出模式')
        self.radio1 = QtWidgets.QRadioButton(u'参考Rig制作方式')
        self.radio2 = QtWidgets.QRadioButton(u'非参考方式')
        self.radio1.setChecked(True)
        vbox = QtWidgets.QVBoxLayout()

        vbox.addWidget(self.radio1)
        vbox.addWidget(self.radio2)
        group_box.setLayout(vbox)
        group_box.setStyleSheet(
            "QGroupBox { background-color : rgb(27, 28, 30);\
             border: 2px solid rgb(50, 50, 50);\
              font-size : 12px;\
              height: 80;}")

        self.main_widget.layout().addWidget(group_box)

        folder_group_box = QtWidgets.QGroupBox(u'导出目录')
        self.radio3 = QtWidgets.QRadioButton(u'原目录')
        self.radio4 = QtWidgets.QRadioButton(u'FBX目录')
        self.radio5 = QtWidgets.QRadioButton(u'父目录自动建立FBX目录')
        self.radio3.setChecked(True)
        folder_vbox = QtWidgets.QVBoxLayout()

        folder_vbox.addWidget(self.radio3)
        folder_vbox.addWidget(self.radio4)
        folder_vbox.addWidget(self.radio5)
        folder_group_box.setLayout(folder_vbox)
        folder_group_box.setStyleSheet(
            "QGroupBox { background-color : rgb(27, 28, 30);\
                     border: 2px solid rgb(50, 50, 50);\
                      font-size : 12px;\
                      height: 80;}")

        self.radio5.setVisible(False)
        self.main_widget.layout().addWidget(folder_group_box)

        # weapon Group
        weapon_group_box = QtWidgets.QGroupBox(u'武器拆分')
        self.weapon_cb = checkbox.MFA_Checkbox(u'是否单独导出武器')
        self.weapon_cb.setStyleSheet('font-size : 12px;')

        weapon_01_hbox = QtWidgets.QHBoxLayout()
        self.weapon_lb = QtWidgets.QLabel(u'武器的跟控制器')
        self.weapon_lb.setStyleSheet("font-size : 12px;")

        self.weapon_el = QtWidgets.QLineEdit()
        self.weapon_el.setStyleSheet('background-color : rgb(67, 68, 67);\
                                                    color : rgb(220, 220, 20);\
                                                    font-size : 13px;\
                                                    font-weight: bold;')

        self.weapon_btn = button.MFA_Button(u'选择')
        self.weapon_btn.clicked.connect(self.setCtrls)

        weapon_01_hbox.addWidget(self.weapon_lb)
        weapon_01_hbox.addWidget(self.weapon_el)
        weapon_01_hbox.addWidget(self.weapon_btn)
        self.weapon_lb.setVisible(False)
        self.weapon_el.setVisible(False)
        self.weapon_btn.setVisible(False)
        self.weapon_cb.stateChanged.connect(self.setWeaponUI)

        self.weapon_vbox = QtWidgets.QVBoxLayout()

        self.weapon_vbox.addWidget(self.weapon_cb)
        self.weapon_vbox.addLayout(weapon_01_hbox)

        weapon_group_box.setLayout(self.weapon_vbox)
        weapon_group_box.setStyleSheet(
            "QGroupBox { background-color : rgb(27, 28, 30);\
                     border: 2px solid rgb(50, 50, 50);\
                      font-size : 12px;\
                      height: 80;}")

        self.main_widget.layout().addWidget(weapon_group_box)

        weapon_group_box.setVisible(False)

        button_layout = QtWidgets.QHBoxLayout()
        self.main_widget.layout().addLayout(button_layout)
        self.folder_bttn = QtWidgets.QPushButton(u'导出目录')
        self.files_bttn = QtWidgets.QPushButton(u'导出文件')
        self.redo_bttn = QtWidgets.QPushButton(u'运行上次')
        self.folder_bttn.setFixedHeight(70)
        self.files_bttn.setFixedHeight(70)
        self.redo_bttn.setFixedHeight(70)
        self.folder_bttn.setStyleSheet('font-size : 20px;')
        self.files_bttn.setStyleSheet('font-size : 20px;')
        self.redo_bttn.setStyleSheet('font-size : 20px;')

        self.folder_bttn.clicked.connect(self.ChooseDir_Path)
        self.files_bttn.clicked.connect(self.ChooseFiles_Path)
        self.redo_bttn.clicked.connect(self.redo)
        button_layout.addWidget(self.folder_bttn)
        button_layout.addWidget(self.files_bttn)
        button_layout.addWidget(self.redo_bttn)

    def setWeaponUI(self):
        state = self.weapon_cb.checkState()
        self.weapon_lb.setVisible(state)
        self.weapon_el.setVisible(state)
        self.weapon_btn.setVisible(state)

    def getMode(self):
        if self.radio1.isChecked():
            return 'ua_expAnimMode_01'
        elif self.radio2.isChecked():
            return 'ua_expAnimMode_02'

    def getFolder(self):
        if self.radio3.isChecked():
            return 'ua_expFolderMode_01'
        elif self.radio4.isChecked():
            return 'ua_expFolderMode_02'
        elif self.radio5.isChecked():
            return 'ua_expFolderMode_03'

    def getCtrls(self):
        ctrls = []
        mesh_str = self.weapon_el.text()
        if mesh_str:
            mesh_list = mesh_str.split(',')[:-1]
            if mesh_list:
                for mesh in mesh_list:
                    ctrls.append(mesh)
        return ctrls

    def ChooseDir_Path(self, *args):
        keywords = {}

        if self.project:
            keywords['project'] = self.project

        keywords['referenceMode'] = self.getMode()
        keywords['folderMode'] = self.getFolder()

        if self.weapon_cb.isChecked():
            ctrls = self.getCtrls()
            if ctrls:
                keywords['weaponCtrls'] = ctrls

        if self.select_start:
            path = cmds.fileDialog2(
                dialogStyle=1,
                fileMode=3,
                startingDirectory=self.select_start)
        else:
            path = cmds.fileDialog2(dialogStyle=1, fileMode=3)

        if path:
            self.select_start = path[0]

        if path is not None:
            # print path
            self.EAInstance = ES_ExportGameAnim(path[0], **keywords)
            self.EAInstance.batchReplace()
            self.refreshLineEdit()

    def ChooseFiles_Path(self, *args):
        keywords = {}
        if self.project:
            keywords['project'] = self.project

        keywords['referenceMode'] = self.getMode()
        keywords['folderMode'] = self.getFolder()

        if self.weapon_cb.isChecked():
            ctrls = self.getCtrls()
            if ctrls:
                keywords['weaponCtrls'] = ctrls

        if self.select_start:
            path = cmds.fileDialog2(
                dialogStyle=1,
                fileMode=4,
                startingDirectory=self.select_start)
        else:
            path = cmds.fileDialog2(dialogStyle=1, fileMode=4)

        if path:
            self.select_start = os.path.dirname(path[0])
        if path is not None:
            # print path
            self.EAInstance = ES_ExportGameAnim("", path, True, **keywords)
            self.EAInstance.batchReplace()
            self.refreshLineEdit()

    def redo(self, *args):
        # cmds.select(self.getCtrls())
        if self.EAInstance:
            self.EAInstance.batchReplace()
        else:
            cmds.warning(u'记录是空的')

    def refreshLineEdit(self):
        if self.EAInstance:
            self.path_line.setText(
                '%s ... %d files' %
                (self.EAInstance.dirName, len(
                    self.EAInstance.mayaFilesList)))

    def setCtrls(self, *args):
        objs = cmds.ls(sl=True)
        target_str = ''
        if objs:
            for i in range(len(objs)):
                target_str += objs[i]
                target_str += ','
            self.weapon_el.setText(target_str)
            # cmds.textField(self.ctrls_TF, e=True, text = target_str)
        return


dialog = None


def create(docked=True):
    global dialog
    if dialog is None:
        dialog = ES_ExportGameAnim_GUI()
        dialog.show()


def delete():
    global dialog
    if dialog:
        dialog.close()
        dialog = None
