# -*- coding: utf-8 -*-

"""
批量引用替换运行脚本
在Maya中运行此脚本来批量处理文件
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(__file__)
if current_dir not in sys.path:
    sys.path.append(current_dir)

from batch_reference_replacer import BatchReferenceReplacer


def run_batch_replacement():
    """运行批量替换"""
    
    # 配置参数 - 请根据您的实际情况修改这些路径
    INPUT_DIRECTORY = "C:/YourMayaFiles"  # 替换为您的Maya文件目录
    
    OLD_REFERENCE = "J:/Content/OG2/IMMOArts/Characters/Player/Player/Common/EmptyHand/Maya/player_actor_rig.ma"
    NEW_REFERENCE = "J:/Content/OG2/IMMOArts/Characters/Player_new/Player/Common/EmptyHand/Maya/player_actor_rig.ma"
    
    print("="*60)
    print("Maya文件批量引用替换工具")
    print("="*60)
    print(f"输入目录: {INPUT_DIRECTORY}")
    print(f"旧引用路径: {OLD_REFERENCE}")
    print(f"新引用路径: {NEW_REFERENCE}")
    print(f"输出目录: {INPUT_DIRECTORY}_new")
    print("="*60)
    
    # 检查输入目录是否存在
    if not Path(INPUT_DIRECTORY).exists():
        print(f"错误: 输入目录不存在 - {INPUT_DIRECTORY}")
        print("请修改 INPUT_DIRECTORY 变量为正确的路径")
        return False
    
    # 创建替换器实例
    replacer = BatchReferenceReplacer()
    
    # 设置引用路径（如果需要自定义）
    replacer.old_reference_path = OLD_REFERENCE
    replacer.new_reference_path = NEW_REFERENCE
    
    try:
        # 执行批量处理
        result = replacer.process_directory(INPUT_DIRECTORY)
        
        print("\n" + "="*60)
        print("批量处理完成!")
        print("="*60)
        
        return result
        
    except Exception as e:
        print(f"批量处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def interactive_run():
    """交互式运行"""
    from maya import cmds
    
    print("Maya文件批量引用替换工具")
    print("-" * 40)
    
    # 获取用户输入
    result = cmds.promptDialog(
        title='输入目录路径',
        message='请输入要处理的Maya文件目录路径:',
        button=['确定', '取消'],
        defaultButton='确定',
        cancelButton='取消',
        dismissString='取消'
    )
    
    if result == '确定':
        input_dir = cmds.promptDialog(query=True, text=True)
        
        if input_dir and Path(input_dir).exists():
            # 创建替换器并运行
            replacer = BatchReferenceReplacer()
            result = replacer.process_directory(input_dir)
            
            # 显示结果
            cmds.confirmDialog(
                title='处理完成',
                message=f'批量处理完成!\n\n'
                       f'总文件数: {result["total"]}\n'
                       f'成功处理: {result["processed"]}\n'
                       f'处理失败: {result["errors"]}\n\n'
                       f'输出目录: {input_dir}_new',
                button=['确定']
            )
        else:
            cmds.confirmDialog(
                title='错误',
                message='输入的目录路径无效或不存在!',
                button=['确定']
            )


# 在Maya Script Editor中使用的简化版本
def quick_run(directory_path):
    """
    快速运行函数 - 在Maya Script Editor中使用
    
    Args:
        directory_path (str): 要处理的目录路径
    
    使用方法:
        from run_batch_replacer import quick_run
        quick_run("C:/path/to/your/maya/files")
    """
    replacer = BatchReferenceReplacer()
    return replacer.process_directory(directory_path)


if __name__ == "__main__":
    # 如果直接运行此脚本
    try:
        # 检查是否在Maya环境中
        from maya import cmds
        
        # 在Maya中运行交互式版本
        interactive_run()
        
    except ImportError:
        # 不在Maya环境中，运行标准版本
        print("警告: 不在Maya环境中运行")
        print("请在Maya Script Editor中运行此脚本")
        
        # 或者修改INPUT_DIRECTORY后运行
        # run_batch_replacement()
