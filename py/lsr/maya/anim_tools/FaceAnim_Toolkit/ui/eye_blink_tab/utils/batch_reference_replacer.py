# -*- coding: utf-8 -*-

"""
批量Maya文件引用路径替换工具
"""

import os
import shutil
from pathlib import Path
from maya import cmds


class BatchReferenceReplacer:
    """批量Maya文件引用替换器"""
    
    def __init__(self):
        self.old_reference_path = "J:/Content/OG2/IMMOArts/Characters/Player/Player/Common/EmptyHand/Maya/player_actor_rig.ma"
        self.new_reference_path = "J:/Content/OG2/IMMOArts/Characters/Player_new/Player/Common/EmptyHand/Maya/player_actor_rig.ma"
        self.processed_files = []
        self.error_files = []
        
    def find_maya_files(self, directory):
        """
        递归查找目录下所有Maya文件
        
        Args:
            directory (str): 要搜索的目录路径
            
        Returns:
            list: Maya文件路径列表
        """
        maya_files = []
        directory = Path(directory)
        
        if not directory.exists():
            print(f"错误: 目录不存在 - {directory}")
            return maya_files
            
        # 递归查找所有.ma和.mb文件
        for file_path in directory.rglob("*.ma"):
            maya_files.append(str(file_path))
        for file_path in directory.rglob("*.mb"):
            maya_files.append(str(file_path))
            
        print(f"在目录 '{directory}' 中找到 {len(maya_files)} 个Maya文件")
        return maya_files
    
    def check_reference_in_file(self, file_path):
        """
        检查Maya文件中是否包含指定的引用路径
        
        Args:
            file_path (str): Maya文件路径
            
        Returns:
            bool: 如果包含指定引用返回True，否则返回False
        """
        try:
            # 打开Maya文件
            cmds.file(file_path, open=True, force=True, ignoreVersion=True)
            
            # 获取所有引用节点
            ref_nodes = cmds.ls(type="reference")
            
            for ref_node in ref_nodes:
                try:
                    # 获取引用文件路径
                    ref_file_path = cmds.referenceQuery(ref_node, filename=True)
                    
                    # 标准化路径进行比较
                    ref_file_path = ref_file_path.replace("\\", "/")
                    old_path = self.old_reference_path.replace("\\", "/")
                    
                    if ref_file_path == old_path:
                        print(f"找到匹配的引用: {ref_file_path}")
                        return True
                        
                except Exception as e:
                    print(f"警告: 无法查询引用节点 {ref_node}: {e}")
                    continue
                    
            return False
            
        except Exception as e:
            print(f"错误: 无法打开文件 {file_path}: {e}")
            return False
    
    def replace_reference_in_file(self, file_path):
        """
        替换Maya文件中的引用路径
        
        Args:
            file_path (str): Maya文件路径
            
        Returns:
            bool: 替换成功返回True，否则返回False
        """
        try:
            # 打开Maya文件
            cmds.file(file_path, open=True, force=True, ignoreVersion=True)
            
            # 获取所有引用节点
            ref_nodes = cmds.ls(type="reference")
            replaced = False
            
            for ref_node in ref_nodes:
                try:
                    # 获取引用文件路径
                    ref_file_path = cmds.referenceQuery(ref_node, filename=True)
                    
                    # 标准化路径进行比较
                    ref_file_path = ref_file_path.replace("\\", "/")
                    old_path = self.old_reference_path.replace("\\", "/")
                    
                    if ref_file_path == old_path:
                        # 替换引用
                        print(f"替换引用: {old_path} -> {self.new_reference_path}")
                        cmds.file(self.new_reference_path, loadReference=ref_node)
                        replaced = True
                        
                except Exception as e:
                    print(f"警告: 无法处理引用节点 {ref_node}: {e}")
                    continue
            
            return replaced
            
        except Exception as e:
            print(f"错误: 无法处理文件 {file_path}: {e}")
            return False
    
    def create_output_directory(self, input_directory):
        """
        创建输出目录
        
        Args:
            input_directory (str): 输入目录路径
            
        Returns:
            str: 输出目录路径
        """
        input_path = Path(input_directory)
        output_path = Path(str(input_path) + "_new")
        
        # 如果输出目录不存在，创建它
        if not output_path.exists():
            output_path.mkdir(parents=True, exist_ok=True)
            print(f"创建输出目录: {output_path}")
        
        return str(output_path)
    
    def get_output_file_path(self, input_file_path, input_directory, output_directory):
        """
        获取输出文件路径
        
        Args:
            input_file_path (str): 输入文件路径
            input_directory (str): 输入目录路径
            output_directory (str): 输出目录路径
            
        Returns:
            str: 输出文件路径
        """
        input_path = Path(input_file_path)
        input_dir = Path(input_directory)
        output_dir = Path(output_directory)
        
        # 计算相对路径
        relative_path = input_path.relative_to(input_dir)
        
        # 构建输出路径
        output_path = output_dir / relative_path
        
        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        return str(output_path)
    
    def save_file(self, output_path):
        """
        保存Maya文件
        
        Args:
            output_path (str): 输出文件路径
            
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            # 确定文件类型
            ext = Path(output_path).suffix.lower()
            if ext == '.ma':
                file_type = 'mayaAscii'
            elif ext == '.mb':
                file_type = 'mayaBinary'
            else:
                print(f"错误: 不支持的文件类型 {ext}")
                return False
            
            # 重命名并保存文件
            cmds.file(rename=output_path)
            cmds.file(save=True, type=file_type, force=True)
            print(f"文件已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"错误: 无法保存文件 {output_path}: {e}")
            return False
    
    def process_directory(self, input_directory):
        """
        处理整个目录
        
        Args:
            input_directory (str): 输入目录路径
            
        Returns:
            dict: 处理结果统计
        """
        print(f"开始处理目录: {input_directory}")
        
        # 查找所有Maya文件
        maya_files = self.find_maya_files(input_directory)
        
        if not maya_files:
            print("未找到Maya文件")
            return {"total": 0, "processed": 0, "errors": 0}
        
        # 创建输出目录
        output_directory = self.create_output_directory(input_directory)
        
        # 重置统计
        self.processed_files = []
        self.error_files = []
        
        # 处理每个文件
        for i, file_path in enumerate(maya_files, 1):
            print(f"\n处理文件 {i}/{len(maya_files)}: {file_path}")
            
            try:
                # 检查文件是否包含目标引用
                if self.check_reference_in_file(file_path):
                    print("文件包含目标引用，开始替换...")
                    
                    # 替换引用
                    if self.replace_reference_in_file(file_path):
                        # 计算输出路径
                        output_path = self.get_output_file_path(
                            file_path, input_directory, output_directory
                        )
                        
                        # 保存文件
                        if self.save_file(output_path):
                            self.processed_files.append((file_path, output_path))
                            print("文件处理完成")
                        else:
                            self.error_files.append(file_path)
                    else:
                        print("引用替换失败")
                        self.error_files.append(file_path)
                else:
                    print("文件不包含目标引用，跳过")
                    
            except Exception as e:
                print(f"处理文件时出错: {e}")
                self.error_files.append(file_path)
        
        # 创建新场景，清理内存
        cmds.file(new=True, force=True)
        
        # 返回统计结果
        result = {
            "total": len(maya_files),
            "processed": len(self.processed_files),
            "errors": len(self.error_files)
        }
        
        self.print_summary(result)
        return result
    
    def print_summary(self, result):
        """
        打印处理摘要
        
        Args:
            result (dict): 处理结果统计
        """
        print("\n" + "="*50)
        print("处理摘要")
        print("="*50)
        print(f"总文件数: {result['total']}")
        print(f"成功处理: {result['processed']}")
        print(f"处理失败: {result['errors']}")
        
        if self.processed_files:
            print("\n成功处理的文件:")
            for input_file, output_file in self.processed_files:
                print(f"  {input_file} -> {output_file}")
        
        if self.error_files:
            print("\n处理失败的文件:")
            for error_file in self.error_files:
                print(f"  {error_file}")


def batch_replace_references(directory_path):
    """
    批量替换引用的便捷函数
    
    Args:
        directory_path (str): 要处理的目录路径
        
    Returns:
        dict: 处理结果统计
    """
    replacer = BatchReferenceReplacer()
    return replacer.process_directory(directory_path)


# 使用示例
if __name__ == "__main__":
    # 示例用法
    input_dir = "C:/path/to/your/maya/files"  # 替换为您的实际路径
    
    try:
        result = batch_replace_references(input_dir)
        print(f"\n处理完成! 成功处理了 {result['processed']} 个文件")
    except Exception as e:
        print(f"批处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
