# Maya批量引用替换工具

## 概述

这个工具用于批量处理Maya文件，查找并替换特定的引用路径。它会遍历指定目录下的所有Maya文件（.ma和.mb），检查是否包含目标引用路径，如果包含则进行替换，并将处理后的文件保存到新的目录中。

## 功能特点

- **批量处理**: 递归遍历目录下所有Maya文件
- **智能检测**: 只处理包含目标引用的文件
- **安全操作**: 原文件保持不变，处理后的文件保存到新目录
- **详细日志**: 提供详细的处理过程和结果统计
- **错误处理**: 完善的错误处理和恢复机制

## 默认配置

- **旧引用路径**: `J:/Content/OG2/IMMOArts/Characters/Player/Player/Common/EmptyHand/Maya/player_actor_rig.ma`
- **新引用路径**: `J:/Content/OG2/IMMOArts/Characters/Player_new/Player/Common/EmptyHand/Maya/player_actor_rig.ma`
- **输出目录**: 输入目录名 + "_new" 后缀

## 使用方法

### 方法1: 在Maya Script Editor中直接使用

```python
# 导入模块
import sys
sys.path.append('path/to/utils/folder')  # 替换为实际路径

from run_batch_replacer import quick_run

# 运行批量替换
result = quick_run("C:/path/to/your/maya/files")  # 替换为您的目录路径

print(f"处理完成! 成功处理了 {result['processed']} 个文件")
```

### 方法2: 使用交互式界面

```python
# 在Maya Script Editor中运行
import sys
sys.path.append('path/to/utils/folder')  # 替换为实际路径

from run_batch_replacer import interactive_run

# 运行交互式版本（会弹出对话框让您输入路径）
interactive_run()
```

### 方法3: 自定义配置

```python
import sys
sys.path.append('path/to/utils/folder')  # 替换为实际路径

from batch_reference_replacer import BatchReferenceReplacer

# 创建替换器实例
replacer = BatchReferenceReplacer()

# 自定义引用路径
replacer.old_reference_path = "您的旧引用路径"
replacer.new_reference_path = "您的新引用路径"

# 执行处理
result = replacer.process_directory("C:/path/to/your/maya/files")
```

## 文件结构

处理前:
```
your_directory/
├── scene1.ma
├── scene2.mb
├── subfolder/
│   ├── scene3.ma
│   └── scene4.mb
```

处理后:
```
your_directory/
├── scene1.ma          (原文件，未修改)
├── scene2.mb          (原文件，未修改)
├── subfolder/
│   ├── scene3.ma      (原文件，未修改)
│   └── scene4.mb      (原文件，未修改)

your_directory_new/     (新创建的目录)
├── scene1.ma          (处理后的文件，如果包含目标引用)
├── scene2.mb          (处理后的文件，如果包含目标引用)
├── subfolder/
│   ├── scene3.ma      (处理后的文件，如果包含目标引用)
│   └── scene4.mb      (处理后的文件，如果包含目标引用)
```

## 处理流程

1. **扫描文件**: 递归查找目录下所有.ma和.mb文件
2. **检查引用**: 打开每个Maya文件，检查是否包含目标引用路径
3. **替换引用**: 如果包含目标引用，则替换为新的引用路径
4. **保存文件**: 将处理后的文件保存到输出目录，保持原有的目录结构
5. **生成报告**: 输出详细的处理结果和统计信息

## 输出示例

```
开始处理目录: C:/YourMayaFiles
在目录 'C:/YourMayaFiles' 中找到 15 个Maya文件
创建输出目录: C:/YourMayaFiles_new

处理文件 1/15: C:/YourMayaFiles/scene1.ma
找到匹配的引用: J:/Content/OG2/IMMOArts/Characters/Player/Player/Common/EmptyHand/Maya/player_actor_rig.ma
文件包含目标引用，开始替换...
替换引用: J:/Content/OG2/IMMOArts/Characters/Player/Player/Common/EmptyHand/Maya/player_actor_rig.ma -> J:/Content/OG2/IMMOArts/Characters/Player_new/Player/Common/EmptyHand/Maya/player_actor_rig.ma
文件已保存: C:/YourMayaFiles_new/scene1.ma
文件处理完成

处理文件 2/15: C:/YourMayaFiles/scene2.ma
文件不包含目标引用，跳过

==================================================
处理摘要
==================================================
总文件数: 15
成功处理: 8
处理失败: 0

成功处理的文件:
  C:/YourMayaFiles/scene1.ma -> C:/YourMayaFiles_new/scene1.ma
  C:/YourMayaFiles/scene3.ma -> C:/YourMayaFiles_new/scene3.ma
  ...
```

## 注意事项

### 使用前准备

1. **备份数据**: 虽然工具不会修改原文件，但建议先备份重要数据
2. **检查路径**: 确保新引用路径的文件确实存在
3. **Maya版本**: 确保在支持的Maya版本中运行
4. **磁盘空间**: 确保有足够的磁盘空间存储处理后的文件

### 性能考虑

- 处理大量文件时可能需要较长时间
- 每个文件都需要在Maya中打开和保存，会消耗内存
- 建议分批处理大量文件

### 错误处理

- 如果某个文件无法打开，会跳过并记录错误
- 如果引用替换失败，会记录错误但继续处理其他文件
- 所有错误都会在最终报告中显示

## 自定义配置

如果需要处理不同的引用路径，可以修改 `BatchReferenceReplacer` 类的属性：

```python
replacer = BatchReferenceReplacer()
replacer.old_reference_path = "您的旧路径"
replacer.new_reference_path = "您的新路径"
```

## 故障排除

### 常见问题

1. **"模块未找到"错误**
   - 检查Python路径设置是否正确
   - 确保文件在正确的目录中

2. **"文件无法打开"错误**
   - 检查Maya文件是否损坏
   - 确保有足够的内存
   - 检查文件权限

3. **"引用替换失败"错误**
   - 检查新引用文件是否存在
   - 检查文件路径格式是否正确
   - 确保引用文件可以正常加载

4. **"目录创建失败"错误**
   - 检查磁盘空间
   - 检查目录权限
   - 确保路径格式正确

### 调试模式

如果需要更详细的调试信息，可以在代码中添加更多的print语句或使用Maya的日志系统。

## 扩展功能

这个工具可以很容易地扩展来处理其他类型的批量操作：

- 批量替换材质
- 批量修改对象属性
- 批量导出/导入操作
- 批量场景清理

## 技术细节

- 使用Maya Python API进行文件操作
- 支持.ma和.mb文件格式
- 保持原有的目录结构
- 使用Path对象进行跨平台路径处理
