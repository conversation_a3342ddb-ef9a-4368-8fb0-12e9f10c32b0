# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2022-10-10 15:52:15
# @Last Modified by:   <PERSON>
# @Last Modified time: 2023-08-29 16:02:00
from lsr.maya.nodezoo.node import Node
import maya.cmds as cmds
import os 
import maya.OpenMaya as om
constrain_objs = ['Root',
                 'pelvis',
                 'spine_01',
                 'spine_02',
                 'spine_03',
                 'spine_04',
                 'clavicle_l',
                 'upperarm_l',
                 'neck_01',
                 'neck_02',
                 'head',
                 'clavicle_r',
                 'upperarm_r']

def reference_head_rig():
    '''参考表情rig 到动画场景
    '''    
    select_obj = cmds.ls(sl=True)
    if len(select_obj) == 1:
        chr_obj = Node(select_obj[0])
        if chr_obj.is_referenced:
            close_head_vis(chr_obj)
            reference_path = cmds.referenceQuery(chr_obj, filename=True)
            chr_namespace = chr_obj.get_name_space()
            if 'Body' in reference_path:
                chr_base_path = os.path.dirname(reference_path)
                head_rig_path = os.path.join(chr_base_path, 'Head')
            else:
                head_rig_path = os.path.dirname(reference_path)

            open_path =  cmds.fileDialog2(fm=1, ff="Filtered Files (*.ma)", dialogStyle=2, startingDirectory=head_rig_path) or None

            if open_path:
                facial_namespace = os.path.basename(open_path[0]).split('.')[0]
                cmds.file(open_path[0], reference=True, namespace=facial_namespace)
                constraint_faciel_joints(chr_namespace, facial_namespace)
        else:
            om.MGlobal.displayError('当前选择的物体不是参考物体，请选择一个角色的参考物体')
    else:
        om.MGlobal.displayError(u'请选择角色的一个控制器')


def constraint_faciel_joints(chr_namespace, facial_namespace):
    '''约束root-head

    :param chr_namespace: 角色的namespace
    :type chr_namespace: str()
    :param facial_namespace: 表情的namespace
    :type facial_namespace: str()
    '''    
    for obj in constrain_objs:
        chr_joint = '%s:%s' % (chr_namespace, obj)
        facial_joint = '%s:%s' % (facial_namespace, obj)
        if cmds.objExists(chr_joint) and cmds.objExists(facial_joint):
            constrain = cmds.parentConstraint(chr_joint, facial_joint)[0]
            cmds.setAttr('%s.hiddenInOutliner'%constrain, 1)
        else:
            
            om.MGlobal.displayError('当前场景内部不存在 %s'%(chr_joint, facial_joint) )


def close_head_vis(chr_obj):
    '''关闭RIG组上的Head_Vis属性
    :param chr_obj: 选中的控制器
    :type chr_obj: nodezoo
    '''
    head_attr = 'Head_Vis'
    RIG_group = chr_obj.get_root_parent()
    if head_attr in cmds.listAttr(RIG_group):
        RIG_group.set_attr(head_attr,0)
