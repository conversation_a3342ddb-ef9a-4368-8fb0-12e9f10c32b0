from lsr.protostar.lib import ActionLibrary as alib


def QX_anim_export(*args, **kwargs):
    """
    Animation FBX Export Graph
    Args:
        *args ():
        **kwargs ():

    Returns:
        ActionGraph
    """
    alib.refresh()

    project = kwargs.get('project', None)
    if not project:
        return

    project_data = kwargs.get('project_data', dict())

    exp_body = kwargs.get('exp_body', True)
    exp_face = kwargs.get('exp_face', True)
    exp_prop = kwargs.get('exp_prop', True)
    exp_prop_origin = kwargs.get('exp_prop_origin', True)
    exp_end = kwargs.get('exp_end', False)
    exp_files = kwargs.get('exp_files', [])
    exp_mesh = kwargs.get('exp_meshes', False)

    translate = kwargs.get("translate", False)
    translateXYZ = kwargs.get("translateXYZ", [])
    rotate = kwargs.get("rotate", False)
    rotateXYZ = kwargs.get("rotateXYZ", [])
    re_animation_objects = kwargs.get("re_animation_objects", [])
    is_root_motion = kwargs.get("is_root_motion", False)
    is_stop_frame = kwargs.get("is_stop_frame", False)
    is_body_at_origin_point = kwargs.get("is_body_at_origin", False)

    is_repair_root_rotation = kwargs.get("is_repair_root_rotation", False)
    startAngle = kwargs.get('startAngle', 0)
    endAngle = kwargs.get('endAngle', 0)
    repairAxis = kwargs.get("repairAxis", "y")
    startFrame = kwargs.get("startFrame")
    endFrame = kwargs.get("endFrame")
    is_repair_frame = kwargs.get("is_repair_frame", False)

    if not exp_files:
        raise RuntimeError('export animation files is not define.')

    graph = alib.create_graph(name='exp_graph')

    # add an iterator parameter to make this graph iterable
    iter_param = graph.add_dynamic_param('iter', name='iter_param')

    # set the values to iterate over
    iter_param.value = exp_files

    # create an action in the iterator graph and connect to the iter parameter
    action_name = project_data[project]['exp_anim']
    action = alib.create_action(
        action_name, name='anim_exp_act', graph=graph)
    action.export_body.value = exp_body
    action.export_face.value = exp_face
    action.export_prop.value = exp_prop
    action.export_prop_origin.value = exp_prop_origin
    if project == "QX-Game":
        action.translate.value = translate
        action.translateXYZ.value = translateXYZ
        action.rotate.value = rotate
        action.rotateXYZ.value = rotateXYZ
        action.re_animation_objects.value = re_animation_objects
        action.is_root_motion.value = is_root_motion
        action.is_stop_frame.value = is_stop_frame
        action.is_body_at_origin_point.value = is_body_at_origin_point
        action.is_repair_root_rotation.value = is_repair_root_rotation
        action.startAngle.value = startAngle
        action.endAngle.value = endAngle
        action.repairAxis.value = repairAxis
        action.startFrame.value = startFrame
        action.endFrame.value = endFrame
        action.is_repair_frame.value = is_repair_frame

    if hasattr(action, 'exp_end'):
        action.export_end.value = exp_end
    if hasattr(action, 'export_meshes'):
        action.export_meshes.value = exp_mesh
    iter_param >> action.file_name

    # execute the iterator graph.
    # each time with a different file_name value.
    graph.execute()

    return graph


def QX_anim_export_batch(exportDate: dict, *args, **kwargs):

    alib.refresh()
    graph = alib.create_graph(name='exp_graph')
    # add an iterator parameter to make this graph iterable
    iter_param = graph.add_dynamic_param('iter', name='iter_param')

    # set the values to iterate over
    iter_param.value = exportDate.keys()
    actions = []
    for file, data in exportDate.items():
        data["exp_files"] = file
        project = data.get('project', None)
        if not project:
            return

        project_data = data.get('project_data', dict())

        exp_body = data.get('exp_body', True)
        exp_face = data.get('exp_face', True)
        exp_prop = data.get('exp_prop', True)
        exp_prop_origin = data.get('exp_prop_origin', True)
        exp_end = data.get('exp_end', False)
        exp_files = data.get('exp_files', [])
        exp_mesh = data.get('exp_meshes', False)

        translate = data.get("translate", False)
        translateXYZ = data.get("translateXYZ", [])
        rotate = data.get("rotate", False)
        rotateXYZ = data.get("rotateXYZ", [])
        re_animation_objects = data.get("re_animation_objects", [])
        is_root_motion = data.get("is_root_motion", False)
        is_stop_frame = data.get("is_stop_frame", False)
        is_body_at_origin_point = data.get("is_body_at_origin", False)

        is_repair_root_rotation = data.get("is_repair_root_rotation", False)
        startAngle = data.get('startAngle', 0)
        endAngle = data.get('endAngle', 0)
        repairAxis = data.get("repairAxis", "y")
        startFrame = data.get("startFrame")
        endFrame = data.get("endFrame")
        is_repair_frame = data.get("is_repair_frame", False)

        # create an action in the iterator graph and connect to the iter parameter
        action_name = project_data[project]['exp_anim']
        action = alib.create_action(
            action_name, name='anim_exp_act', graph=graph)
        action.export_body.value = exp_body
        action.export_face.value = exp_face
        action.export_prop.value = exp_prop
        action.export_prop_origin.value = exp_prop_origin
        if project == "QX-Game":
            action.translate.value = translate
            action.translateXYZ.value = translateXYZ
            action.rotate.value = rotate
            action.rotateXYZ.value = rotateXYZ
            action.re_animation_objects.value = re_animation_objects
            action.is_root_motion.value = is_root_motion
            action.is_stop_frame.value = is_stop_frame
            action.is_body_at_origin_point.value = is_body_at_origin_point
            action.is_repair_root_rotation.value = is_repair_root_rotation
            action.startAngle.value = startAngle
            action.endAngle.value = endAngle
            action.repairAxis.value = repairAxis
            action.startFrame.value = startFrame
            action.endFrame.value = endFrame
            action.is_repair_frame.value = is_repair_frame

        if hasattr(action, 'exp_end'):
            action.export_end.value = exp_end
        if hasattr(action, 'export_meshes'):
            action.export_meshes.value = exp_mesh
        action.file_name.value = file
        actions.append(action)
    graph.execute()
    return graph