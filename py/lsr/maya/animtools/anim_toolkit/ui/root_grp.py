""" Root motion group box. """

from functools import partial

import maya.cmds as cmds
import maya.OpenMaya as OpenMaya

from Qt import QtWidgets

import lsr.maya.rig.rig_global as rg

from lsr.maya.animtools.anim_toolkit.char_utils import char_edit
from lsr.qt.core.widgets.mobu_widget import MB_Button, MB_Checkbox
from lsr.maya.animtools.anim_toolkit.ui.interact_utils import get_rig_global

CONSTRAINT_TYPES = [
    "aimConstraint",
    "geometryConstraint",
    "normalConstraint",
    "orientConstraint",
    "parentConstraint",
    "pointConstraint",
    "pointOnPolyConstraint",
    "poleVectorConstraint",
    "scaleConstraint",
    "tangentConstraint",
    "pairBlend"
]


class Root_Motion_GroupBox(QtWidgets.QGroupBox):
    """
    QGroupBox for root motion.
    """
    def __init__(self, parent=None):
        super(Root_Motion_GroupBox, self).__init__(parent)
        self._setup_ui()
        self.create_connections()

    def _setup_ui(self):
        self._layout = QtWidgets.QVBoxLayout(self)
        self.setLayout(self._layout)

        _spacer_item = QtWidgets.QSpacerItem(40, 20,
                                            QtWidgets.QSizePolicy.Expanding,
                                            QtWidgets.QSizePolicy.Minimum)
        self._layout.addItem(_spacer_item)

        self.rot_cb_btn = QtWidgets.QCheckBox(self)
        self.rot_cb_btn.setText("Create Rotate Animation")
        self._layout.addWidget(self.rot_cb_btn)

        self.rot_param_widget = QtWidgets.QGroupBox(self)
        self.rot_param_widget.setTitle("Axis Edit")
        self.rot_wid_layout = QtWidgets.QVBoxLayout(self.rot_param_widget)

        aim_label = QtWidgets.QLabel("Aim Axis:")
        self.rot_wid_layout.addWidget(aim_label)

        aim_pos_h_layout = QtWidgets.QHBoxLayout()
        self.aim_pos_x_rbt = QtWidgets.QRadioButton("X")
        self.aim_pos_y_rbt = QtWidgets.QRadioButton("Y")
        self.aim_pos_z_rbt = QtWidgets.QRadioButton("Z")
        aim_pos_h_layout.addWidget(self.aim_pos_x_rbt)
        aim_pos_h_layout.addWidget(self.aim_pos_y_rbt)
        aim_pos_h_layout.addWidget(self.aim_pos_z_rbt)
        self.rot_wid_layout.addLayout(aim_pos_h_layout)

        aim_neg_h_layout = QtWidgets.QHBoxLayout()
        self.aim_neg_x_rbt = QtWidgets.QRadioButton("-X")
        self.aim_neg_y_rbt = QtWidgets.QRadioButton("-Y")
        self.aim_neg_z_rbt = QtWidgets.QRadioButton("-Z")
        aim_neg_h_layout.addWidget(self.aim_neg_x_rbt)
        aim_neg_h_layout.addWidget(self.aim_neg_y_rbt)
        aim_neg_h_layout.addWidget(self.aim_neg_z_rbt)

        self.rot_wid_layout.addLayout(aim_neg_h_layout)

        self.aim_button_group = QtWidgets.QButtonGroup(self)
        self.aim_button_group.addButton(self.aim_pos_x_rbt, 0)
        self.aim_button_group.addButton(self.aim_pos_y_rbt, 1)
        self.aim_button_group.addButton(self.aim_pos_z_rbt, 2)
        self.aim_button_group.addButton(self.aim_neg_x_rbt, 3)
        self.aim_button_group.addButton(self.aim_neg_y_rbt, 4)
        self.aim_button_group.addButton(self.aim_neg_z_rbt, 5)

        # add a spacer
        _spacer_item = QtWidgets.QSpacerItem(40, 20,
                                             QtWidgets.QSizePolicy.Expanding,
                                             QtWidgets.QSizePolicy.Minimum)
        self.rot_wid_layout.addItem(_spacer_item)

        up_axis_label = QtWidgets.QLabel("Object Up Axis(World Up):")
        self.rot_wid_layout.addWidget(up_axis_label)

        up_pos_h_layout = QtWidgets.QHBoxLayout()
        self.up_pos_x_rbt = QtWidgets.QRadioButton("X")
        self.up_pos_y_rbt = QtWidgets.QRadioButton("Y")
        self.up_pos_z_rbt = QtWidgets.QRadioButton("Z")
        up_pos_h_layout.addWidget(self.up_pos_x_rbt)
        up_pos_h_layout.addWidget(self.up_pos_y_rbt)
        up_pos_h_layout.addWidget(self.up_pos_z_rbt)
        self.rot_wid_layout.addLayout(up_pos_h_layout)

        up_neg_h_layout = QtWidgets.QHBoxLayout()
        self.up_neg_x_rbt = QtWidgets.QRadioButton("-X")
        self.up_neg_y_rbt = QtWidgets.QRadioButton("-Y")
        self.up_neg_z_rbt = QtWidgets.QRadioButton("-Z")
        up_neg_h_layout.addWidget(self.up_neg_x_rbt)
        up_neg_h_layout.addWidget(self.up_neg_y_rbt)
        up_neg_h_layout.addWidget(self.up_neg_z_rbt)

        self.rot_wid_layout.addLayout(up_neg_h_layout)

        self.up_button_group = QtWidgets.QButtonGroup(self)
        self.up_button_group.addButton(self.up_pos_x_rbt, 0)
        self.up_button_group.addButton(self.up_pos_y_rbt, 1)
        self.up_button_group.addButton(self.up_pos_z_rbt, 2)
        self.up_button_group.addButton(self.up_neg_x_rbt, 3)
        self.up_button_group.addButton(self.up_neg_y_rbt, 4)
        self.up_button_group.addButton(self.up_neg_z_rbt, 5)

        # self.rotate_root_btn = MB_Button(self)
        # self.rotate_root_btn.setFixedHeight(50)
        # self.rotate_root_btn.setText('Auto Rotate Root')
        # self.rot_wid_layout.addWidget(self.rotate_root_btn)

        self._layout.addWidget(self.rot_param_widget)

        self.root_btn = MB_Button(self)
        self.root_btn.setFixedHeight(50)
        self.root_btn.setText('Create Root Motion')
        self._layout.addWidget(self.root_btn)

        hbox = QtWidgets.QHBoxLayout()
        self.og_CB = QtWidgets.QCheckBox(self)
        self.og_CB.setText("Keep on Ground")
        self.og_CB.setChecked(True)
        hbox.addWidget(self.og_CB)

        self.fs_CB = QtWidgets.QCheckBox(self)
        self.fs_CB.setText("Follow Selected")
        hbox.addWidget(self.fs_CB)

        self.root_CB = QtWidgets.QCheckBox(self)
        self.root_CB.setText("All")
        hbox.addWidget(self.root_CB)

        hbox.setStretch(0, 1)
        hbox.setStretch(1, 1)
        hbox.setStretch(1, 1)
        self._layout.addLayout(hbox)

        _spacer_item = QtWidgets.QSpacerItem(40, 20,
                                             QtWidgets.QSizePolicy.Expanding,
                                             QtWidgets.QSizePolicy.Minimum)
        self._layout.addItem(_spacer_item)

        fix_hbox = QtWidgets.QHBoxLayout()
        degree_Label = QtWidgets.QLabel(self)
        degree_Label.setText('Degrees')
        fix_hbox.addWidget(degree_Label)

        self.degree_DSB = QtWidgets.QDoubleSpinBox(self, minimum=-999, maximum=999)
        fix_hbox.addWidget(self.degree_DSB)

        self.degree_btn = MB_Button(self)
        self.degree_btn.setFixedHeight(50)
        self.degree_btn.setText('Rotate')
        fix_hbox.addWidget(self.degree_btn)

        fix_hbox.setStretch(0, 1)
        fix_hbox.setStretch(1, 2)
        fix_hbox.setStretch(2, 3)
        self._layout.addLayout(fix_hbox)

        self.rep_btn = MB_Button(self)
        self.rep_btn.setFixedHeight(50)
        self.rep_btn.setText('Re-Position To Zero of WorldSpace')
        self._layout.addWidget(self.rep_btn)

    def create_connections(self, *args, **kwargs):
        """create connections for all buttons"""
        self.root_btn.clicked.connect(partial(self.create_root_motion))
        self.degree_btn.clicked.connect(partial(self.rotate_root))
        self.rep_btn.clicked.connect(partial(self.reposition_root))
        self.rot_cb_btn.toggled.connect(partial(self.change_rot_wid_enable))

    def change_rot_wid_enable(self, state, *args, **kwargs):
        """change rot wid enable"""
        self.rot_param_widget.setVisible(state)

    def create_root_motion(self, *args, **kwargs):
        """create root motion"""
        print('create root motion ...')
        rotate_modify = self.rot_cb_btn.isChecked()
        aim_axis = self.aim_button_group.button(self.aim_button_group.checkedId()).text().lower()
        up_axis = self.up_button_group.button(self.up_button_group.checkedId()).text().lower()
        cmd_kwargs = {
            'rotate_modify': rotate_modify,
            'aim_axis': aim_axis,
            'up_axis': up_axis
        }

        rg_node = get_rig_global()
        if not rg_node:
            return

        all_ctrls = rg_node.get_ctrls()
        if not all_ctrls:
            cmds.warning('No anim controls found.')
            return

        constrained_ctrls = []
        for ctrl in all_ctrls:
            ct_nodes = ctrl.list_connections(
                                  source=True,
                                  destination=False,
                                  plugs=False) or []

            constraints = [conn for conn in ct_nodes if cmds.nodeType(conn) in CONSTRAINT_TYPES]

            if constraints:
                constrained_ctrls.append(ctrl)
        if constrained_ctrls:
            cmds.confirmDialog(title='Warning',
                               message='some controller has been constrained, please delete them first',
                               button=['OK'], defaultButton='OK', icon='warning')
            cmds.select(constrained_ctrls, replace=True)
            return

        char_edit.create_root_motion(rg_node=rg_node, **cmd_kwargs)

    def rotate_root(self):
        """rotate root"""
        print('rotate root ...')
        degree = self.degree_DSB.value()
        axis = cmds.upAxis(query=True, axis=True)
        if axis == 'y':
            vector = OpenMaya.MVector(0, degree, 0)
        elif axis == 'z':
            vector = OpenMaya.MVector(0, 0, degree)
        else:
            raise ValueError('Unsupported up axis: {}'.format(axis))
        rg_node = get_rig_global()
        if not rg_node:
            return
        char_edit.rotate_character(rg_node=rg_node, vector=vector)

    def reposition_root(self):
        """reposition root ..."""
        rg_node = get_rig_global()
        if not rg_node:
            return
        char_edit.reset_character(rg_node=rg_node)
