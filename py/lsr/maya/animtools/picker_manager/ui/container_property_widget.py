""" This module contains the ContainerPropertyWidget class."""

# Import local modules
import lsr.maya.animtools.picker_manager.const as const
import lsr.maya.animtools.picker_manager.ui.attribute_widget as paw
from lsr.maya.animtools.picker_manager.ui.picker_property_widget import PickerPropertyWidget

class ContainerPropertyWidget(PickerPropertyWidget):
    """This widget controls the property of the container items."""

    def __init__(self, parent = None):
        super(ContainerPropertyWidget, self).__init__(parent=parent)

    def add_property_widgets(self):
        """Add the property widgets to the layout."""
        image_widget = paw.FileAttrEditor([], const.IMAGE_PATH_ATTR_NAME, parent=self)
        self.property_widgets_layout.addWidget(image_widget)
        self.picker_property_widgets.append(image_widget)

        widget = paw.NumericAttrEditor(
            [],
            "alpha",
            parent=self,
            default_value=1.0,
        )
        self.property_widgets_layout.addWidget(widget)
        self.picker_property_widgets.append(widget)

        widget = paw.NumericAttrEditor(
            [],
            "width",
            parent=self,
            default_value=const.BG_SCENE_SIZE,
            default_min=const.MINIMUM_SCENE_SIZE,
            default_max=const.BG_SCENE_SIZE,
        )
        self.property_widgets_layout.addWidget(widget)
        self.picker_property_widgets.append(widget)

        widget = paw.NumericAttrEditor(
            [],
            "height",
            parent=self,
            default_value=const.BG_SCENE_SIZE,
            default_min=const.MINIMUM_SCENE_SIZE,
            default_max=const.BG_SCENE_SIZE,
        )
        self.property_widgets_layout.addWidget(widget)
        self.picker_property_widgets.append(widget)

        color_widget = paw.ColorAttributeEditor(
            [],
            "containerTextColor",
            parent=self,
        )
        self.property_widgets_layout.addWidget(color_widget)
        self.picker_property_widgets.append(color_widget)

    def update_children_info(self, data):
        """
        Update the information in the container's children information attribute
        Args:
            data Tuple[Any, str]: The data to update

        Returns:
            None
        """
        if not data:
            return
        node, attr = data
        for item in self.items:
            container_node = item.node
            info = container_node.get_children_information()
            node_name = node.name.split(":")[0]
            info[node_name] = node.export()
            container_node.set_children_information(info)

    def update_picker_ui_info(self, data):
        """
        Update the UI position, zoom factor info in the container's ui information attribute
        Args:
            data Tuple[Any, str]: The data to update

        Returns:
            None
        """
        container = self.items[0].node
        container_ui_info = container.get_picker_ui_information()
        if not container_ui_info:
            container_ui_info = {
                "center": (),
                "zoom_scale": 1,
            }
        if isinstance(data, tuple):
            container_ui_info["center"] = data
        elif isinstance(data, float):
            container_ui_info["zoom_scale"] = data

        container.set_picker_ui_information(container_ui_info)
