# -*- coding: utf-8 -*-

import os
import json
import re

from maya import mel, cmds
import maya.OpenMaya as OpenMaya
import maya.OpenMayaAnim as OpenMayaAnim

from lsr.maya.nodezoo.node import DependencyNode
from lsr.maya.nodezoo.node import HIKCharacterNode
from lsr.maya.nodezoo.node import TransformConstraint


class JointAnimExport(object):
    """
    Skeleton, humanIK, skeletal animation data import and export json files
    """
    def __init__(self):
        self._is_add_namespace = True
        self._namespace = '_tmp_hik_jot'
        self._hik_joint_namespace = None
        self._tmp_hik_node = ''
        self._joint_visibility = True
        self._all_namespace_list = []
        self._joint_attr_info = dict()
        self._hik_attr_info = dict()
        self._joint_hik_map = dict()
        self._joint_matrix_map = dict()
        self._hik_joint_map = dict()
        self._hik_full_joint_map = dict()
        self._joint_tree = dict()
        self._anim_joint_info = dict()
        self._anim_frame_range = []
        self._anim_frame_rate = None
        self._default_frame_rate = 30.0
        self._root_joint = ''
        self._current_hik_node = ''
        self._anim_attr_list = ['tx', 'ty', 'tz', 'rx', 'ry', 'rz', 'sx', 'sy', 'sz']
        self._double3_attr_list = [
            'translate', 'rotate', 'scale', 'jointOrient', 'rotateAxis', 'stiffness', 'preferredAngle',
            'rotatePivot', 'scalePivot', 'ghostColorPre', 'ghostColorPost'
        ]
        self._float_attr_list = [
            'rotateOrder', 'drawStyle', 'side', 'type', 'radius', 'drawLabel', 'ghosting', 'ghostingControl',
            'ghostPreSteps', 'ghostPostSteps', 'ghostStepSize', 'ghostRangeStart', 'ghostRangeEnd', 'ghostColorPreA',
            'ghostColorPostA'
        ]
        self._hik_double3_attr_list = [
            'T', 'R', 'S', 'RotateAxis', 'JointOrient', 'MinRLimit', 'MaxRLimit', 'MinRLimitEnable', 'MaxRLimitEnable',
        ]
        self._hik_float_attr_list = ['RotateOrder']
        self._hik_state_double3_attr_list = [
            'T', 'R', 'S', 'PreR', 'PostR', 'IS'
        ]
        self._hik_state_float_attr_list = ['ROrder', 'SC']
        self._hik_state_matrix_attr_list = ['PGX']

        # This switch is used to build the correct bone if a bone containing a duplicate name is recorded
        self._force_source = False
        self._new_joint_map = dict()
        self._full_new_joint_map = dict()
        self._new_joint_ctrl_map = dict()

    def _update_hik_attr_info(
            self, hik_node, hik_joint, attr_name, attr_value, hik_type='HIKCharacterNode', *args, **kwargs):
        """
        Update the hik attribute information

        Args:
            hik_node (str): hik node name
            hik_joint (str): hik joint name
            attr_name (str): attribute name
            attr_value (str): attribute value
            hik_type (str): hik node type

        Returns:
            None
        """
        hik_node = self._get_simple_node_name(hik_node)
        hik_node_info = self._hik_attr_info.get(hik_node)
        if not hik_node_info:
            hik_node_info = dict(
                {
                'HIKCharacterNode': {},
                'HIKState2SK': {}
                }
            )
            self._hik_attr_info[hik_node] = hik_node_info
        hik_joint_info = hik_node_info.get(hik_type).get(hik_joint)
        if not hik_joint_info:
            hik_joint_info = dict()
            hik_node_info[hik_type][hik_joint] = hik_joint_info
        hik_joint_info[attr_name] = attr_value

    def _get_hik_attr_value(self, hik_node, hik_joint, attr_name, hik_type='HIKCharacterNode', *args, **kwargs):
        """
        Get the hik attribute value

        Args:
            hik_node (str): hik node name
            hik_joint (str): hik joint name
            attr_name (str): attribute name
            hik_type (str): hik node type

        Returns:
            str: attribute value
        """
        hik_node = self._get_simple_node_name(hik_node)
        attr_value = None
        hik_node_info = self._hik_attr_info.get(hik_node, {}).get(hik_type)
        if not hik_node_info:
            return attr_value
        hik_joint_info = hik_node_info.get(hik_joint)
        if not hik_joint_info:
            return attr_value
        attr_value = hik_joint_info.get(attr_name)
        return attr_value

    def _update_attr_info(self, joint_name, attr_name, attr_value, *args, **kwargs):
        """
        Update the attribute information

        Args:
            joint_name (str): joint name
            attr_name (str): attribute name
            attr_value (str): attribute value

        Returns:
            None
        """
        joint_name = self._get_simple_node_name(joint_name)
        joint_info = self._joint_attr_info.get(joint_name)
        if not joint_info:
            joint_info = dict()
            self._joint_attr_info[joint_name] = joint_info
        joint_info[attr_name] = attr_value

    def _get_attr_value(self, joint_name, attr_name, *args, **kwargs):
        """
        Get the attribute value

        Args:
            joint_name ():
            attr_name ():

        Returns:

        """
        joint_name = self._get_simple_node_name(joint_name)
        attr_value = None
        joint_info = self._joint_attr_info.get(joint_name)
        if not joint_info:
            return attr_value
        attr_value = joint_info.get(attr_name)
        return attr_value

    def _update_anim_joint_info(self, joint_name, attr_name, attr_value, is_init=False, *args, **kwargs):
        """
        Update the animation joint information

        Args:
            joint_name ():
            attr_name ():
            attr_value ():
            is_init ():

        Returns:
            None
        """
        joint_name = self._get_simple_node_name(joint_name)
        anim_info = self._anim_joint_info.get(joint_name)
        if not anim_info or is_init:
            anim_info = dict({
                'tx': [], 'ty': [], 'tz': [], 'rx': [], 'ry': [], 'rz': [], 'sx': [], 'sy': [], 'sz': []
            })
            self._anim_joint_info[joint_name] = anim_info
        anim_info[attr_name].append(attr_value)

    def _get_anim_joint_value(self, joint_name, attr_name, *args, **kwargs):
        """
        Get the animation joint value

        Args:
            joint_name ():
            attr_name ():

        Returns:

        """
        joint_name = self._get_simple_node_name(joint_name)
        attr_value = None
        joint_info = self._anim_joint_info.get(joint_name)
        if not joint_info:
            return attr_value
        attr_value = joint_info.get(attr_name)
        return attr_value

    def _get_double3_attr(self, joint_name, *args, **kwargs):
        """
        Get the double3 attribute

        Args:
            joint_name ():

        Returns:

        """
        simple_jot = self._get_simple_node_name(joint_name)
        for attr in self._double3_attr_list:
            attr_name = '%s.%s' % (joint_name, attr)
            if not cmds.objExists(attr_name):
                continue
            try:
                attr_value = [
                    cmds.getAttr('%sX' % attr_name),
                    cmds.getAttr('%sY' % attr_name),
                    cmds.getAttr('%sZ' % attr_name),
                ]
            except:
                attr_value = cmds.getAttr(attr_name)
                if attr_value:
                    attr_value = attr_value[0]
            self._update_attr_info(simple_jot, attr, attr_value)
        return self._joint_attr_info.get(simple_jot)

    def _get_float_attr(self, joint_name, *args, **kwargs):
        """
        Get the float attribute

        Args:
            joint_name ():

        Returns:

        """
        simple_jot = self._get_simple_node_name(joint_name)
        for attr in self._float_attr_list:
            attr_name = '%s.%s' % (joint_name, attr)
            if not cmds.objExists(attr_name):
                continue
            attr_value = cmds.getAttr(attr_name)
            self._update_attr_info(simple_jot, attr, attr_value)
        return self._joint_attr_info.get(simple_jot)

    def _set_double3_attr(self, joint_name, new_joint, *args, **kwargs):
        """
        Set the double3 attribute

        Args:
            joint_name ():
            new_joint ():

        Returns:

        """
        for attr in self._double3_attr_list:
            attr_value = self._get_attr_value(joint_name, attr)
            if not attr_value:
                continue
            attr_name = '%s.%s' % (new_joint, attr)
            if not cmds.objExists(attr_name):
                continue
            try:
                cmds.setAttr('%s.%sX' % (new_joint, attr), attr_value[0])
                cmds.setAttr('%s.%sY' % (new_joint, attr), attr_value[1])
                cmds.setAttr('%s.%sZ' % (new_joint, attr), attr_value[2])
            except RuntimeError:
                cmds.setAttr('%s.%s' % (new_joint, attr), attr_value[0], attr_value[1], attr_value[2], type='double3')
        return True

    def _set_float_attr(self, joint_name, new_joint, *args, **kwargs):
        """
        Set the float attribute

        Args:
            joint_name ():
            new_joint ():

        Returns:

        """
        for attr in self._float_attr_list:
            attr_value = self._get_attr_value(joint_name, attr)
            if not attr_value:
                continue
            attr_name = '%s.%s' % (new_joint, attr)
            if not cmds.objExists(attr_name):
                continue
            cmds.setAttr('%s.%s' % (new_joint, attr), attr_value)
        return True

    def _get_hik_double3_attr(self, hik_node, hik_joint, hik_type='HIKCharacterNode', *args, **kwargs):
        """
        Get the hik double3 attribute

        Args:
            hik_node ():
            hik_joint ():
            hik_type ():

        Returns:

        """
        for attr in self._hik_double3_attr_list:
            attr_name = '%s.%s%s' % (hik_node, hik_joint, attr)
            if not cmds.objExists(attr_name):
                continue
            attr_value = [
                cmds.getAttr('%s.%s%sx' % (hik_node, hik_joint, attr)),
                cmds.getAttr('%s.%s%sy' % (hik_node, hik_joint, attr)),
                cmds.getAttr('%s.%s%sz' % (hik_node, hik_joint, attr)),
            ]
            self._update_hik_attr_info(hik_node, hik_joint, attr, attr_value)
        attr_name = '%s.OutputCharacterDefinition' % hik_node
        if cmds.objExists(attr_name):
            state_nodes = cmds.listConnections(attr_name, type='HIKState2SK')
            if state_nodes:
                for attr in self._hik_state_double3_attr_list:
                    attr_name = '%s.%s%s' % (state_nodes[0], hik_joint, attr)
                    if not cmds.objExists(attr_name):
                        continue
                    attr_value = [
                        cmds.getAttr('%s.%s%sx' % (state_nodes[0], hik_joint, attr)),
                        cmds.getAttr('%s.%s%sy' % (state_nodes[0], hik_joint, attr)),
                        cmds.getAttr('%s.%s%sz' % (state_nodes[0], hik_joint, attr)),
                    ]
                    self._update_hik_attr_info(hik_node, hik_joint, attr, attr_value, hik_type='HIKState2SK')
        return self._hik_attr_info.get(hik_node, {}).get(hik_type, {}).get(hik_joint)

    def _get_hik_float_attr(self, hik_node, hik_joint, hik_type='HIKCharacterNode', *args, **kwargs):
        """
        Get the hik float attribute

        Args:
            hik_node ():
            hik_joint ():
            hik_type ():

        Returns:

        """
        for attr in self._hik_float_attr_list:
            attr_name = '%s.%s%s' % (hik_node, hik_joint, attr)
            if not cmds.objExists(attr_name):
                continue
            attr_value = cmds.getAttr(attr_name)
            self._update_hik_attr_info(hik_node, hik_joint, attr, attr_value)
        attr_name = '%s.OutputCharacterDefinition' % hik_node
        if cmds.objExists(attr_name):
            state_nodes = cmds.listConnections(attr_name, type='HIKState2SK')
            if state_nodes:
                for attr in self._hik_state_float_attr_list + self._hik_state_matrix_attr_list:
                    attr_name = '%s.%s%s' % (state_nodes[0], hik_joint, attr)
                    if not cmds.objExists(attr_name):
                        continue
                    attr_value = cmds.getAttr(attr_name)
                    self._update_hik_attr_info(hik_node, hik_joint, attr, attr_value, hik_type='HIKState2SK')
        return self._hik_attr_info.get(hik_node, {}).get(hik_type, {}).get(hik_joint)

    @property
    def joint_attr_info(self, *args, **kwargs):
        """
        joint attribute information

        Returns:
            dict
        """
        return self._joint_attr_info

    @joint_attr_info.setter
    def joint_attr_info(self, value, *args, **kwargs):
        """
        joint attribute information

        Args:
            value (dict): joint attribute information

        Returns:
            None
        """
        self._joint_attr_info = value

    def get_attr_value(self, joint_name, *args, **kwargs):
        """
        Get the attribute value of the joint

        Args:
            joint_name ( str ): joint name

        Returns:
            dict
        """
        self._get_double3_attr(joint_name)
        self._get_float_attr(joint_name)

    def get_joint_matrix_map(self, *args, **kwargs):
        """
        Get the joint matrix map

        Returns:
            dict
        """
        return self._joint_matrix_map

    def get_joint_matrix_by_root(self, root_joint=None, *args, **kwargs):
        """
        Get the joint matrix map by root joint

        Args:
            root_joint ( str ): root joint name

        Returns:
            dict
        """
        if root_joint is None:
            root_joint = self._root_joint
        self._joint_matrix_map = {}
        all_joints = cmds.listRelatives(root_joint, allDescendents=True, fullPath=True) or []
        all_joints.append(root_joint)
        for jot in all_joints:
            try:
                _matrix = cmds.xform(jot, query=True, worldSpace=True, matrix=True)
                simple_jot = self._get_simple_node_name(jot)
                if jot == root_joint:
                    simple_jot = simple_jot.split('|')[-1] if '|' in simple_jot else simple_jot
                self._joint_matrix_map[simple_jot] = _matrix
            except Exception as e:
                continue
        return self._joint_matrix_map

    def set_joint_matrix_by_root(self, root_joint, joint_matrix_map=None, *args, **kwargs):
        """
        Set the joint matrix map by root joint

        Args:
            root_joint ( str ): root joint name
            joint_matrix_map ( dict ): joint matrix map

        Returns:
            None
        """
        if joint_matrix_map is None:
            joint_matrix_map = self._joint_matrix_map
        all_joints = cmds.listRelatives(root_joint, allDescendents=True, fullPath=True) or []
        all_joints.append(root_joint)
        all_joints.sort(reverse=False)
        for jot in all_joints:
            for joint_name, matrix in joint_matrix_map.items():
                if jot.endswith(joint_name):
                    try:
                        cmds.xform(jot, worldSpace=True, matrix=matrix)
                        break
                    except Exception as e:
                        break

    def set_joint_matrix_by_hik_node(self, hik_node, joint_matrix_map=None, *args, **kwargs):
        """
        Set the joint matrix map by hik node

        Args:
            hik_node ( str ): hik node name
            joint_matrix_map ( dict ): joint matrix map

        Returns:
            None
        """
        hik_joint_map = dict()
        connect_list = cmds.listConnections(
            hik_node, plugs=True, source=True, connections=True, destination=True) or []
        for i in range(len(connect_list)):
            obj_name = connect_list[i].split('.')[0]
            if cmds.objExists(obj_name) and cmds.objectType(obj_name) == 'joint' and i > 0:
                hik_attr = connect_list[i - 1].split('.')[-1]
                hik_joint_map[hik_attr] = obj_name
        root_joint = self.get_root_joint(hik_joint_map=hik_joint_map)
        self.set_joint_matrix_by_root(root_joint, joint_matrix_map=joint_matrix_map)

    def set_attr_value(self, joint_name, new_joint, *args, **kwargs):
        """
        Set the attribute value of the joint

        Args:
            joint_name ( str ): joint name
            new_joint ( str ): new joint name

        Returns:
            None
        """
        self._set_double3_attr(joint_name, new_joint)
        self._set_float_attr(joint_name, new_joint)

    @staticmethod
    def _get_simple_node_name(node_name, *args, **kwargs):
        """
        Get the simple node name

        Args:
            node_name (str): node name

        Returns:
            str
        """
        node_names = cmds.ls(node_name)
        if node_names:
            node_name = node_names[0]
        tmp_name = node_name.split('|')[-1] if '|' in node_name else node_name
        namespace = ':'.join(tmp_name.split(':')[:-1]) + ':' if ':' in tmp_name else ''
        if namespace:
            node_name = node_name.replace(namespace, '')
        return node_name

    def get_node_tree_dict(self, node_name, tree_dict=None, filter_nodes=None, *args, **kwargs):
        """
        Get the node tree dict

        Args:
            node_name ():
            tree_dict ():
            filter_nodes ():

        Returns:

        """
        if tree_dict is None:
            tree_dict = dict()
        childes = cmds.listRelatives(node_name, f=1)
        if childes and filter_nodes:
            childes = [child for child in childes if child not in filter_nodes]
        node_name = self._get_simple_node_name(node_name)
        if not childes:
            return node_name
        tree_dict[node_name] = [self.get_node_tree_dict(child, filter_nodes=filter_nodes) for child in childes]
        return tree_dict

    def _is_exist_namespace(self, *args, **kwargs):
        """
        Is there a namespace

        Returns:
            bool: True if there is a namespace; False otherwise.
        """
        if ':%s' % self._namespace not in self._all_namespace_list:
            self._all_namespace_list = cmds.namespaceInfo(":", recurse=True, listOnlyNamespaces=True, absoluteName=True)
        if ':%s' % self._namespace in self._all_namespace_list:
            return True
        return False

    def _add_namespace(self, node_name, *args, **kwargs):
        """
        Add namespace

        Args:
            node_name ():

        Returns:
            str
        """
        if not self._is_exist_namespace():
            self._namespace = cmds.namespace(add=self._namespace, parent=":")
        return '%s:%s' % (self._namespace, node_name)

    def _remove_namespace(self, *args, **kwargs):
        """
        Remove namespace

        Returns:
            None
        """
        if self._is_exist_namespace():
            cmds.namespace(removeNamespace=self._namespace, mergeNamespaceWithParent=True)
        self._all_namespace_list = []

    def get_new_tmp_namespace(self, *args, **kwargs):
        """
        Get the new tmp namespace

        Returns:
            str
        """
        tmp_namespace = '_tmp_hik_jot'
        if not self._is_exist_namespace():
            return self._namespace
        i = 1
        while True:
            self._namespace = '%s_%s' % (tmp_namespace, i)
            if not self._is_exist_namespace():
                break
            i += 1
        return self._namespace

    def create_joint(self, joint_name, hik_node, parent_node=None, *args, **kwargs):
        """
        Create a joint

        Args:
            joint_name ():
            hik_node ():
            parent_node ():

        Returns:

        """
        _p = (0, 0, 0)
        _r = None
        hik_jot = ''
        if parent_node:
            cmds.select(parent_node, r=True)
            _p = cmds.xform(parent_node, q=1, t=1, ws=1)
        else:
            cmds.select(cl=True)
        if joint_name in self._joint_hik_map:
            hik_jot = self._joint_hik_map[joint_name]
            _p = self._get_hik_attr_value(hik_node, hik_jot, 'T')
            # _r = self._get_hik_attr_value(hik_node, self._joint_hik_map[joint_name], 'R', hik_type='HIKState2SK')
            _r = self._get_hik_attr_value(hik_node, hik_jot, 'R')
        new_joint = joint_name.split('|')[-1]
        if self._is_add_namespace:
            new_joint = self._add_namespace(new_joint)
        new_joint = cmds.joint(name=new_joint)
        # self._full_new_joint_map
        cmds.setAttr('%s.visibility' % new_joint, self._joint_visibility)

        # self._force_source is a switch.
        # If turned on, the source data skeleton will be forcibly restored,
        # even if the source data skeleton is the wrong skeleton with duplicate names.
        # Otherwise, a correct skeleton without duplicate names will be built.
        if not self._force_source and '|' in new_joint:
            new_joint = cmds.rename(new_joint, new_joint.replace('|', '_'))
        self._new_joint_map[joint_name] = new_joint
        self.set_attr_value(joint_name, new_joint)
        cmds.setAttr('%s.rotate' % new_joint, 0, 0, 0, type='double3')
        if _r:
            cmds.xform(new_joint, absolute=True, rotation=_r, worldSpace=1)
            # cmds.setAttr('%s.r' % new_joint, _r[0], _r[1], _r[2], type='double3')
        if hik_jot in ['Reference', 'Hips']:
            cmds.xform(new_joint, absolute=True, translation=_p, worldSpace=1)
        return new_joint

    def create_node_tree(self, tree_dict, hik_node, parent_node=None, *args, **kwargs):
        """
        Create a node tree

        Args:
            tree_dict (dict): node tree dict
            hik_node (str): hik node name
            parent_node (str): parent node name

        Returns:
            None
        """
        for _key, _value in tree_dict.items():
            parent_node = self.create_joint(_key, hik_node, parent_node)
            for node_name in _value:
                if isinstance(node_name, dict):
                    self.create_node_tree(node_name, hik_node, parent_node)
                    continue
                self.create_joint(node_name, hik_node, parent_node)

    def _get_hik_joint_namespace(self, obj_name, *args, **kwargs):
        """
        Get the hik joint namespace

        Args:
            obj_name (str): object name

        Returns:
            None
        """
        if self._hik_joint_namespace is None:
            self._hik_joint_namespace = ':'.join(obj_name.split(':')[:-1]) if ':' in obj_name else ''

    def get_hik_is_valid(self, hik_node, *args, **kwargs):
        """
        hik_node if valid
        Args:
            hik_node (str):

        Returns:
            bool
        """
        is_locked = bool(mel.eval('hikIsDefinitionLocked("%s")' % hik_node))
        if not is_locked:
            return False
        is_validate = bool(mel.eval('hikValidateSkeleton("%s");' % (hik_node)))
        if not is_validate:
            return False
        hik_joint_map = self.get_joint_hik_map(hik_node)
        root_joint = hik_joint_map.get('Reference')
        if not root_joint:
            root_joint = hik_joint_map.get('Hips')
        if not root_joint:
            return False
        return True

    def get_joint_hik_map(self, hik_node, *args, **kwargs):
        """
        Get the joint hik map

        Args:
            hik_node ():

        Returns:

        """
        hik_joint_map = dict()
        connect_list = cmds.listConnections(
            hik_node, plugs=True, source=True, connections=True, destination=True) or []
        for i in range(len(connect_list)):
            obj_name = connect_list[i].split('.')[0]
            if cmds.objExists(obj_name) and cmds.objectType(obj_name) == 'joint' and i > 0:
                hik_attr = connect_list[i - 1].split('.')[-1]
                hik_joint_map[hik_attr] = obj_name
                self._get_hik_joint_namespace(obj_name)
                obj_name = self._get_simple_node_name(obj_name)
                self._joint_hik_map[obj_name] = hik_attr
                self._hik_joint_map[hik_attr] = obj_name
        return hik_joint_map

    def get_root_joint(self, hik_joint_map=None, *args, **kwargs):
        if not hik_joint_map:
            hik_joint_map = self._hik_full_joint_map
        root_joint = hik_joint_map.get('Reference')
        if not root_joint:
            hip_joint = hik_joint_map.get('Hips')
            if not hip_joint:
                return None
            if not cmds.objExists(hip_joint):
                hip_joint = '%s:%s' % (self._namespace, self._get_simple_node_name(hip_joint))
            if cmds.objExists(hip_joint):
                parent_jots = cmds.listRelatives(hip_joint, parent=True, fullPath=True)
            else:
                parent_jots = []
            if parent_jots:
                root_joint = parent_jots[0]
            else:
                root_joint = hip_joint
        return root_joint

    def get_hik_info(self, hik_node='', *args, **kwargs):
        """
        Get the hik information

        Args:
            hik_node ():

        Returns:

        """
        if not hik_node:
            hik_node = self.get_human_ik_node_by_select()
        if not hik_node:
            return
        # --------------------
        hik_joint_map = self.get_joint_hik_map(hik_node)
        self._hik_full_joint_map = hik_joint_map
        # --------------------
        root_joint = self.get_root_joint(hik_joint_map)
        if root_joint:
            filter_nodes = cmds.listRelatives(root_joint, allDescendents=True, fullPath=True, type='constraint')
            self._joint_tree = self.get_node_tree_dict(root_joint, filter_nodes=filter_nodes)
            self._root_joint = root_joint
            self._current_hik_node = hik_node
        else:
            return False
        # --------------------
        self.get_joint_matrix_by_root(root_joint)
        nodes = cmds.listRelatives(root_joint, allDescendents=True, fullPath=True, type='joint') or []
        nodes.append(root_joint)
        for _node in nodes:
            self.get_attr_value(_node)
        # ---------------------
        for _key in self._hik_joint_map.keys():
            self._get_hik_double3_attr(hik_node, _key)

        # Rebuild a set of temporary bones in the Stance state based on the information read above,
        # and read its bone data
        self._is_add_namespace = True
        self.get_new_tmp_namespace()
        self.create_node_tree(self._joint_tree, self._get_simple_node_name(hik_node))
        for _node in self._new_joint_map.values():
            self.get_attr_value(_node)

        # Delete temporary bones and namespaces.
        # Note that in the py3 environment, the values function of the dictionary returns data of
        # the <class 'dict_values'> type, and maya's instructions will not be recognized.
        tmp_new_jots = list(self._new_joint_map.values())
        cmds.delete(tmp_new_jots)
        self._new_joint_map = dict()
        self._remove_namespace()
        return hik_joint_map

    def _create_tmp_root_joint(self, *args, **kwargs):
        """
        Create a temporary root joint

        Returns:
            str
        """
        if not (self._root_joint and self._current_hik_node):
            return False
        root_joint = self.create_joint(
            self._get_simple_node_name(self._root_joint),
            self._get_simple_node_name(self._current_hik_node)
        )
        return root_joint

    def _update_current_anim_joint_value(self, joint_name, *args, **kwargs):
        """
        Update the current animation joint value

        Args:
            joint_name (str): joint name

        Returns:
            None
        """
        attr_list = ['tx', 'ty', 'tz', 'rx', 'ry', 'rz', 'sx', 'sy', 'sz']
        for attr in self._anim_attr_list:
            attr_name = '%s.%s' % (joint_name, attr)
            if not cmds.objExists(attr_name):
                continue
            attr_value = cmds.getAttr(attr_name)
            self._update_anim_joint_info(joint_name, attr, attr_value)

    @staticmethod
    def get_anim_frame_rate(*args, **kwargs):
        """
        Get the animation frame rate

        Returns:
            float: animation frame rate
        """
        index = mel.eval('getIndexFromCurrentUnitCmdValue("{}")'.format(
            cmds.currentUnit(query=True, time=True)))

        time_unit = mel.eval('getTimeUnitDisplayStringTable()')[int(index) - 1]
        anim_frame_rate = float(re.findall(r'[\d\.]+', time_unit)[0])

        return anim_frame_rate

    @staticmethod
    def set_anim_frame_rate(frame_rate, unit='fps', *args, **kwargs):
        """
        Set the animation frame rate

        Args:
            frame_rate (float): animation frame rate
            unit (str): unit

        Returns:
            None
        """
        if int(frame_rate) == frame_rate:
            frame_rate = int(frame_rate)
        rate_str = '{}{}'.format(frame_rate, unit)
        cmds.currentUnit(time=rate_str, updateAnimation=True)

    def get_anim_info(self, frame_range=None, *args, **kwargs):
        """
        Get the animation information

        Args:
            frame_range ( list ): frame range

        Returns:
            None
        """
        if not self._root_joint:
            return False
        if not frame_range:
            frame_range = [
                int(cmds.playbackOptions(q=True, min=True)),
                int(cmds.playbackOptions(q=True, max=True))
            ]
        self._anim_frame_range = frame_range
        self._anim_frame_rate = self.get_anim_frame_rate()
        self._is_add_namespace = True
        root_joint = self._create_tmp_root_joint()
        for i in range(frame_range[0], frame_range[1]+1):
            cmds.currentTime(i)
            _t = cmds.xform(self._root_joint, q=1, t=1, ws=1)
            _r = cmds.xform(self._root_joint, q=1, ro=1, ws=1)
            _s = cmds.xform(self._root_joint, q=1, s=1, ws=1)
            cmds.xform(root_joint, a=True, t=_t, ws=1)
            cmds.xform(root_joint, a=True, ro=_r, ws=1)
            cmds.xform(root_joint, a=True, s=_s, ws=1)
            self._update_current_anim_joint_value(root_joint)
            for jot in cmds.listRelatives(self._root_joint, ad=1) or []:
                self._update_current_anim_joint_value(jot)
        cmds.delete(root_joint)
        self._remove_namespace()

    def set_hik_definition(self, hik_node, *args, **kwargs):
        """
        Set the hik definition

        Args:
            hik_node ( str ): hik node name

        Returns:
            None
        """
        for _key, _value in self._hik_joint_map.items():
            # mel.eval('setCharacterObject("worldOffset_M_JNT","Character1",0,0);')
            new_joint = self._new_joint_map.get(_value, _value)
            hik_id = mel.eval('hikGetNodeIdFromName("%s");' % _key)
            mel.eval('setCharacterObject("%s","%s",%s,0);' % (new_joint, hik_node, hik_id))

    def create_hik_joint(self, hik_node='', is_add_namespace=True, joint_visibility=True, *args, **kwargs):
        """
        Create hik joint

        Args:
            hik_node (str):
            is_add_namespace (bool):
            joint_visibility (bool):

        Returns:
            hik_node(str)
        """
        if not hik_node:
            hik_node = list(self._hik_attr_info.keys())[0] if self._hik_attr_info else ''
        if not hik_node:
            cmds.warning(u'No human ik data found, need to load human ik data first.')
            return False

        self._is_add_namespace = is_add_namespace
        self._joint_visibility = joint_visibility

        self.create_node_tree(self._joint_tree, hik_node)
        root_joint = self.get_root_joint() or ''
        root_joint = self._get_simple_node_name(root_joint)
        new_root_joint = self._new_joint_map.get(root_joint, '')
        root_matrix = self._joint_matrix_map.get(root_joint, {})
        # ----------------------------
        hik_node = self._add_namespace(hik_node)
        try:
            mel.eval('ToggleCharacterControls')
            mel.eval('ToggleCharacterControls')
            hik_node = mel.eval('hikCreateCharacter( "{}" );'.format(hik_node))
            mel.eval('hikUpdateCharacterList();')
            self.set_hik_definition(hik_node)
            if cmds.objExists(new_root_joint) and root_matrix:
                cmds.xform(new_root_joint, matrix=root_matrix, worldSpace=True)
            mel.eval('hikToggleLockDefinition();')
        except:
            cmds.warning(u'Create hik node failed, please check the joint name.')
            tmp_new_jots = list(self._new_joint_map.values())
            cmds.delete(tmp_new_jots)
            self._new_joint_map = dict()
            self._remove_namespace()
            return False
        retargeter = mel.eval('RetargeterGetName( "{}" );'.format(hik_node))
        self._tmp_hik_node = hik_node
        if cmds.objExists(new_root_joint):
            self.set_joint_matrix_by_root(new_root_joint)
        return hik_node

    def load_anim_data(self, start_frame=None, ignore_value_width=0.001, *args, **kwargs):
        if start_frame is None:
            start_frame = int(cmds.playbackOptions(q=True, min=True))
        for jot in self._new_joint_map.values():
            for attr in self._anim_attr_list:
                value_list = self._get_anim_joint_value(jot, attr)
                if not value_list:
                    continue
                try:
                    max_value = max(value_list)
                    min_value = min(value_list)
                except:
                    # print('anim', jot, attr, value_list)
                    continue
                # 如果帧数没有多少变化, 比ignore_value_width的值还低, 仅仅就K第一帧
                if max_value - min_value < ignore_value_width:
                    cmds.setKeyframe(jot, t=start_frame, at=attr, v=value_list[0])
                    continue
                for i in range(len(value_list)):
                    _t = start_frame + i
                    cmds.setKeyframe(jot, t=_t, at=attr, v=value_list[i])

    def load_anim_data_api(self, start_frame=None, ignore_value_width=0.001, *args, **kwargs):
        if start_frame is None:
            start_frame = int(cmds.playbackOptions(q=True, min=True))
        for jot in self._new_joint_map.values():
            for attr in self._anim_attr_list:
                value_list = self._get_anim_joint_value(jot, attr)
                if not value_list:
                    continue
                try:
                    max_value = max(value_list)
                    min_value = min(value_list)
                except:
                    # print('anim', jot, attr, value_list)
                    continue
                # 如果帧数没有多少变化, 比ignore_value_width的值还低, 仅仅就K第一帧
                if max_value - min_value < ignore_value_width:
                    cmds.setKeyframe(jot, t=start_frame, at=attr, v=value_list[0])
                    continue
                # --------------------
                # 转换api时间
                current_frame_rate = self.get_anim_frame_rate()
                fps = self._anim_frame_rate if self._anim_frame_rate else self._default_frame_rate
                times = OpenMaya.MTimeArray()
                for i in range(len(value_list)):
                    base_frame = float(start_frame) * 24.0 / current_frame_rate + i * 24.0 / float(fps)
                    times.append(OpenMaya.MTime(base_frame))
                # ------------------
                # 转换api属性数值
                util = OpenMaya.MScriptUtil()
                count = len(value_list)
                util.createFromList(value_list, count)
                doublePtr = util.asDoublePtr()
                doubleArray = OpenMaya.MDoubleArray(doublePtr, count)
                # -----------------
                # api 节点
                # pm_node = pm.PyNode(jot)
                # MfNode = pm_node.__apimobject__()
                sel_list = OpenMaya.MSelectionList()
                sel_list.add(jot)
                dag_path = OpenMaya.MDagPath()
                mobject = OpenMaya.MObject()
                sel_list.getDependNode(0, mobject)
                om_node = OpenMaya.MFnDependencyNode(mobject)
                plug_attr = om_node.findPlug(om_node.attribute(attr), True)
                # ------------------
                # api animCurve
                animCurveNode = OpenMayaAnim.MFnAnimCurve()
                animCurveNode.create(plug_attr, 1)
                animCurveNode.addKeys(times, doubleArray)

    @staticmethod
    def get_human_ik_node_by_select(objs=None, *args, **kwargs):
        hik_node = ''
        if not objs:
            objs = cmds.ls(sl=True)
        if not objs:
            return hik_node
        obj = objs[0]
        if ':' in obj:
            name_space = ':'.join(obj.split(':')[:-1])
            hik_nodes = cmds.ls('%s::*' % name_space, type='HIKCharacterNode')
            if hik_nodes:
                return hik_nodes[0]
        # 如果选中的物体没有名字空间
        hik_nodes = [_node for _node in cmds.ls(type='HIKCharacterNode') if ':' not in _node]
        if hik_nodes:
            return hik_nodes[0]
        return hik_node

    def set_tmp_namespace(self, namespace, *args, **kwargs):
        """
        Set the temporary namespace
        Args:
            namespace (str): namespace

        Returns:
            None
        """
        self._namespace = namespace

    def get_tmp_hik_node(self, *args, **kwargs):
        """
        Get the temporary hik node

        Returns:
            str
        """
        return self._tmp_hik_node

    def remove_tmp_hik_joint(self, *args, **kwargs):
        """
        Remove the temporary hik joint

        Returns:
            None
        """
        if self._tmp_hik_node:
            mel.eval('deleteCharacter( "{}" );'.format(self._tmp_hik_node))
            mel.eval('hikUpdateCharacterList();hikUpdateContextualUI();hikOnSwitchContextualTabs();')
        tmp_new_jots = list(self._new_joint_map.values())
        if tmp_new_jots:
            cmds.delete(tmp_new_jots)
        self._remove_namespace()

    def apply_tmp_hik_anim_to_hik_node(self, hik_node='', min_time=None, max_time=None,
                                       tmp_hik_node='', ex_bake_objs=None, *args, **kwargs):
        """

        Args:
            hik_node (): hik_node of target rig
            min_time ():
            max_time ():
            tmp_hik_node (): hik_node of template
            ex_bake_objs ():

        Returns:

        """
        if not tmp_hik_node:
            tmp_hik_node = self._tmp_hik_node
        if not tmp_hik_node:
            return False
        if not hik_node:
            hik_node = self.get_human_ik_node_by_select()
        if not hik_node:
            return False

        mel.eval('hikEnableCharacter( "{}", 2 );'.format(hik_node))
        mel.eval('hikSetCharacterInput("{}", "{}")'.format(hik_node, tmp_hik_node))

        # add non hik control to bake, and then bake to control rig
        cmds.refresh(force=True)

        # set bake frame range by time range
        if min_time is None:
            min_time = cmds.playbackOptions(query=True, minTime=True)
        if max_time is None:
            max_time = cmds.playbackOptions(query=True, maxTime=True)

        # bake setting
        bake_results_setting = {
            'simulation': True,
            'time': (min_time, max_time),
            'sampleBy': 1,
            'oversamplingRate': 1,
            'disableImplicitControl': True,
            'preserveOutsideKeys': True,
            'sparseAnimCurveBake': False,
            'removeBakedAttributeFromLayer': False,
            'removeBakedAnimFromLayer': False,
            'bakeOnOverrideLayer': False,
            'minimizeRotation': True,
            'controlPoints': False,
            'shape': False
        }

        is_control_rig = False
        bake_objs = mel.eval('RetargeterDestinationAttributes(RetargeterGetName("{}"));'.format(hik_node))
        control_rig = mel.eval('hikGetControlRig("{}");'.format(hik_node))
        if not bake_objs:
            if control_rig:
                is_control_rig = True
                mel.eval('hikBakeToControlRigPre("{}");'.format(hik_node))
            else:
                mel.eval('hikBakeCharacterPre("{}");'.format(hik_node))
            bake_objs = cmds.ls(sl=True)
        if bake_objs:
            if ex_bake_objs:
                bake_objs.extend(ex_bake_objs)
            cmds.bakeResults(bake_objs, **bake_results_setting)

            if is_control_rig:
                mel.eval('hikBakeToControlRigPost("{}");'.format(hik_node))
            else:
                mel.eval('hikBakeCharacterPost("{}");'.format(hik_node))
        return bake_objs

    def empty_human_ik_info(self, *args, **kwargs):
        """
        clear all human ik info

        Returns:
            None
        """
        self.__init__()

    def get_human_ik_info(self, *args, **kwargs):
        """
        get human ik info

        Returns:
            dict
        """
        hik_info = {
            'joint_attr_info': self._joint_attr_info,
            'hik_attr_info': self._hik_attr_info,
            'joint_hik_map': self._joint_hik_map,
            'hik_joint_map': self._hik_joint_map,
            'joint_tree': self._joint_tree,
            'anim_joint_info': self._anim_joint_info,
            'anim_frame_range': self._anim_frame_range,
            'anim_frame_rate': self._anim_frame_rate,
        }
        return hik_info

    def save_json_file(self, json_file, *args, **kwargs):
        """
        save human ik info to json file

        Args:
            json_file (str): json file path

        Returns:
            None
        """
        hik_info = self.get_human_ik_info()
        with open(json_file, 'w') as fp:
            json.dump(hik_info, fp, indent=4, sort_keys=True)
            fp.close()

    def load_human_ik_info(self, hik_info, *args, **kwargs):
        """
        load human ik info from json file

        Args:
            hik_info (dict): human ik info

        Returns:
            None
        """
        self._joint_attr_info = hik_info.get('joint_attr_info')
        self._hik_attr_info = hik_info.get('hik_attr_info')
        self._joint_hik_map = hik_info.get('joint_hik_map')
        self._hik_joint_map = hik_info.get('hik_joint_map')
        self._joint_tree = hik_info.get('joint_tree')
        self._anim_joint_info = hik_info.get('anim_joint_info')
        self._anim_frame_range = hik_info.get('anim_frame_range', [])
        self._anim_frame_rate = hik_info.get('anim_frame_rate', None)

    def load_json_file(self, json_file, *args, **kwargs):
        """
        load human ik info from json file
        Args:
            json_file (str):

        Returns:
            None
        """
        with open(json_file, 'r') as fp:
            hik_info = json.load(fp)
            fp.close()
        self.load_human_ik_info(hik_info)

    def _get_new_joint_ctrl_map(self, joint_ctrl_map, *args, **kwargs):
        """
        Get the new joint control map

        Args:
            joint_ctrl_map ():

        Returns:

        """
        new_joint_ctrl_map = dict()
        for jot, ctrl in joint_ctrl_map.items():
            ctrl_list = cmds.ls('::%s' % ctrl)
            if not ctrl_list:
                ctrl_list = cmds.ls('::%s' % self._get_simple_node_name(ctrl))
            if not ctrl_list:
                continue
            ex_ctrl = ctrl_list[0]
            if jot in self._full_new_joint_map:
                new_joint_ctrl_map[self._full_new_joint_map[jot]] = ex_ctrl
                continue
            _jot = self._get_simple_node_name(jot)
            for ex_jot in cmds.ls('::%s' % _jot):
                if ex_jot in self._full_new_joint_map:
                    new_joint_ctrl_map[self._full_new_joint_map[ex_jot]] = ex_ctrl
                    continue
        return new_joint_ctrl_map

    def _get_full_obj_name(self, name_space, obj_name, *args, **kwargs):
        """
        Get the full object name

        Args:
            name_space ():
            obj_name ():

        Returns:

        """
        obj_list = cmds.ls('%s::%s' % (name_space, obj_name))
        if obj_list:
            return obj_list[0]
        else:
            return ''

    def _get_full_new_joint_map(self, name_space, *args, **kwargs):
        """
        Get the full new joint map

        Args:
            name_space ():

        Returns:

        """
        full_new_joint_map = dict()
        if not name_space:
            return self._new_joint_map
        for jot, new_jot in self._new_joint_map.items():
            # jot_list = cmds.ls('%s::%s' % (name_space, jot))
            full_jot = self._get_full_obj_name(name_space, jot)
            if full_jot:
                full_new_joint_map[full_jot] = new_jot
        return full_new_joint_map

    def _get_constraint_st(self, obj_name, *args, **kwargs):
        """
        Get the constraint st

        Args:
            obj_name ():

        Returns:

        """
        st = []
        not_st = []
        attr_skip_map = {
            '.tx': 'x', '.ty': 'y', '.tz': 'z'
        }
        for attr, skip in attr_skip_map.items():
            attr_name = obj_name + attr
            if cmds.getAttr(attr_name, l=True):
                st.append(skip)
            else:
                not_st.append(skip)
        return st, not_st

    def _get_constraint_sr(self, obj_name, *args, **kwargs):
        """
        Get the constraint sr

        Args:
            obj_name ():

        Returns:

        """
        sr = []
        not_sr = []
        attr_skip_map = {
            '.rx': 'x', '.ry': 'y', '.rz': 'z'
        }
        for attr, skip in attr_skip_map.items():
            attr_name = obj_name + attr
            if cmds.getAttr(attr_name, l=True):
                sr.append(skip)
            else:
                not_sr.append(skip)
        return sr, not_sr

    def create_hik_template_by_hik_node(self, joint_ctrl_map, *args, **kwargs):
        """
        Create hik template by hik node

        Args:
            joint_ctrl_map ():
            **kwargs ():

        Returns:

        """
        hik_node = kwargs.get('hik_node', '')
        namespace = kwargs.get('namespace', 'tmp_hik_template_01')
        additive_joint_map = kwargs.get('additive_joint_map', dict())

        if not hik_node:
            hik_node = self.get_human_ik_node_by_select()
        if not hik_node:
            return {}

        self.get_hik_info(hik_node)
        self.set_tmp_namespace(namespace)
        hik_node_new = self.create_hik_joint(self._get_simple_node_name(hik_node))
        if not hik_node_new:
            return {}

        # -----------------------------
        name_space = self._hik_joint_namespace

        if name_space is None:
            name_space = ':'.join(hik_node.split(':')[:-1]) if ':' in hik_node else ''
        full_new_joint_map = self._get_full_new_joint_map(name_space)
        self._full_new_joint_map = full_new_joint_map
        # -----------------------------
        # First forward constrain the new bone
        constraint_list = []
        for jot, new_jot in full_new_joint_map.items():
            constraint_list.extend(cmds.parentConstraint(jot, new_jot))

        if constraint_list:
            cmds.delete(constraint_list)
        # ------------------------------
        # Inversely constrain the controller with the new bone
        world_jots = []
        root_jot = None
        if 'Hips' in self._hik_joint_map:
            hips_jot = self._new_joint_map[self._hik_joint_map['Hips']]
            hips_jot_ns = self._get_full_obj_name(name_space, hips_jot)
            if hips_jot_ns:
                world_jots.append(hips_jot_ns)
        if 'Reference' in self._hik_joint_map:
            root_jot = self._new_joint_map[self._hik_joint_map['Reference']]
            root_jot_ns = self._get_full_obj_name(name_space, root_jot)
            if root_jot_ns:
                world_jots.append(root_jot_ns)
        self._new_joint_ctrl_map = self._get_new_joint_ctrl_map(joint_ctrl_map)
        # todo

        # node zoo
        hik_node_nd = HIKCharacterNode(hik_node_new)
        if root_jot:
            root_jot_nd = DependencyNode(root_jot)
            hik_node_nd.connect_reference(root_jot_nd)

        for new_jot, ctrl in self._new_joint_ctrl_map.items():
            # print('{} -> {}'.format(new_jot, ctrl))

            # exclude bones of additive_joint_map
            # if new_jot.split(':')[-1] in additive_joint_map.keys():
            #     continue

            if new_jot.split(':')[-1] in ['Root_M_JNT']:
                continue

            st, not_st = self._get_constraint_st(ctrl)
            sr, not_sr = self._get_constraint_sr(ctrl)
            kwargs = dict()
            if new_jot in world_jots:
                if st:
                    kwargs['st'] = st
                if sr:
                    kwargs['sr'] = sr

                cmds.parentConstraint(new_jot, ctrl, maintainOffset=True, **kwargs)
                continue

            if not_st and not_sr:
                if st:
                    kwargs['st'] = st
                if sr:
                    kwargs['sr'] = sr
                cmds.parentConstraint(new_jot, ctrl, maintainOffset=True, **kwargs)
            elif not_st:
                if st:
                    kwargs['skip'] = st
                cmds.pointConstraint(new_jot, ctrl, maintainOffset=True, **kwargs)
            elif not_sr:
                if sr:
                    kwargs['skip'] = sr
                cmds.orientConstraint(new_jot, ctrl, maintainOffset=True, **kwargs)

        return self._new_joint_ctrl_map

    @staticmethod
    def _set_fbx_parameters(min_time=None, max_time=None, *args, **kwargs):
        """
        Set fbx export parameters

        Args:
            min_time (int): min time frame
            max_time (int): max time frame

        Returns:
            None
        """

        # set bake frame range by time range
        if min_time is None:
            min_time = int(cmds.playbackOptions(q=True, min=True))
        if max_time is None:
            max_time = int(cmds.playbackOptions(q=True, max=True))
        # --------------
        if not cmds.pluginInfo('fbxmaya', q=True, loaded=True):
            cmds.loadPlugin('fbxmaya')
        mel.eval('FBXResetExport')
        mel.eval('FBXExportFileVersion -v FBX201900')
        mel.eval('FBXExportUpAxis y')
        mel.eval('FBXExportShapes -v false')
        mel.eval('FBXExportScaleFactor 1.0')
        mel.eval('FBXExportInAscii -v false')
        mel.eval('FBXExportConstraints -v false')
        mel.eval('FBXExportLights -v false')
        mel.eval('FBXExportSkins -v false')
        mel.eval('FBXExportSmoothingGroups -v false')
        mel.eval('FBXExportSmoothMesh -v false')
        mel.eval('FBXExportEmbeddedTextures -v false')
        mel.eval('FBXExportCameras -v false')
        mel.eval('FBXExportBakeResampleAnimation -v false')
        # because use maya api to bake keyframe,
        # some rotation data fbx may not be recognized,
        # it is best to bake when exporting
        mel.eval('FBXExportBakeComplexAnimation -v true')
        mel.eval('FBXExportBakeComplexStart -v %s' % min_time)
        mel.eval('FBXExportBakeComplexEnd -v %s' % max_time)
        mel.eval('FBXExportBakeComplexStep -v 1')
        mel.eval('FBXExportSkeletonDefinitions -v true')
        mel.eval('FBXExportInputConnections -v false')
        mel.eval('FBXExportIncludeChildren -v true')
        mel.eval('FBXExportUseSceneName -v true')
        mel.eval('FBXExportGenerateLog -v false')

    def export_hik_fbx_by_hik_node(
            self, fbx_path, hik_node='', namespace='tmp_hik_fbx',
            is_current_time=False, joint_visibility=True, *args, **kwargs):
        """
        Export fbx by hik node

        Args:
            fbx_path (str): fbx file path
            hik_node (str): hik node
            namespace (str): namespace
            is_current_time (bool): is current time
            joint_visibility (bool): joint visibility

        Returns:
            bool: True is success, False is failed
        """
        if not hik_node:
            hik_node = self.get_human_ik_node_by_select()
        if not hik_node:
            return False
        min_time = None
        max_time = None
        frame_range = None
        if is_current_time:
            current_time = int(cmds.currentTime(q=1))
            min_time = current_time
            max_time = min_time + 1
            frame_range = [current_time, current_time]
        self.get_hik_info(hik_node)
        self.get_anim_info(frame_range=frame_range)
        self.set_tmp_namespace(namespace)
        result = self.create_hik_joint(self._get_simple_node_name(hik_node), joint_visibility=joint_visibility)
        if not result:
            return False
        self.load_anim_data_api()
        export_objs = list(self._new_joint_map.values())
        hik_node = self.get_tmp_hik_node()
        if cmds.objExists(hik_node):
            export_objs.append(hik_node)
        if not export_objs:
            return False
        cmds.select(export_objs, r=True)
        # ---------------------
        fbx_path = fbx_path.replace('\\', '/')
        fbx_dir = os.path.dirname(fbx_path)
        if not os.path.isdir(fbx_dir):
            os.makedirs(fbx_dir)
        self._set_fbx_parameters(min_time, max_time)
        print('fbx_path', fbx_path)
        mel.eval('FBXExport -f "%s" -s' % fbx_path)

        # Delete temporary skeleton and namespace
        self.remove_tmp_hik_joint()
        return True

    def reference_hik_fbx(self, fbx_path, *args, **kwargs):
        """
        Reference fbx
        Args:
            fbx_path (str): fbx file path

        Returns:

        """
        pass

    def remove_reference_hik_fbx(self, fbx_path, *args, **kwargs):
        """
        Remove reference fbx

        Args:
            fbx_path (str): fbx file path

        Returns:

        """
        pass
