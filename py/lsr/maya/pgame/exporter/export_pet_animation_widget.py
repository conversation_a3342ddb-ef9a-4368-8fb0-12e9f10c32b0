from functools import partial

from lsr.qt.core.widgets import qr_widgets as QRWidget
from Qt import QtWidgets, QtGui
from lsr.maya.pgame.exporter.exp_fbx import export_pet_animation as export_pet_animation
from pathlib import Path
from lsr.maya.rigtools.quick_rig.qr_list_context_maya import QRListContextMaya

class PGamePetAnimWidget(QtWidgets.QWidget):
    """
    A widget for rig data export/import.
    """

    def __init__(self, *args, **kwargs):
        super(PGamePetAnimWidget, self).__init__(*args, **kwargs)

        ui_vl_main = QtWidgets.QVBoxLayout(self)
        self.setLayout(ui_vl_main)

        ui_gl_setting = QtWidgets.QGroupBox('Settings:')
        ui_vl_setting = QtWidgets.QVBoxLayout(ui_gl_setting)
        ui_gl_setting.setContentsMargins(0, 20, 0, 0)
        ui_vl_main.addWidget(ui_gl_setting)

        ui_gl_files = QtWidgets.QGroupBox('Files:')
        ui_vl_files = QtWidgets.QVBoxLayout(ui_gl_files)
        ui_gl_files.setContentsMargins(0, 20, 0, 0)
        ui_vl_main.addWidget(ui_gl_files)

        ui_gl_export = QtWidgets.QGroupBox('Export:')
        ui_vl_export = QtWidgets.QVBoxLayout(ui_gl_export)
        ui_gl_export.setContentsMargins(0, 20, 0, 0)
        ui_vl_main.addWidget(ui_gl_export)

        ui_hl_export_option = QtWidgets.QHBoxLayout()
        ui_vl_setting.addLayout(ui_hl_export_option)

        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)

        # checkBox for split start
        self.ui_cb_start = QRWidget.QRCheckBox("Start")
        self.ui_cb_start.setChecked(True)
        ui_hl_export_option.addWidget(self.ui_cb_start)

        # checkBox for split idle
        self.ui_cb_idle = QRWidget.QRCheckBox("Idle")
        self.ui_cb_idle.setChecked(True)
        ui_hl_export_option.addWidget(self.ui_cb_idle)

        # checkBox for auto dt
        self.ui_cb_dt = QRWidget.QRCheckBox("DT")
        self.ui_cb_dt.setChecked(True)
        ui_hl_export_option.addWidget(self.ui_cb_dt)

        # checkBox for un Fast Start
        self.ui_cb_run_fast_start = QRWidget.QRCheckBox("RunFastStart")
        self.ui_cb_run_fast_start.setChecked(True)
        ui_hl_export_option.addWidget(self.ui_cb_run_fast_start)

        self.ui_fl_file = QRWidget.QRFileListWidget('animation_list')
        ui_vl_files.addWidget(self.ui_fl_file)

        ui_hl_export = QtWidgets.QHBoxLayout()
        self.ui_bt_export_current = QtWidgets.QPushButton()
        self.ui_bt_export_current.setFont(font)
        self.ui_bt_export_current.setFixedHeight(50)
        self.ui_bt_export_current.setStyleSheet('background-color : rgb(37, 138, 137);')
        self.ui_bt_export_current.setText('Export Current')
        ui_hl_export.addWidget(self.ui_bt_export_current)
        self.ui_bt_export_selected = QtWidgets.QPushButton()
        self.ui_bt_export_selected.setFont(font)
        self.ui_bt_export_selected.setFixedHeight(50)
        self.ui_bt_export_selected.setStyleSheet('background-color : rgb(37, 138, 137);')
        self.ui_bt_export_selected.setText('Export Selected')
        ui_hl_export.addWidget(self.ui_bt_export_selected)
        self.ui_bt_export_all = QtWidgets.QPushButton()
        self.ui_bt_export_all.setFont(font)
        self.ui_bt_export_all.setFixedHeight(50)
        self.ui_bt_export_all.setStyleSheet('background-color : rgb(37, 138, 137);')
        self.ui_bt_export_all.setText('Export All')
        ui_hl_export.addWidget(self.ui_bt_export_all)
        ui_vl_export.addLayout(ui_hl_export)

        self.progress_bar = QRWidget.QRProgressBarWidget()
        ui_vl_main.addWidget(self.progress_bar)

        self.__connect_signals()
        self.ui_fl_file.set_pop_context_menu(QRListContextMaya())

    def __get_ui_cb_data(self):
        return {
            'start': self.ui_cb_start.isChecked(),
            'idle': self.ui_cb_idle.isChecked(),
            'dt': self.ui_cb_dt.isChecked(),
            'run_fast_start': self.ui_cb_run_fast_start.isChecked()
        }

    def __connect_signals(self):
        self.ui_bt_export_current.clicked.connect(partial(self.__export_current_fbx))
        self.ui_bt_export_selected.clicked.connect(partial(self.__export_selected_fbx))
        self.ui_bt_export_all.clicked.connect(partial(self.__export_all_fbx))

    def __export_selected_fbx(self):
        files = self.ui_fl_file.get_selected_files()
        ui_cb_data = self.__get_ui_cb_data()
        if not files:
            QtWidgets.QMessageBox.warning(self, 'Warning', 'Please add files to export.')
            return

        for file in files:
            export_pet_animation(file, **ui_cb_data)

    def __export_all_fbx(self):
        files = self.ui_fl_file.get_files()
        ui_cb_data = self.__get_ui_cb_data()
        if not files:
            QtWidgets.QMessageBox.warning(self, 'Warning', 'Please add files to export.')
            return

        for file in files:
            export_pet_animation(file, **ui_cb_data)

    def __export_current_fbx(self):
        files = self.ui_fl_file.get_files()
        ui_cb_data = self.__get_ui_cb_data()
        if not files:
            QtWidgets.QMessageBox.warning(self, 'Warning', 'Please add files to export.')
            return

        has_dead_fall_end = any(Path(file_name).stem.endswith('_Dead_Fall_End') for file_name in files)
        if has_dead_fall_end:
            ui_cb_data['has_dead_fall_end'] = True

        self.progress_bar.reset()
        self.progress_bar.set_maximum(len(files))
        for file in files:
            export_pet_animation(file, **ui_cb_data)
            self.progress_bar.increment_progress()
        self.progress_bar.reset()
