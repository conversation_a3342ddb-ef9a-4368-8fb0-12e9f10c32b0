# -*- coding: utf-8 -*-

# Built-in modules
import os
import logging

# Third-party modules
from Qt import QtCore, QtGui, QtWidgets

# Studio modules

# Local modules


logging.basicConfig(filename=os.path.join(os.environ["TMP"], 'work_pyside_utils_log.txt'),
                    level=logging.WARN, filemode='a', format='%(asctime)s - %(levelname)s: %(message)s')


def get_sys_folder_icon():
    """
    获取系统文件夹的图标
    Returns:
        QIcon folder_icon
    """
    icon_provider = QtWidgets.QFileIconProvider()
    folder_icon = icon_provider.icon(icon_provider.Folder)
    return folder_icon


def get_exist_file_icon(file_path):
    """
    获取指定文件图标(文件必须存在)
    Args:
        file_path: 文件的具体路径字符串
                例如:r"D:\test\maya.mb"
    Returns:
        QIcon file_icon
    """
    icon_provider = QtWidgets.QFileIconProvider()
    file_info = QtCore.QFileInfo(unicode(file_path))
    file_icon = icon_provider.icon(file_info)
    return file_icon


def get_exist_file_type(file_path):
    """
    获取指定文件的文件类型(文件必须存在)
    Args:
        file_path: 文件的具体路径字符串
                例如:r"D:\test\maya.mb"
    Returns:
        QtGui.QFileIconProvider.type
    """
    icon_provider = QtWidgets.QFileIconProvider()
    file_info = QtCore.QFileInfo(unicode(file_path))
    file_type = icon_provider.type(file_info)
    return file_type


def get_tmp_file(extension):
    """
    根据扩展名称获取temp临时文件的存放地址和名称
    Args:
        extension: 文件扩展名字符串
                例如: "txt", "ma", "mb", "pdf"等等
    Returns:
        QtCore.QTemporaryFile
            例如: unicode(tmp_file.fileName())等于
                r'C:/Users/<USER>/AppData/Local/Temp/python_Hp8312.txt'
    """
    qt_dir = QtCore.QDir()
    app_name = QtCore.QCoreApplication.applicationName()
    str_temp_late_name = qt_dir.tempPath() + qt_dir.separator() + app_name + "_XXXXXX." + extension
    tmp_file = QtCore.QTemporaryFile(str_temp_late_name)
    return tmp_file


def get_file_extension_icon(extension):
    """
    通过扩展名extension（.***）获取对应的图标
    Args:
        extension: 文件扩展名字符串
                例如: "txt", "ma", "mb", "pdf"等等
    Returns:
        QIcon extension_icon
    """
    tmp_file = get_tmp_file(extension)
    tmp_file.setAutoRemove(False)
    # icon_provider = QtGui.QFileIconProvider()
    extension_icon = QtGui.QIcon()
    if tmp_file.open():
        # file_name = unicode(tmp_file.fileName())
        tmp_file.write(QtCore.QByteArray())
        tmp_file.close()
        # extension_icon = icon_provider.icon(QtCore.QFileInfo(file_name))
        extension_icon = get_exist_file_icon(tmp_file.fileName())
        tmp_file.remove()
    else:
        message = "failed to write temporary file %s" % unicode(tmp_file.fileName())
        QtCore.qCritical(message)
    return extension_icon


def get_file_extension_type(extension):
    """
    通过扩展名extension（.***）获取文件的类型
    Args:
        extension: 文件扩展名字符串
                例如: "txt", "ma", "mb", "pdf"等等
    Returns:
        QtGui.QFileIconProvider.type
    """
    tmp_file = get_tmp_file(extension)
    tmp_file.setAutoRemove(False)
    extension_type = ''
    if tmp_file.open():
        # file_name = unicode(tmp_file.fileName())
        tmp_file.write(QtCore.QByteArray())
        tmp_file.close()
        # extension_type = icon_provider.type(QtCore.QFileInfo(file_name))
        extension_type = get_exist_file_type(tmp_file.fileName())
        tmp_file.remove()
    else:
        message = "failed to write temporary file %s" % unicode(tmp_file.fileName())
        QtCore.qCritical(message)
    return extension_type


# def side_utils_def():
#     pass


if __name__ == "__main__":
    pass
