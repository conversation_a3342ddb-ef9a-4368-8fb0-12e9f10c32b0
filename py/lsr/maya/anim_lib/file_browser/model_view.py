import logging

import os
import subprocess
from functools import partial
import shutil
from Qt import QtCore, QtWidgets, QtGui
import lsr.qt.icon_lib.api as lsr_qt_api
from lsr.maya.anim_lib.manager.signal import SignalManager
from lsr.maya.anim_lib.manager import constant

from lsr.maya.anim_lib.utils import resolve_root_paths

logger = logging.getLogger(__name__)

QSETTINGS = QtCore.QSettings("LSR_DEV", "maya_anim_lib")
# animLib_root_path
ANIM_LIB_DIR_QSETTING = 'animLib_root_dir'
DEFAULT_FOLDER = 'AnimLib'
#root_path = resolve_root_paths()[0]
root_path = constant.animLib_root_path


class HideExtensionProxy(QtCore.QSortFilterProxyModel):
    """
    A proxy model that excludes files from the view
    that end with the given extension
    """

    def __init__(self, excludes, *args, **kwargs):
        QtCore.QSortFilterProxyModel.__init__(self)
        self._excludes = excludes[:]

    def filterAcceptsRow(self, srcRow, srcParent):
        idx = self.sourceModel().index(srcRow, 0, srcParent)
        name = idx.data()
        for exc in self._excludes:
            if name.endswith(exc):
                return False

        return True


class AnimLibFileIconProvider(QtWidgets.QFileIconProvider):
    """custom icon provider class"""

    def __init__(self, *args, **kwargs):
        QtWidgets.QFileIconProvider.__init__(self)
        self.DirIcon = QtGui.QPixmap(lsr_qt_api._resolve_path("/library/folder.png"))

    def icon(self, type_info):
        """hack the default provider icon """
        if isinstance(type_info, QtCore.QFileInfo):
            return self.getInfoIcon(type_info)
        if type_info == QtWidgets.QFileIconProvider.Folder:
            return self.DirIcon
        return super(AnimLibFileIconProvider, self).icon(type_info)

    def getInfoIcon(self, type_info):
        if type_info.isDir():
            return self.DirIcon
        return super(AnimLibFileIconProvider, self).icon(type_info)

class AnimLibFileModel(QtWidgets.QFileSystemModel):
    def __init__(self, rootPath=root_path):
        QtWidgets.QFileSystemModel.__init__(self)
        self.setRootPath(rootPath)
        self.setFilter(QtCore.QDir.NoDotAndDotDot | QtCore.QDir.Dirs)
        self.setIconProvider(AnimLibFileIconProvider())


class AnimLibFileController(QtWidgets.QTreeView):

    def __init__(self, parent=None):
        super(AnimLibFileController, self).__init__(parent)

        self._current_path = ''
        self._model = AnimLibFileModel()
        skip_exts = ['_footage']
        self._proxyModel = HideExtensionProxy(excludes=skip_exts)
        self._proxyModel.setDynamicSortFilter(True)

        self._proxyModel.setSourceModel(self._model)
        self._proxyModel.setFilterRegExp(r'')

        self.setModel(self._proxyModel)
        path = QSETTINGS.value(ANIM_LIB_DIR_QSETTING) or ''
        if not os.path.isdir(path):
            path = root_path
        self.setRootIndex(self._proxyModel.mapFromSource(self._model.index(path)))

        self.header().hide()
        self.setColumnHidden(1, 1)
        self.setColumnHidden(2, 1)
        self.setColumnHidden(3, 1)
        self.setStyleSheet('font-size: 11pt; ')
        #self.setDragDropMode(QtWidgets.QAbstractItemView.InternalMove)

        self.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu_cb)

        self.setMinimumWidth(250)
        self.clicked.connect(self.on_clicked_cb)

    # right click menu ---------------------
    def show_context_menu_cb(self, pos):
        current_file_path = self.getCurrentCursorPath()
        if not current_file_path:
            return
        menu = QtWidgets.QMenu()
        create_folder_action = QtWidgets.QAction('Create Folder', self)
        create_folder_action.triggered.connect(partial(self.create_folder_cb, current_file_path))
        menu.addAction(create_folder_action)

        # rename_folder_action = QtWidgets.QAction('Rename Folder', self)
        # rename_folder_action.triggered.connect(partial(self.rename_folder_cb, current_file_path))
        # menu.addAction(rename_folder_action)

        del_action = QtWidgets.QAction('Delete', self)
        del_action.triggered.connect(partial(self.delete_cb, current_file_path))
        menu.addAction(del_action)
        menu.addSeparator()

        show_action = QtWidgets.QAction('Show In Explorer', self)
        show_action.triggered.connect(partial(self.show_in_explorer_cb, current_file_path))
        menu.addAction(show_action)
        menu.exec_(self.mapToGlobal(pos))

    def set_root_path(self):
        path = QSETTINGS.value(ANIM_LIB_DIR_QSETTING) or ''
        if not os.path.isdir(path):
            path = root_path
        fn = QtWidgets.QFileDialog.getExistingDirectory(
            self, 'Set AnimLib Root Path', path
        )
        if fn:
            self.setRootIndex(self._proxyModel.mapFromSource(self._model.index(fn)))
            QSETTINGS.setValue(ANIM_LIB_DIR_QSETTING, fn)
            # ------------------
            folder_list = [
                x for x in os.listdir(fn)
                if not x.endswith('_footage')
                if os.path.isdir(os.path.join(fn, x))
            ]
            if not folder_list:
                new_folder = os.path.join(fn, DEFAULT_FOLDER)
                os.makedirs(new_folder)

    def get_current_path(self):
        current_file_path = ''
        selected_index_list = self.selectionModel().selectedIndexes()
        if not selected_index_list:
            return ''
        selected_index = selected_index_list[0]
        if not selected_index.isValid():
            return ''
        model = selected_index.model()
        if not isinstance(model, QtCore.QSortFilterProxyModel):
            return ''
        real_index = model.mapToSource(selected_index)
        current_file_path = self._model.filePath(real_index)
        return current_file_path

    def getCurrentCursorPath(self):
        current_pos = self.mapFromGlobal(QtGui.QCursor.pos())
        selected_index = self.indexAt(current_pos)
        selected_path = None
        if selected_index.isValid():
            model = selected_index.model()
            if isinstance(model, QtCore.QSortFilterProxyModel):
                real_index = model.mapToSource(selected_index)
                selected_path = self._model.filePath(real_index)
                return selected_path
        if not selected_path:
            return None

    def delete_cb(self, path):
        result = QtWidgets.QMessageBox.question(self, "Delete", "Deletion is non-revertible. Are you sure?")
        if result != QtWidgets.QMessageBox.StandardButton.Yes:
            return
        if os.path.isdir(path):
            shutil.rmtree(path)

    def show_in_explorer_cb(self, path):
        if os.path.isfile(path):
            path = os.path.dirname(path)
        if not os.path.exists(path):
            return
        path = path.replace('/', '\\')
        subprocess.Popen(r'explorer "{}"'.format(path))

    def create_folder_cb(self, path):
        text, ok = QtWidgets.QInputDialog.getText(self, 'Create Folder Dialog', 'Enter folder name:')
        if not ok:
            return
        folder_path = os.path.join(path, text)
        try:
            os.mkdir(folder_path)
        except BaseException as e:
            logger.error(str(e))

    # right click menu ---------------------
    def on_clicked_cb(self, args):
        SignalManager.file_browser_selection_changed.emit(self.getCurrentCursorPath())


























