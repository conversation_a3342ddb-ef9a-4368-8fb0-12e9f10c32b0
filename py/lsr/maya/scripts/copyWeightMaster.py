# -*- coding: GBK -*-
import maya.cmds as cmds

'''
 选择顺序千万不能错,先选择被复制权重的源模型,再选择所有需要权重的模型或组（有无蒙皮都无所谓）,再执行
 by 劲爆羊 2018/8/7 测试环境 maya2017-update5
'''


class CopyTheWeightJBY():
    def __init__(self):
        pass

    def getAllHistoryNodesByType(self, inputNodes, theType):
        getIter = inputNodes.__iter__()
        getAllNodes = []
        print(theType)
        while getIter:
            try:
                try:
                    # getAllNodes.extend(getIter.next().listHistory(type=theType))  # python2
                    getAllNodes.extend(
                        [node for node in cmds.listHistory(getIter.next()) if cmds.nodeType(node) == theType]
                    )  # python2
                except AttributeError:
                    getAllNodes.extend(
                        [node for node in cmds.listHistory(getIter.__next__()) if cmds.nodeType(node) == theType]
                    )  # python3
            except StopIteration:
                break
        return list(set(getAllNodes))

    def createSkinClusterToObjects(self, theSourceSkinNode, theBones, theObjcts):
        getTheObjIter = theObjcts.__iter__()
        while getTheObjIter:
            try:
                getTheSkinClusterNew = ''
                try:
                    theOBJ = getTheObjIter.next() # python2
                except AttributeError:
                    theOBJ = getTheObjIter.__next__() # python3
                getTheSCs = self.getAllHistoryNodesByType([theOBJ], 'skinCluster')
                if getTheSCs:
                    getTheSkinClusterNew = getTheSCs[0]
                else:
                    getTheSkinClusterNew = cmds.skinCluster(theBones, theOBJ, tsb=True)[0]
                cmds.copySkinWeights(ss=theSourceSkinNode, ds=getTheSkinClusterNew, noMirror=True,
                                    surfaceAssociation='closestPoint',
                                    influenceAssociation=('oneToOne', 'closestJoint', 'closestJoint'))
            except StopIteration:
                break

    def getAllChildren(self, theInputs, theOutputs):
        getTheInputsIter = theInputs.__iter__()
        while getTheInputsIter:
            try:
                try:
                    theItem = getTheInputsIter.next() # python2
                except AttributeError:
                    theItem = getTheInputsIter.__next__() # python3
                if cmds.listRelatives(theItem, shapes=True):
                    theOutputs.append(theItem)
                getChildren = cmds.listRelatives(theItem, f=True, type='transform', c=True)
                if getChildren:
                    self.getAllChildren(getChildren, theOutputs)
            except StopIteration:
                break

    def startCopyWeight(self):
        getSel = cmds.ls(sl=True)
        getAllBones = self.getAllHistoryNodesByType(getSel[:1], 'joint')
        if getAllBones:
            getAllSkinCluster = self.getAllHistoryNodesByType(getSel[:1], 'skinCluster')
            getChildren = []
            self.getAllChildren(getSel[1:], getChildren)
            self.createSkinClusterToObjects(getAllSkinCluster[0], getAllBones, getChildren)
        else:
            cmds.warning(u'第一个选择的源物体没有蒙皮！')


class CopyTheWeightJBY_UI():
    def __init__(self):
        self.initUI()

    def initUI(self):
        if cmds.window('win_copyWeight', ex=True):
            cmds.deleteUI('win_copyWeight')
        cmds.window('win_copyWeight', t=u'羊牌复制权重小工具 v1.00 by劲爆羊 2018/8/7')
        cmds.gridLayout(cwh=(550, 60), nc=1)
        cmds.text(
            l='选择顺序千万不能错\n先选择【被复制权重的源模型】\n再选择【所有需要权重的模型或组（有无蒙皮都无所谓）】\n最后执行以下代码（测试环境 maya2017-update5）')
        cmds.button(l=u'复制', c=self.command_copy)
        cmds.setParent('..')
        cmds.showWindow('win_copyWeight')

    def command_copy(self, *args):
        startCopy = CopyTheWeightJBY()
        startCopy.startCopyWeight()