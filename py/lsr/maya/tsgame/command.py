from lsr.maya.tsgame.correct_shape import correct_shape_view as cp
from lsr.maya.tsgame.exporter import exporter_view as ep
from lsr.maya.tsgame.replace_reference import replace_reference_view as rp
from lsr.maya.tsgame.animlinker import animlinker_view as ap


def tsgame_export_fbx():
    ep.TSGameAnimExporterWindow().launch()


def tsgame_replace_reference():
    rp.TSGameReplaceReferenceWindow().launch()


def tsgame_correct_shape():
    cp.TSGameCorrectShapeWindow().launch()


def tsgame_anim_linker():
    ap.TSAnimLinkerWindow().launch()

