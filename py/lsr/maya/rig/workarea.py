import os
import pathlib

import maya.mel as mel
import maya.cmds as cmds
import lsr.python.core.logger as logger


CHAR_DIR_LIST = (
    'assets',
    'autosave',
    'data',
    'movies',
    'scenes',
    'scripts',
    'sourceimages',
    'FBX')


ASSET_DIR_DICT = {
    'body': ('skeleton',
             'rigmesh',
             'deformer',
             'connections',
             'anim',
             'picker',
             'graph'),
    'face': ('skeleton',
             'rigmesh',
             'deformer',
             'wtsDeformer',
             'connections',
             'anim',
             'pose',
             'picker',
             'textureDriver',
             'graph')
}

SCENES_DIR_LIST = (
    'animation',
    'model',
    'rig')

ASSET_DIR_DICT.setdefault('default', ASSET_DIR_DICT['body'])


def get_rig_asset_path(*args, **kwargs):
    """Get the rig asset path.

    Args:
        *args: The args.
        **kwargs: The kwargs.

    Returns:
        str: The rig asset path.
    """
    rig_type = kwargs.get('rig_type', 'body')
    work_space = pathlib.Path(cmds.workspace(query=True, rootDirectory=True))
    root = str(work_space.parent.parent)
    project = work_space.parent.name
    char = work_space.name
    create_workarea(root, project, char, rig_type=rig_type)
    rig_asset_path = asset_path(root, project, char, rig_type=rig_type)

    return rig_asset_path


def _clean_join(*args):
    return os.path.join(*args).replace('\\', ' /').replace(' ', '')


def create_workarea(root, project, char, rig_type='body'):
    """Creates maya project folders for a given character.

    Args:
        root (str): The workarea root path.
        project (str): The project name.
        char (str): The character name.
        rig_type (str): The rig type. "body" or "face", or custom.

    Returns:
        None
    """
    c_path = char_path(root, project, char)
    a_path = asset_path(root, project, char, rig_type=rig_type)

    for sub_dir in CHAR_DIR_LIST:
        sub_dir = _clean_join(c_path, sub_dir)
        if not os.path.exists(sub_dir):
            os.makedirs(sub_dir)

        if sub_dir == _clean_join(c_path, 'scenes'):
            for res_type in SCENES_DIR_LIST:
                res_dir = _clean_join(sub_dir, res_type)
                if not os.path.exists(res_dir):
                    os.makedirs(res_dir)

    for rt, sub_dirs in ASSET_DIR_DICT.items():
        if rt != rig_type and rig_type in ASSET_DIR_DICT.keys():
            logger.debug('NOT {}, {}'.format(rig_type, rig_type))
            continue
        else:
            sub_dirs = ASSET_DIR_DICT.get(rig_type, ASSET_DIR_DICT['body'])

        for sub_dir in sub_dirs:
            sub_dir = _clean_join(a_path, sub_dir)
            if not os.path.exists(sub_dir):
                os.makedirs(sub_dir)

    walk_create_gitkeep(c_path)

    mel.eval('setProject \"' + c_path + '\"')


def char_path(root, project, char):
    """Returns a character path."""
    project_path = _clean_join(root, project)
    return _clean_join(project_path, char)


def asset_path(root, project, char, rig_type='body'):
    """Returns an asset path."""
    project_path = _clean_join(root, project)
    char_path = _clean_join(project_path, char)
    return _clean_join(char_path, 'assets', rig_type + 'RigAssets')


def template_data_file(root, project, char, rig_type='body'):
    """Returns the template data file path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'temp.json')


def marker_data_file(root, project, char, rig_type='body'):
    """Returns the marker data file path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'marker.json')


def ctrl_data_file(root, project, char, rig_type='body'):
    """Returns the ctrl data file path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'ctrl.json')


def set_data_file(root, project, char, rig_type='body'):
    """Returns the export set data file path for a given character."""
    return _clean_join(
        asset_path(root, project, char, rig_type), 'export_set.json')


def ctrl_joint_map_data_file(root, project, char, rig_type='body'):
    """Returns the ctrl <-> joint map data file path for a given character."""
    return _clean_join(
        asset_path(root, project, char, rig_type), 'joint_ctrl_map.json')


def skeleton_path(root, project, char, rig_type='body'):
    """Returns the skeleton directory path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'skeleton')


def rigmesh_path(root, project, char, rig_type='body'):
    """Returns the mesh directory path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'rigmesh')


def connection_path(root, project, char, rig_type='body'):
    """Returns the connection directory path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'connections')


def deformer_path(root, project, char, rig_type='body'):
    """Returns the deformer directory path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'deformer')


def wts_deformer_path(root, project, char, rig_type='body'):
    """Returns the wts deformer directory path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'wtsDeformer')


def pose_path(root, project, char, rig_type='body'):
    """Returns the pose directory path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'pose')


def picker_path(root, project, char, rig_type='body'):
    """Returns the picker directory path for a given character."""
    return _clean_join(asset_path(root, project, char, rig_type), 'picker')


def texture_driver_path(root, project, char, rig_type='body'):
    """Returns the texture driver node data path in json format."""
    return _clean_join(asset_path(root, project, char, rig_type), 'textureDriver', 'shader.json')


def rig_file(root, project, char, rig_type='body'):
    """Returns the output rig file path."""
    file_name = '{}.ma'.format(char)
    if rig_type == 'face':
        file_name = '{}_face.ma'.format(char)
    return _clean_join(char_path(root, project, char), 'scenes', file_name)


def create_gitkeep(directory, *args, **kwargs):
    """
    Create .gitkeep file if directory is empty
    Args:
        directory (str): directory path

    Returns:
    """
    if not os.path.exists(directory):
        return None
    if not os.listdir(directory):
        gitkeep_path = os.path.join(directory, ".gitkeep")
        if not os.path.exists(gitkeep_path):
            with open(gitkeep_path, "w") as gitkeep_file:
                gitkeep_file.write("")
            print("Created .gitkeep file in {}".format(directory))
        else:
            print(".gitkeep file already exists in {}.".format(directory))


def walk_create_gitkeep(directory, *args, **kwargs):
    """
    Walk directory and create .gitkeep file if directory is empty
    Args:
        directory (str): directory path
        *args ():
        **kwargs ():

    Returns:
        None
    """
    for root, dirs, files in os.walk(directory):
        for d in dirs:
            create_gitkeep(_clean_join(root, d))
