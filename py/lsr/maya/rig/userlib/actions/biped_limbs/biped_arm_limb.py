import maya.cmds as cmds
import lsr.maya.maya_math as mmath
import lsr.maya.rig.quadruped_utils as qutil
from lsr.maya.nodezoo.node import Joint
from lsr.maya.nodezoo.node import Node
from lsr.maya.rig.userlib.actions.biped_limbs.leg_arm_2bone_limb import LegArm2BaseLimb
from lsr.maya.standard.name import NodeName
import lsr.maya.rig.utils as util
import lsr.protostar.core.parameter as pa


class IKFKBipedArm(LegArm2BaseLimb):
    """A limb action that makes a simple fk rig in maya.
    """
    _LIMB_TYPE = 'arm'
    _DEFAULT_SIDE = 'L'
    _END_CTRL_NAME = 'hand'

    _UI_ICON = 'arm'

    @pa.bool_param(default=False)
    def ik_up_z(self):
        """The IK up vector is in Y."""

    @pa.bool_param(default=False)
    def ik_world(self):
        """The IK is in world space."""

    # --- input parameters
    def limb_template_data(self, *args, **kwargs):
        """limb template data"""
        limb_name = kwargs.get('name_part', 'arm')
        name_side = kwargs.get('name_side', 'L')

        cmds.select(d=True)
        parent_joint = None
        joint_chain = []
        _joint_pos = [(0, 0, 0.0), (3, 0, -0.01), (6, 0, 0), (7, 0, 0)]

        for i, pos_data in enumerate(_joint_pos):
            jt_node = Joint.create(name='{}_{:02d}_{}_RIGJNT'.format(limb_name, i, name_side), p=pos_data)
            joint_chain.append(jt_node)
            if i == 0:
                parent_joint = jt_node
        parent_joint.orient_chain(orientJoint='xyz', secondaryAxisOrient='yup', zeroScaleOrient=True, children=True)

        # Make sure joint_chain is in a flat
        for _jnt in joint_chain[1:]:
            _jnt.lock(attrs='ty')

        cmds.select(d=True)

        return joint_chain

    def run(self):
        # """Builds the limb ctrl rig."""
        upper_twist = self.upper_twist_joints.value
        lower_twist = self.lower_twist_joints.value

        limb_root = self.limb_root.value
        start_joint = self.rig_skeleton[0][0]

        name = NodeName(limb_root, desc='ScaleRoot', ext='GRP')
        name_npo = name.replace_ext('npo')
        name_ctrl = name.replace_ext('CTRL')
        name_lvl = name.replace_ext('lvl')
        name_cns = name.replace_ext('cns')
        name_ref = name.replace_ext('ref')
        name_grp = name.replace_ext('grp')
        name_jnt = name.replace_ext('Jnt')
        name_loc = name.replace_ext('loc')
        name_rot = name.replace_ext('rot')
        name_drv = name.replace_ext('drv')
        name_mth = name.replace_ext('mth')
        self.joint_rig = True
        self.ikSolver = False
        self.rot_offset = (0, 0, 0)
        self.jointList = []
        self.jnt_pos = []
        self.relatives = {}
        self.controlRelatives = {}
        self.jointRelatives = {}
        self.aliasRelatives = {}
        self.x_axis = (1.0, 0.0, 0.0)
        self.size = .01
        self.negate = self.side.enum_value == NodeName.SIDE_R and True or False
        self.ikOri = True
        self.ikTR = False
        self.apos = [mmath.get_position(n) for n in self.rig_skeleton[0]]
        self.pos = dict(zip(['root', 'knee', 'ankle', 'foot', 'eff'], [qutil.get_transform_from_pos(pos) for pos in self.apos]))

        self.length0 = mmath.distance(self.apos[0], self.apos[1])
        self.length1 = mmath.distance(self.apos[1], self.apos[2])
        self.length2 = mmath.distance(self.apos[2], self.apos[3])

        #Adjust the hand bones axially according to the original positioning of the axial skeleton
        self.jnts = [n for n in self.rig_skeleton[0]]
        ctrl_up_z = mmath.axis_to_vector(self.jnts[2], 'z')
        hand_matrix = qutil.get_transform_looking_at(self.apos[2], self.apos[3], ctrl_up_z, "xy", self.negate)

        self.supportJoints = True

        size = .01
        for pos in self.apos:
            d = mmath.distance(self.apos[0], pos)
            size = max(size, d)
        self.size = max(size, .01)

        ctrl_scale = [self.length0 / 6] * 3

        self.n_factor = 1
        scale_root = qutil.create_transform(None, name, start_joint)
        scale_root.make_identity()
        scale_root.set_parent(util.get_or_create_deformer_node())
        scale_root.v.value = False

        # FK root
        guid = qutil.create_transform(scale_root, name.replace_desc('guid'))
        guid.add_attr('float', defaultValue=0, keyable=True, name='st_profile')
        guid.add_attr('float', defaultValue=0, keyable=True, name='sq_profile')

        self.st = Node.create("animCurveUU", name=name.replace_desc('st'))
        self.st.add_key(0, 0, 'flat', 'flat')
        self.st.add_key(0.5, -1, 'flat', 'flat')
        self.st.add_key(1, 0, 'flat', 'flat')
        self.st.output >> guid.st_profile

        self.sq = Node.create("animCurveUU", name=name.replace_desc('st'))
        self.sq.add_key(0, 0, 'flat', 'flat')
        self.sq.add_key(0.5, 1, 'flat', 'flat')
        self.sq.add_key(1, 0, 'flat', 'flat')
        self.sq.output >> guid.sq_profile

        setup = qutil.create_transform(scale_root, name.replace_desc('setup'))
        self.global_ctrl = qutil.create_transform(limb_root, name.replace_desc('global'))
        self.root_ctrl = self.add_ctrl(name=name_ctrl.replace_desc('root'), parent=self.global_ctrl, shape='sphere', scale=[scale * 0.3 for scale in ctrl_scale], group_exts=None)
        self.root_ctrl.lock('rsv')
        self.root = qutil.create_transform(self.root_ctrl, name.replace_desc('root'))
        self.jnt_org = qutil.create_transform(scale_root, name.replace_desc('jnts'))

        # # global scale ===============================================
        cmds.addAttr(self.global_ctrl.name, longName='gScale', attributeType='double3')
        cmds.addAttr(self.global_ctrl.name, longName='gScaleX', attributeType='double', parent='gScale')
        cmds.addAttr(self.global_ctrl.name, longName='gScaleY', attributeType='double', parent='gScale')
        cmds.addAttr(self.global_ctrl.name, longName='gScaleZ', attributeType='double', parent='gScale')

        global_scale_dmt = Node.create('decomposeMatrix', name=self.global_ctrl.name + '_dmt')
        self.global_ctrl.worldMatrix >> global_scale_dmt.inputMatrix
        global_scale_dmt.outputScale >> self.global_ctrl.gScale

        t = self.apos[0]
        self.global_ctrl.set_translation(t, space='world')

        self.ws = qutil.create_transform(setup, name.replace_desc('ws'))
        self.normal = qutil.get_normal_from_pos(self.apos)
        self.binormal = qutil.get_binormal_from_pos(self.apos)

        # 1 bone chain for upv ref
        self.armChainUpvRef = qutil.add_2d_chain(self.root, name_jnt.replace_desc('armUpvRef'), [self.apos[0], self.apos[2]], self.normal, False, True)
        self.armChainUpvRef[0].v.value = False
        joint_orient = self.armChainUpvRef[1].jointOrient.value
        self.armChainUpvRef[1].jointOrient.value = (joint_orient[0], joint_orient[1], joint_orient[2] * -1)

        # base Controlers -----------------------------------
        self.root_npo = qutil.create_transform(self.root, name_npo.replace_desc('root'))
        self.root_npo.set_translation(t, space='world')

        self.ctl_grp = qutil.create_transform(self.root_npo, name_grp.replace_desc('ctrl'))
        self.ctl_grp.set_translation(t, space='world')
        self.ctl_grp.lock('sv')

        # FK Controlers -----------------------------------
        t = qutil.get_transform_looking_at(self.apos[0], self.apos[1], self.normal, "xz", self.negate)
        self.fk0_npo = qutil.create_transform(self.root, name_npo.replace_desc('fk0'), t)
        self.fk0_ctl = self.add_ctrl(name=name_ctrl.replace_desc('fk0'), parent=self.fk0_npo, shape='cube', scale=ctrl_scale, group_exts=None)
        self.fk0_ctl.set_matrix(t, space='world')
        self.set_control_size(self.fk0_ctl, self.length0, self.negate)

        t = qutil.get_transform_looking_at(self.apos[1], self.apos[2], self.normal, "xz", self.negate)
        self.fk1_npo = qutil.create_transform(self.fk0_ctl, name_npo.replace_desc('fk1'), t)
        self.fk1_ctl = self.add_ctrl(name=name_ctrl.replace_desc('fk1'), parent=self.fk1_npo, shape='cube', scale=ctrl_scale, group_exts=None)
        self.fk1_ctl.set_matrix(t, space='world')
        self.set_control_size(self.fk1_ctl, self.length1, self.negate)

        t = qutil.get_transform_looking_at(self.apos[2], self.apos[3], self.normal, "xz", self.negate)
        if self.ik_up_z.value and (not self.ik_world.value):
            t = hand_matrix
        self.fk2_npo = qutil.create_transform(self.fk1_ctl, name_npo.replace_desc('fk2'), t)
        self.fk2_ctl = self.add_ctrl(name=name_ctrl.replace_desc('fk2'), parent=self.fk2_npo, shape='cube', scale=ctrl_scale, group_exts=None)
        self.fk2_ctl.set_matrix(t, space='world')
        self.set_control_size(self.fk2_ctl, self.length2, self.negate)

        self.fk_ctl = [self.fk0_ctl, self.fk1_ctl, self.fk2_ctl]

        # IK upv ---------------------------------
        v = self.apos[2] - self.apos[0]
        v = self.normal ^ v
        v.normalize()
        v *= self.size * .5
        v += self.apos[1]

        self.upv_cns = qutil.create_transform(self.root, name_cns.replace_desc('upv'), v)
        self.upv_ctl = self.add_ctrl(name=name_ctrl.replace_desc('upv'), parent=self.upv_cns, shape='sphereCurve', scale=ctrl_scale, group_exts=None)
        self.upv_ctl.lock('rsv')
        self.upv_ctl.set_matrix(self.upv_cns.get_matrix('world'), space='world')
        # IK Controlers -----------------------------------

        self.ik_cns = qutil.create_transform(self.root, name_cns.replace_desc('ik'), self.apos[2])
        t = qutil.get_transform_from_pos(self.apos[2])
        self.ikcns_ctl = self.add_ctrl(name=name_ctrl.replace_desc('ikcns'), parent=self.ik_cns, shape='sphereCurve', scale=ctrl_scale, group_exts=None)
        self.ikcns_ctl.set_matrix(t, space='world')

        if self.negate:
            m = qutil.get_transform_looking_at(self.apos[2], self.apos[3], self.normal, "x-y", True)
        else:
            m = qutil.get_transform_looking_at(self.apos[2], self.apos[3], self.normal, "xy", False)

        if self.ik_world.value:
            m = qutil.get_transform_from_pos(self.apos[2])
        elif self.ik_up_z.value:
            m = hand_matrix

        self.ik_grp = qutil.create_transform(self.ikcns_ctl, name_grp.replace_desc('ik'), m)
        self.ik_ctl = self.add_ctrl(name=name_ctrl.replace_desc('ik'), parent=self.ik_grp, shape='cube', scale=ctrl_scale, group_exts=None)
        self.ik_ctl.set_matrix(m, space='world')

        self.ik_ctl_ref = qutil.create_transform(self.ik_ctl, name_ref.replace_desc('ikctrl'), m)

        # IK rotation controls
        if self.ikTR:
            self.ikRot_npo = qutil.create_transform(self.root, name_npo.replace_desc('ikRot'), m)
            self.ikRot_cns = qutil.create_transform(self.ikRot_npo, name_cns.replace_desc('ikRot'), m)
            self.ikRot_ctl = self.add_ctrl(name=name_ctrl.replace_desc('ikRot'), parent=self.ikRot_cns, shape='cube', scale=ctrl_scale, group_exts=None)
            self.ikRot_ctl.set_matrix(m, space='world')

        # References --------------------------------------
        # Calculate  again the transfor for the IK ref. This way align with FK
        trnIK_ref = qutil.get_transform_looking_at(self.apos[2], self.apos[3], self.normal, "xz", self.negate)
        self.ik_ref = qutil.create_transform(self.ik_ctl_ref, name_ref.replace_desc('ik'), trnIK_ref)
        self.fk_ref = qutil.create_transform(self.fk_ctl[2], name_ref.replace_desc('fk'), trnIK_ref)

        # Chain --------------------------------------------
        # The outputs of the ikfk2bone solver
        self.bone0, self.bone0_shp = qutil.create_locator(self.ctl_grp, name_loc.replace_desc('0bone'), self.fk_ctl[0])
        self.bone0_shp.localPositionX = self.n_factor * .5
        self.bone0_shp.localScale = (.5, 0, 0)
        self.bone0.sx = self.length0
        self.bone0.v.value = False

        self.bone1, self.bone1_shp = qutil.create_locator(self.ctl_grp, name_loc.replace_desc('1bone'), self.fk_ctl[1])

        self.bone1_shp.localPositionX = self.n_factor * .5
        self.bone1_shp.localScale = (.5, 0, 0)
        self.bone1.sx = self.length1
        self.bone1.v.value = False

        self.ctrn_loc = qutil.create_transform(self.ctl_grp, name_loc.replace_desc('ctrn'), self.apos[1])
        self.eff_loc = qutil.create_transform(self.ctl_grp, name_loc.replace_desc('eff'), self.apos[2])

        # Mid Controler ------------------------------------
        self.mid_cns = qutil.create_transform(self.ctrn_loc, name_cns.replace_desc('mid'), self.ctrn_loc)
        self.mid_ctl = self.add_ctrl(name=name_ctrl.replace_desc('mid'), parent=self.mid_cns, shape='sphereCurve', scale=ctrl_scale, group_exts=None)
        self.mid_ctl.lock('rsv')
        self.mid_ctl.set_matrix(self.ctrn_loc.get_matrix('world'), 'world')

        self.mid_ctl_twst_ref = self.mid_ctl

        self.rollRef = qutil.add_2d_chain(self.root, name_jnt.replace_desc('rollChain'), self.apos[:2], self.normal, self.negate, True)
        self.rollRef[0].v.value = False
        self.tws0_loc = qutil.create_transform(self.rollRef[0], name_loc.replace_desc('tws0'), self.fk_ctl[0])
        self.tws0_rot = qutil.create_transform(self.tws0_loc, name_rot.replace_desc('tws0'), self.fk_ctl[0])
        self.tws1_npo = qutil.create_transform(self.ctrn_loc, name_npo.replace_desc('tws1'), self.ctrn_loc)
        self.tws1_loc = qutil.create_transform(self.tws1_npo, name_loc.replace_desc('tws1'), self.ctrn_loc)
        self.tws1_rot = qutil.create_transform(self.tws1_loc, name_rot.replace_desc('tws1'), self.ctrn_loc)
        self.tws2_npo = qutil.create_transform(self.root, name_npo.replace_desc('tws2'), self.fk_ctl[2])
        self.tws2_loc = qutil.create_transform(self.tws2_npo, name_loc.replace_desc('tws2'), self.fk_ctl[2])
        self.tws2_rot = qutil.create_transform(self.tws2_loc, name_rot.replace_desc('tws2'), self.fk_ctl[2])

        # Divisions ----------------------------------------
        # We have at least one division at the start, the end and one for the
        # elbow. + 2 for elbow angle control
        self.divisions = upper_twist + lower_twist + 3
        self.div_cns = []
        for i in range(self.divisions):
            div_cns = qutil.create_transform(self.ctl_grp, name_loc.replace_desc('div').replace_num(i))
            if i != self.divisions - 1:
                div_ctrl = self.add_ctrl(name=name_ctrl.replace_desc('div').replace_num(i), parent=div_cns, shape='square', scale=ctrl_scale, group_exts=None)
                div_ctrl.get_shapes(type_='lsController')[0].localRotateZ.value = 90
            else:
                div_ctrl = qutil.create_transform(div_cns, name_loc.replace_desc('divctl').replace_num(i))

            div_ctrl.reset()
            div_ctrl.lock('sv')
            self.div_cns.append(div_cns)
            self.jnt_pos.append([div_ctrl, i])

        #self.jnt_pos.append([self.eff_loc, 'end'])

        # # match IK FK references
        self.match_fk0_off = self.add_match_ref(self.fk_ctl[1], self.root, name_npo.replace_desc('matchFk0_npo'), False)
        self.match_fk0 = self.add_match_ref(self.fk_ctl[0], self.match_fk0_off, name_mth.replace_desc('fk0_mth'))
        self.match_fk1_off = self.add_match_ref(self.fk_ctl[2], self.root, name_npo.replace_desc('matchFk1_npo'), False)
        self.match_fk1 = self.add_match_ref(self.fk_ctl[1], self.match_fk1_off, name_mth.replace_desc('fk1_mth'))

        if self.ikTR:
            reference = self.ikRot_ctl
            self.match_ikRot = self.add_match_ref(self.ikRot_ctl, self.fk2_ctl, "ikRot_mth")
        else:
            reference = self.ik_ctl

        self.match_fk2 = self.add_match_ref(self.fk_ctl[2], self.ik_ctl, name_mth.replace_desc('fk2_mth'))
        self.match_ik = self.add_match_ref(self.ik_ctl, self.fk2_ctl, name_mth.replace_desc('ik_mth'))
        self.match_ikUpv = self.add_match_ref(self.upv_ctl, self.fk0_ctl, name_mth.replace_desc('upv_mth'))

        # =====================================================
        # setRelation
        # =====================================================
        """Set the relation beetween object from guide to rig"""
        self.relatives["root"] = self.div_cns[0]
        self.relatives["elbow"] = self.div_cns[upper_twist + 2]
        self.relatives["wrist"] = self.div_cns[-1]
        self.relatives["eff"] = self.eff_loc

        self.jointRelatives["root"] = 0
        self.jointRelatives["elbow"] = upper_twist + 2
        self.jointRelatives["wrist"] = len(self.div_cns) - 1
        self.jointRelatives["eff"] = -1

        self.controlRelatives["root"] = self.fk0_ctl
        self.controlRelatives["elbow"] = self.fk1_ctl
        self.controlRelatives["wrist"] = self.fk2_ctl
        self.controlRelatives["eff"] = self.fk2_ctl

        # here is really don't needed because the name is the same as the alias
        self.aliasRelatives["root"] = "root"
        self.aliasRelatives["elbow"] = "elbow"
        self.aliasRelatives["wrist"] = "wrist"
        self.aliasRelatives["eff"] = "hand"
        self.uihost = self.root_ctrl

        # =====================================================
        # addAttributes
        # =====================================================
        self.blend_att = self.uihost.add_attr('bool', name='blend', niceName='Fk/Ik Blend', defaultValue=1.0, minValue=0, maxValue=1, keyable=True)
        self.roll_att = self.uihost.add_attr('double', name='roll', niceName='Roll', defaultValue=0, minValue=-180, maxValue=180, keyable=True)
        self.armpit_roll_att = self.uihost.add_attr('double', name='aproll', niceName='Armpit Roll', defaultValue=0, minValue=-360, maxValue=360, keyable=True)
        self.scale_att = self.uihost.add_attr('double', name='ikscale', niceName='Scale', defaultValue=1, minValue=0.001, maxValue=99, keyable=True)

        self.maxstretch_att = self.uihost.add_attr('double', name='maxstretch', niceName='Max Stretch', defaultValue=1, minValue=1, maxValue=99, keyable=True)
        self.slide_att = self.uihost.add_attr('double', name='slide', niceName='Slide', defaultValue=.5, minValue=0, maxValue=1, keyable=True)
        self.softness_att = self.uihost.add_attr('double', name='softness', niceName='Softness', defaultValue=0, minValue=0, maxValue=1, keyable=True)
        self.reverse_att = self.uihost.add_attr('double', name='reverse', niceName='Reverse', defaultValue=0, minValue=0, maxValue=1, keyable=True)
        self.roundness_att = self.uihost.add_attr('double', name='roundness', niceName='Roundness', defaultValue=0, minValue=0, maxValue=self.size, keyable=True)
        self.volume_att = self.uihost.add_attr('double', name='volume', niceName='Volume', defaultValue=1, minValue=0, maxValue=1, keyable=True)

        self.global_ik_vis = self.uihost.add_attr('bool', name='global_ik_vis', niceName='Global Ik Vis', defaultValue=0, keyable=False)
        self.mid_ctl_vis = self.uihost.add_attr('bool', name='mid_ctl_vis', niceName='Mid Ctl Vis', defaultValue=0, keyable=False)
        self.twist_vis = self.uihost.add_attr('bool', name='mid_twist_vis', niceName='Twist Vis', defaultValue=0, keyable=False)

        # connect visibility
        for shp in self.ikcns_ctl.get_shapes(type_='lsController'):
            self.global_ik_vis >> shp.v
        self.mid_ctl_vis >> self.mid_cns.v

        # connect twist visibility
        for loc in self.div_cns:
            self.twist_vis >> loc.v

        # Setup ------------------------------------------
        # Eval Fcurve
        self.uihost = self.root

        st_value = self.getDivValues(self.st, self.divisions)
        self.st_att = [self.uihost.add_attr('double', name='stretch_%s' % i, niceName='Stretch %s' % i, defaultValue=st_value[i], minValue=-1, maxValue=0, keyable=True) for i in range(self.divisions)]

        sq_value = self.getDivValues(self.sq, self.divisions)
        self.sq_att = [self.uihost.add_attr('double', name='squash%s' % i, niceName='Squash %s' % i, defaultValue=sq_value[i], minValue=0, maxValue=1, keyable=True) for i in range(self.divisions)]

        # self.st_att = [self.addSetupParam("stretch_%s" % i, "Stretch %s" % i, "double", self.st_value[i], -1, 0) for i in range(self.divisions)]
        # self.sq_att = [self.addSetupParam("squash_%s" % i, "Squash %s" % i, "double", self.sq_value[i], 0, 1) for i in range(self.divisions)]

        self.resample_att = self.uihost.add_attr('bool', name='resample', niceName='Resample', defaultValue=True, keyable=True)
        self.absolute_att = self.uihost.add_attr('bool', name='absolute', niceName='Absolute', defaultValue=False, keyable=True)

        # =====================================================
        # OPERATORS
        # =====================================================
        self.ikHandleUpvRef = qutil.create_quatruped_ik_chain(self.armChainUpvRef[0], self.armChainUpvRef[-1], 'ikSCsolver', self.root)
        self.add_constraint('point', self.ik_ctl, self.ikHandleUpvRef)

        # Visibilities -------------------------------------
        # fk
        fkvis_node = qutil.create_reverse_node(self.blend_att)
        for ctrl in self.fk_ctl:
            ctrl.unlock(attrs='v')
            fkvis_node.outputX >> ctrl.v
            ctrl.lock(attrs='v')

        # ik
        for ctrl in [self.ik_ctl, self.upv_ctl, self.ikcns_ctl]:
            ctrl.unlock(attrs='v')
            self.blend_att >> ctrl.v
            ctrl.lock(attrs='v')

        if self.ikTR:
            for ctrl in [self.ikRot_ctl]:
                ctrl.unlock(attrs='v')
                self.blend_att >> ctrl.v
                ctrl.lock(attrs='v')

        # Controls ROT order -----------------------------------
        self.fk0_ctl.set_rotate_order('XZY')
        self.fk1_ctl.set_rotate_order('XYZ')
        self.fk2_ctl.set_rotate_order('YZX')
        self.ik_ctl.set_rotate_order('XYZ')

        # IK Solver -----------------------------------------
        out = [self.bone0, self.bone1, self.ctrn_loc, self.eff_loc]
        o_node = qutil.create_ikfk2bone_op(out, self.root, self.ik_ref, self.upv_ctl, self.fk_ctl[0], self.fk_ctl[1], self.fk_ref, self.length0, self.length1, self.negate)

        if self.ikTR:
            # connect the control inputs
            outEff_dm = o_node.list_connections(c=True)[-1]

            inAttr = self.ikRot_npo.attr("translate")
            outEff_dm.attr("outputTranslate") >> inAttr

            outEff_dm.outputScale >> self.ikRot_npo.scale
            dm_node = qutil.create_decomposematrix_node(o_node.outB)
            dm_node.outputRotate >> self.ikRot_npo.rotate

            # rotation
            mulM_node = qutil.create_mulmatrix_op(self.ikRot_ctl.worldMatrix, self.eff_loc.parentInverseMatrix)
            intM_node = qutil.create_intmatrix_op(o_node.outEff, mulM_node.output, o_node.blend)
            dm_node = qutil.create_decomposematrix_node(intM_node.output)
            dm_node.attr("outputRotate") >> self.eff_loc.attr("rotate")

            self.ikRot_cns.set_matrix(self.fk2_ctl.get_matrix('world'), space='world')

        # scale: this fix the scalin popping issue
        intM_node = qutil.create_intmatrix_op(self.fk2_ctl.worldMatrix, self.ik_ctl_ref.worldMatrix, o_node.blend)
        mulM_node = qutil.create_mulmatrix_op(intM_node.output, self.eff_loc.parentInverseMatrix)
        dm_node = qutil.create_decomposematrix_node(mulM_node.output)
        dm_node.outputScale >> self.eff_loc.scale

        self.blend_att >> o_node.blend
        if self.negate:
            mulVal = -1
        else:
            mulVal = 1
        qutil.create_mul_node(self.roll_att, mulVal, o_node.roll)
        self.scale_att >> o_node.scaleA
        self.scale_att >> o_node.scaleB
        self.maxstretch_att >> o_node.maxstretch
        self.slide_att >> o_node.slide
        self.softness_att >> o_node.softness
        self.reverse_att >> o_node.reverse

        # Twist references ---------------------------------

        self.add_constraint('point', self.mid_ctl_twst_ref, self.tws1_npo, maintainOffset=False)
        self.mid_ctl.scaleX >> self.tws1_loc.scaleX
        self.add_constraint('orient', self.mid_ctl_twst_ref, self.tws1_npo, maintainOffset=False)

        o_node = qutil.create_mulmatrix_op(self.eff_loc.worldMatrix, self.root.worldInverseMatrix)
        dm_node = qutil.create_decomposematrix_node(o_node.output)
        dm_node.outputTranslate >> self.tws2_npo.translate

        dm_node = qutil.create_decomposematrix_node(o_node.output)
        dm_node.outputRotate >> self.tws2_npo.rotate

        o_node = qutil.create_mulmatrix_op(self.eff_loc.worldMatrix, self.tws2_rot.parentInverseMatrix)
        dm_node = qutil.create_decomposematrix_node(o_node.output)
        self.tws2_rot.set_rotate_order('XYZ')
        dm_node.outputRotate >> self.tws2_rot.rotate

        self.tws0_rot.sx.value = .001
        self.tws2_rot.sx.value = .001

        add_node = qutil.create_add_node(self.roundness_att, .001)
        add_node.output >> self.tws1_rot.sx

        self.armpit_roll_att >> self.tws0_rot.rotateX

        # Roll Shoulder
        qutil.create_quatruped_spline_ik_chain(self.rollRef[0], self.rollRef[-1], parent=self.root, cParent=self.bone0)

        # Volume -------------------------------------------
        distA_node = qutil.create_dis_node(self.tws0_loc, self.tws1_loc)
        distB_node = qutil.create_dis_node(self.tws1_loc, self.tws2_loc)
        add_node = qutil.create_add_node(distA_node.distance, distB_node.distance)
        div_node = qutil.create_div_node(add_node.output, self.root.sx)

        dm_node = qutil.create_decomposematrix_node(self.root.worldMatrix)

        div_node2 = qutil.create_div_node(div_node.outputX, dm_node.outputScaleX)
        self.volDriver_att = div_node2.outputX

        self.locate_jonts.extend([0, upper_twist + 1, len(self.div_cns) - 2, len(self.div_cns) - 1])

        # Divisions ----------------------------------------
        # at 0 or 1 the division will follow exactly the rotation of the
        # controler.. and we wont have this nice tangent + roll

        for i, div_cns in enumerate(self.div_cns):

            subdiv = 40
            if i < (upper_twist + 1):
                perc = i * .5 / (upper_twist + 1.0)
            elif i < (upper_twist + 2):
                perc = .501
            else:
                perc = .5 + (i - upper_twist - 1.0) * .5 / (lower_twist + 1.0)

            perc = max(.001, min(.990, perc))

            # Roll
            if self.negate:
                o_node = qutil.create_rollsplinekine_op(div_cns, [self.tws2_rot, self.tws1_rot, self.tws0_rot], 1.0 - perc, subdiv, True)
            else:
                o_node = qutil.create_rollsplinekine_op(div_cns, [self.tws0_rot, self.tws1_rot, self.tws2_rot], perc, subdiv)

            self.resample_att >> o_node.resample
            self.absolute_att >> o_node.absolute

            # Squash n Stretch
            o_node = qutil.create_squashstretch2_op(div_cns, None, self.volDriver_att.value, 'x')
            self.volume_att >> o_node.blend
            self.volDriver_att >> o_node.driver
            self.st_att[i] >> o_node.stretch
            self.sq_att[i] >> o_node.squash

        # NOTE: next line fix the issue on meters.
        # This is special case becasuse the IK solver from mGear use
        # the scale as lenght and we have shear
        # TODO: check for a more clean and elegant solution instead of
        # re-match the world matrix again
        # transform.matchWorldTransform(self.fk_ctl[0], self.match_fk0_off)
        # transform.matchWorldTransform(self.fk_ctl[1], self.match_fk1_off)
        # transform.matchWorldTransform(self.fk_ctl[0], self.match_fk0)
        # transform.matchWorldTransform(self.fk_ctl[1], self.match_fk1)

        # build a new effector loc to correct the orientation
        if self.ik_up_z.value and (not self.ik_world.value):
            self.eff_loc_correct_orient = qutil.create_transform(self.eff_loc, name_loc.replace_desc('correct'), hand_matrix)
            self.jnt_pos.append([self.eff_loc_correct_orient, 'end'])
        else:
            self.jnt_pos.append([self.eff_loc, 'end'])

        # match IK/FK ref
        self.add_constraint('parent', self.bone0, self.match_fk0_off, mo=True)
        self.add_constraint('parent', self.bone1, self.match_fk1_off, mo=True)

        if self.ikTR:
            self.match_ikRot.set_matrix(self.ikRot_ctl.get_matrix('world'), space='world')
            self.match_fk2.set_matrix(self.fk_ctl[2].get_matrix('world'), space='world')

        #
        self.create_bind_joint(name_jnt)

        self.end_bind_joint.value = self.joint_list[-1]

        # set follow object
        self.follow_ik_ref_attr_obj = self.root_ctrl
        self.follow_ik_ref_obj = self.ik_cns

        self.follow_fk_ref_attr_obj = self.root_ctrl
        self.follow_fk_ref_obj = self.fk0_npo

        self.follow_pole_ref_auto_obj = self.armChainUpvRef[0]
        self.follow_pole_ref_foot_obj = self.ik_ctl
        self.follow_pole_ref_obj = self.upv_cns
        self.follow_pole_ref_attr_obj = self.root_ctrl
