"""
Maya scene utility actions
"""
import json

import maya.cmds as cmds

import lsr.protostar.core.parameter as pa
import lsr.protostar.core.exception as exp

from lsr.maya.rig.base_actions import BaseSceneAction


class NewMayaScene(BaseSceneAction):
    """
    Creates a new Maya scene.
    """

    def run(self):
        """Executes this action."""
        cmds.file(newFile=True, force=True)


class SaveMayaScene(BaseSceneAction):
    """
    Saves the current Maya scene.
    """

    @pa.file_param(ext=('ma', 'mb'))
    def file_path(self):
        """The file path to save the current scene to."""

    def start(self):
        super(SaveMayaScene, self).start()
        world_offset = self.find_actions(
            type_='lsr:Root', from_root=True)
        if world_offset and len(world_offset) == 1:
            data = self.graph._get_data()
            build_data = json.dumps(data)
            world_offset[0].save_graph_data(build_data)

    def run(self):
        """Executes this action."""
        path = self.file_path.value
        if not path:
            raise exp.ActionError('File path is empty.')
        typ = 'mayaAscii' if path.endswith('ma') else 'mayaBinary'
        cmds.file(rename=path)
        cmds.file(save=True, defaultExtensions=False, type=typ, force=True)
        self.info('saved scene to {}'.format(path))


class OpenMayaScene(BaseSceneAction):
    """
    Open exist Maya Scene.
    """

    @pa.file_param(ext=('ma', 'mb'))
    def file_path(self):
        """The file path need to open."""

    def run(self):
        """Executes this action."""
        path = self.file_path.value
        if not path:
            raise exp.ActionError('File path is empty.')

        typ = 'mayaAscii' if path.endswith('ma') else 'mayaBinary'
        cmds.file(path, open=True, f=True)
        self.info('open scene {}, type is {}'.format(path, typ))
