import math
import maya.cmds as cmds
import lsr.maya.rig.userlib.actions.biped_limbs.biped_spine_limb as bs_limb
import lsr.maya.rig.constants as const
from lsr.maya.nodezoo.node import Joint

from lsr.maya.rig.meta_limb import MetaLimb

import lsr.protostar.core.parameter as pa

from lsr.maya.nodezoo.node import MatrixConstraint
from lsr.maya.nodezoo.node import NurbsCurve
from lsr.maya.nodezoo.node import NurbsCurveCns
from lsr.maya.nodezoo.node import SlideCurve
from lsr.maya.nodezoo.node import MotionPath
from lsr.maya.nodezoo.node import InterpolateMatrix
from lsr.maya.nodezoo.node import MultiplyMatrix
from lsr.maya.nodezoo.node import MultiplyDivide
from lsr.maya.nodezoo.node import BlendColors
from lsr.maya.nodezoo.node import Node
from lsr.maya.standard.name import NodeName
import lsr.maya.rig.joint_utils as jutil
import lsr.maya.maya_math as mmath


class RoboticArm(bs_limb.BipedSpineIKFK01):
    """RoboticArm limb class
    Spline spine system which has IK and FK works in the same time.

    :limb type: roboticArm
    """

    _LIMB_TYPE = 'roboticArm'
    _DEFAULT_SIDE = 'L'
    _UI_ICON = 'spine'

    # --- input parameters

    @pa.list_param(item_type='str', default=['Root:root_M_CTRL', 'Master:Root_M_RIGJNT'])
    def follow_refs(self):
        """TODO doc"""

    @pa.int_param(default=9, min_value=3)
    def num_joints(self):
        """The number of joints."""

    @pa.enum_param(items=const.AXIS_TYPE, default='-Z')
    def second_axis(self):
        """The second_axis up axis of self coordinates."""

    @pa.bool_param(default=False)
    def central_ik(self):
        """If True, central ik ctrl."""

    @pa.int_param(default=1, min_value=1,max_value=20)
    def ik_ctrl_num(self):
        """The number of ik ctrl."""

    # --- end of parameter definition

    def end(self):
        """Adjust default properties."""
        super(RoboticArm, self).end()
        self.maxstretch_attr.value = 1.0
        self.maxsquash_attr.value = 0.00

    def limb_template_data(self, *args, **kwargs):
        """limb template data"""
        limb_name = kwargs.get('name_part', 'roboticArm')
        name_side = kwargs.get('name_side', 'L')
        count = self.num_joints.value
        cmds.select(deselect=True)
        joint_chain = []
        for i in range(count):
            jt_node = Joint.create(
                name='{}_{:02d}_{}_RIGJNT'.format(limb_name, (i+1), name_side),
                p=(0, 0, i * 3))
            joint_chain.append(jt_node)
        joint_chain[0].orient_chain()
        cmds.select(deselect=True)
        return joint_chain

    def create_joint_structure(self, **kwargs):
        """
        Create joint structure
        """
        extra_scale = kwargs.get('extra_scale', False)
        kwargs['ctrls'] = self.extra_ctrls
        kwargs['tag_ctrls'] = self.extra_ctrls
        self.joint_list = MetaLimb.create_joint_structure(self, **kwargs)
        # self.joint_list = super(BipedSpineIKFK01, self).create_joint_structure(**kwargs)

        if extra_scale:
            for index, joint in enumerate(self.joint_list):
                jnt_name = NodeName(joint.name)
                jnt_name = jnt_name.replace_desc('SCALE')
                scale_jnt = joint.duplicate(name=jnt_name, parentOnly=True)[0]
                scale_jnt.radius.value = 2
                scale_jnt.set_parent(joint)
                self.extra_ctrls[index].scale >> scale_jnt.scale

        return self.joint_list

    def add_objects(self):
        self.auto_bend.value = True
        group_exts = self.group_exts.value
        side = self.side.enum_value
        local_scale = 1
        clean_nodes = []
        joint_count = len(self.rig_skeleton[0])
        self.apos = [mmath.get_position(n) for n in self.rig_skeleton[0]]

        self.normal = mmath.get_normal_from_pos(self.apos)
        if self.normal.length() == 0:
            self.normal.x = 1.0

        self.fk_ctrls = []
        self.extra_ctrls = []
        axis = self.main_axis.enum_value
        second_axis = self.second_axis.enum_value

        axis_index = const.AXIS_TYPE.index(axis)

        scale_offset = [1.0, 1.0, 1.0]
        t_matrix = None
        for i, pos_vec in enumerate(self.apos):
            if i < joint_count - 1:
                t_matrix = mmath.get_transform_looking_at(
                    self.apos[i], self.apos[i + 1], self.normal,
                    '{}{}'.format(axis.lower(), second_axis.lower()),
                    self.negate)

                t_matrix = mmath.set_matrix_position(t_matrix, self.apos[i])
                local_scale = mmath.distance(self.apos[i], self.apos[i + 1]) / 1.5
                scale_offset = [local_scale / 10.0, local_scale, local_scale]

            else:
                t_matrix = mmath.set_matrix_position(t_matrix, self.apos[i])

            ctrl = self.add_ctrl(
                name=NodeName(self.long_name, num=i + 1, side=side, ext='FKCTRL'),
                shape='cube',
                color=(1, 0, 0),
                scale=scale_offset,
                group_exts=group_exts)
            ctrl.get_group(group_exts[0]).set_matrix(t_matrix, space='world')
            ctrl.get_group(group_exts[0]).set_scale([1, 1, 1])
            self.fk_ctrls.append(ctrl)

            extra_ctrl = self.add_ctrl(
                parent=ctrl,
                name=NodeName(self.long_name, num=(i + 1), side=side, ext='CTRL'),
                shape='triangle',
                pos=(0, 0, 0),
                color=(0, 1, 0),
                scale=(local_scale, local_scale, local_scale),
                rot=const.CTRL_ROTATION_OFFSET[axis],
                group_exts=['PLC']
            )
            extra_ctrl.plc_node.set_matrix(t_matrix, space='world')
            extra_ctrl.plc_node.set_scale([1, 1, 1])
            self.extra_ctrls.append(extra_ctrl)

            if not self.enable_scale.value:
                ctrl.lock('s')
                extra_ctrl.lock('s')

            if i > 0:
                parent_ctrl = self.fk_ctrls[i - 1]
                ctrl.get_group(group_exts[0]).set_parent(parent_ctrl)

        self.fk_ctrls[0].get_group(group_exts[0]).set_parent(self.ctrl_root)

        self.create_joint_structure(extra_scale=self.enable_scale.value)
        joint_chain = jutil.JointChain(
            start=self.joint_list[0], end=self.joint_list[-1])

        # Set IK Rig
        ik_count = self.ik_ctrl_num.value

        name = NodeName(self.limb_root.value, side=side, desc='MST', ext='NBCRV')
        curve_ctrls = self.fk_ctrls
        curve_mst = NurbsCurve.create(curve_ctrls,
                                      name=name,
                                      degree=1,
                                      uniform=True,
                                      spans=ik_count,
                                      simple=True)

        cns_node = NurbsCurveCns.create(name, curve_shape=curve_mst)

        points_mst = curve_mst.get_points(space='world')
        ik_ctrls = []

        # Create and Set IK Controllers
        input_loc_grp_name = NodeName(name, desc='INLOC', ext='GRP')
        input_loc_grp = Node.create('transform', name=input_loc_grp_name)

        ik_ctrls_grp_name = NodeName(name, desc='IKCTRLS', ext='GRP')
        ik_ctrls_grp = Node.create('transform', name=ik_ctrls_grp_name)
        ik_ctrls_grp.align(self.fk_ctrls[0])

        for i, point in enumerate(points_mst):
            loc_name = NodeName(name, num=i + 1, desc='ik', ext='INLOC')
            ik_input = Node.create('transform', name=loc_name)
            ik_input.set_translation(point, space='world')
            if 0 < i < len(points_mst) - 1:
                tangent = curve_mst.closest_tangent(ik_input)
                tangent_node = ik_input.add_child(loc_name.replace_ext('IPTAIM'))
                tangent_node.set_translation(tangent, space='object')
                tangent_node.set_parent('world')
                ik_input_pos = mmath.get_position(ik_input)
                tangent_pos = mmath.get_position(tangent_node)

                tangent_matrix = mmath.get_transform_looking_at(
                    ik_input_pos, tangent_pos, self.normal,
                    '{}{}'.format(axis.lower(), second_axis.lower()),
                    self.negate)

                tangent_matrix = mmath.set_matrix_position(tangent_matrix, ik_input_pos)
                ik_input.set_matrix(tangent_matrix, space='world')
                ik_input.set_scale([1, 1, 1])
                cmds.delete(tangent_node)

            elif i == 0:
                ik_input.align(self.fk_ctrls[i])

            else:
                ik_input.align(self.fk_ctrls[-1])

            ik_scale = local_scale * 0.5
            ctrl = self.add_ctrl(
                xform=ik_input,
                ext='IKCTRL',
                shape='sphereCurve',
                scale=(ik_scale, ik_scale, ik_scale),
                rot=const.CTRL_ROTATION_OFFSET[axis],
                group_exts=group_exts)

            ik_ctrls.append(ctrl)

            MatrixConstraint.create(ctrl, ik_input, connect_srt='trs')
            ik_input.worldMatrix[0] >> cns_node.inputs[i]

            # Clean up
            ik_input.set_parent(input_loc_grp)
            ctrl.get_group(group_exts[0]).set_parent(ik_ctrls_grp)

        ik_root_ctrl = ik_ctrls[0]
        ik_ctrl_scale = 2.0 * local_scale
        ik_root_ctrl.shape.shape_type = 'circle'
        ik_root_ctrl.shape.localScaleX.value = ik_ctrl_scale
        ik_root_ctrl.shape.localScaleY.value = ik_ctrl_scale
        ik_root_ctrl.shape.localScaleZ.value = ik_ctrl_scale

        ik_end_ctrl = ik_ctrls[-1]
        ik_ctrls[-2].get_group(group_exts[0]).set_parent(ik_end_ctrl)
        tan_ik_ctrls = ik_ctrls[1:-1]
        ik_end_ctrl.shape.shape_type = 'circle'
        ik_end_ctrl.shape.localScaleX.value = ik_ctrl_scale
        ik_end_ctrl.shape.localScaleY.value = ik_ctrl_scale
        ik_end_ctrl.shape.localScaleZ.value = ik_ctrl_scale

        [ctrl.lock('rs') for ctrl in tan_ik_ctrls]
        ik_root_ctrl.lock('s')
        ik_end_ctrl.lock('s')

        # Set slider curve deformer
        point_array = [(i, 0, 0) for i in range(10)]
        slv_name = NodeName(
            self.limb_root.value,
            side=side,
            desc='SLV',
            ext='NBCRV')
        curve_slv = NurbsCurve.create(point_array,
                                      name=slv_name,
                                      degree=3,
                                      spans=7,
                                      uniform=True,
                                      simple=True)

        self.slide_node = SlideCurve.create(slv_name,
                                            curve_shape=curve_slv,
                                            mst_curve=curve_mst)
        self.slide_node.slave_length.value = self.slide_node.master_length.value - 0.01

        self.slide_node.maxsquash.value = 0.0

        # create FK CNS
        ref_grp_name = NodeName(
            self.limb_root.value,
            side=side,
            desc='REF',
            ext='GRP')
        ref_grp = Node.create('transform', name=ref_grp_name)
        ref_grp.align(self.fk_ctrls[0], skipRotate=True)
        inter_ref = ref_grp.duplicate(
            name=ref_grp_name.replace_desc('interMat'),
            parentOnly=True)[0]
        inter_ref.align(self.fk_ctrls[0])

        betweent_grp_name = NodeName(
            self.limb_root.value, side=side, desc='BET', ext='GRP')
        betweent_grp = Node.create('transform', name=betweent_grp_name)
        last_between_node = None

        fk_cns_list = []
        fk_cns_count = len(self.fk_ctrls[:-1])

        demat_root_scale = None

        # up rotate twist transform : up_lvl_node
        name = NodeName(ik_root_ctrl, num=None, ext='LVL')
        up_lvl_node = Node.create('transform', name=name)
        up_lvl_node.align(ik_end_ctrl)
        up_lvl_node.set_parent(ik_end_ctrl)

        # create MotionPath
        upAxis_index = const.AXIS_TYPE.index(second_axis)
        between_list = []
        fk_offset_list = []
        for ctrl in self.fk_ctrls:
            fk_cns_name = NodeName(
                ctrl.long_name, side=side, desc='fk', ext='CNS')
            fk_cns_loc = Node.create('transform', name=fk_cns_name)
            fk_cns_loc.inheritsTransform.value = False
            fk_cns_list.append(fk_cns_loc)
            if len(fk_cns_list) > 1:
                fk_cns_loc.set_parent(fk_cns_list[-2])

            fk_cns_loc.align(ctrl)

            # set motion path constraint
            loc_index = fk_cns_list.index(fk_cns_loc)
            u = curve_slv.closest_param(fk_cns_loc)
            # self.warn(u)
            mp_node = MotionPath.create(fk_cns_name, fk_cns_loc, curve_slv,
                                        num=loc_index + 1, u=u,
                                        cnsType=True, tangent=True)

            # front axis is 'Y' or 'X'
            mp_node.frontAxis.value = axis_index % 3
            if axis_index > 2:
                mp_node.inverseFront.value = True

            # front axis is same with World 'X' axis
            upAxis_value = upAxis_index % 3
            mp_node.upAxis.value = upAxis_value
            if upAxis_index < 3:
                mp_node.inverseUp.value = True

            up_ref_name = NodeName(
                ctrl.long_name, side=side, desc='up', ext='REF')
            up_ref = Node.create('transform', name=up_ref_name)
            up_ref.align(self.fk_ctrls[0])
            up_ref.set_parent(ref_grp)

            rot_ref = up_ref.duplicate(name=up_ref_name.replace_desc('rot'),
                                       parentOnly=True)[0]

            offset_pos = [0, 0, 0]
            offset_pos[upAxis_value] = 1.0
            up_ref.set_translation(offset_pos)

            inter_matrix = InterpolateMatrix.create(ctrl,
                                                    objA=ik_root_ctrl,  # inter_ref
                                                    objB=up_lvl_node)

            blend = (1.0 / (fk_cns_count - 1)) * loc_index
            inter_matrix.create_decomposeMatrix(obj=rot_ref,
                                                blend=min(1.0, blend),
                                                rotate=True)
            up_ref.constrain('parent', rot_ref, maintainOffset=True)
            up_ref.translate >> mp_node.worldUpVector

            # connect fkctrl plc node and set sdk inverse value
            fk_offset_pos = Node.create(
                'transform', name=fk_cns_name.replace_ext('OFFSET'))
            fk_offset_pos.align(ctrl)
            fk_offset_pos.inheritsTransform.value = True
            if not fk_offset_list:
                fk_offset_pos.set_parent(self.ws_root)
            else:
                fk_offset_pos.set_parent(fk_offset_list[-1])
            fk_offset_list.append(fk_offset_pos)

            matrix_a_node = fk_offset_pos
            if len(fk_offset_list) > 1:
                matrix_b_node = fk_offset_list[-2]
            else:
                matrix_b_node = self.ctrl_root
            mul_matrix = MultiplyMatrix.create(ctrl,
                                               objA=matrix_a_node,
                                               objB=matrix_b_node)

            between_name = NodeName(fk_offset_pos.long_name, ext='BET')
            between = fk_offset_pos.duplicate(
                name=between_name, parentOnly=True)[0]
            between.inheritsTransform.value = True
            between_list.append(between)

            if last_between_node:
                if not demat_root_scale:
                    name = NodeName(
                        self.limb_root.value, desc='SCALE', ext='DPMAT')
                    demat_root_scale = Node.create(
                        'decomposeMatrix', name=name)
                    self.limb_root.value.worldMatrix >> demat_root_scale.inputMatrix

                # connect rotate
                between.set_parent(last_between_node)
                demat_node = mul_matrix.create_decomposeMatrix(
                    obj=between, rotate=True)

                # connect translate
                name = NodeName(between, desc='scldiv', ext='MDNODE')
                scd_node = Node.create('multiplyDivide', name=name)
                scd_node.operation.value = 2
                scd_node.input1.value = (1, 1, 1)
                demat_root_scale.outputScale >> scd_node.input2

                name = NodeName(scd_node, desc='sclmul', ext='MDNODE')
                scm_node = Node.create('multiplyDivide', name=name)
                scm_node.operation.value = 1
                scd_node.output >> scm_node.input1
                demat_node.outputTranslate >> scm_node.input2
                scm_node.output >> between.translate

            else:
                between.set_parent(betweent_grp)
                mul_matrix.create_decomposeMatrix(
                    obj=between, translate=True, rotate=True)

            name = NodeName(ctrl, desc='TEMP', ext='LOC')
            temp_loc = Node.create('transform', name=name)
            temp_loc.align(ctrl.sdk_node)
            temp_mul_matrix = MultiplyMatrix.create(temp_loc,
                                                    objA=temp_loc,
                                                    objB=ctrl.sdk_node,
                                                    parentInverse=True)
            temp_demat_node = temp_mul_matrix.create_decomposeMatrix(
                obj=ctrl.sdk_node, translate=True, rotate=True, scale=True)

            between.translate >> ctrl.get_group(group_exts[0]).translate
            between.rotate >> ctrl.get_group(group_exts[0]).rotate
            clean_nodes.extend([temp_demat_node, temp_mul_matrix, temp_loc])

            last_between_node = between


        # create ik tan attrs
        name = NodeName(ik_root_ctrl.long_name, ext='DPMAT')
        demat_ik_root = Node.create('decomposeMatrix', name=name)
        ik_root_ctrl.worldMatrix >> demat_ik_root.inputMatrix

        name = NodeName(ik_end_ctrl.long_name, ext='DPMAT')
        demat_ik_end = Node.create('decomposeMatrix', name=name)
        ik_end_ctrl.worldMatrix >> demat_ik_end.inputMatrix

        name = NodeName(name, side=side, num=None, desc='RE', ext='DISTNODE')
        dist_node = Node.create('distanceBetween', name=name)

        demat_ik_root.outputTranslate >> dist_node.point1
        demat_ik_end.outputTranslate >> dist_node.point2

        name = NodeName(name, desc='scldiv', ext='MDNODE')
        scldiv_node = Node.create('multiplyDivide', name=name)
        scldiv_node.operation.value = 2
        dist_node.distance >> scldiv_node.input1X
        demat_ik_root.outputScaleX >> scldiv_node.input2X

        name = NodeName(name, desc='distdiv', ext='MDNODE')
        distdiv_node = Node.create('multiplyDivide', name=name)
        distdiv_node.operation.value = 2
        scldiv_node.outputX >> distdiv_node.input1X
        dist = ik_end_ctrl.get_distance_to(ik_root_ctrl)
        distdiv_node.input2X.value = dist

        for i, ik_ctrl in enumerate(tan_ik_ctrls):
            tan_attr = self.add_limb_attr(
                'float', name='tan{}'.format(
                    i + 1), keyable=True, minValue=0, defaultValue=1)
            name = NodeName(
                self.limb_root.value,
                desc='tanA',
                num=i + 1,
                ext='MDNODE')
            mul_node = Node.create('multiplyDivide', name=name)
            mul_node.operation.value = 1
            tan_attr >> mul_node.input1X

            axis_dict = {'X': 0, 'Y': 1, 'Z': 2}
            pos = ik_ctrl.get_group(group_exts[0]).get_translation()
            t_value = pos[axis_dict[axis]]
            inverse_value = t_value / distdiv_node.outputX.value
            mul_node.input2X.value = inverse_value

            name = NodeName(
                self.limb_root.value,
                desc='tanB',
                num=i + 1,
                ext='MDNODE')
            mul02_node = Node.create('multiplyDivide', name=name)
            mul02_node.operation.value = 1

            mul_node.outputX >> mul02_node.input1X
            distdiv_node.outputX >> mul02_node.input2X

            mul02_node.outputX >> getattr(
                ik_ctrl.get_group(
                    group_exts[0]),
                'translate{}'.format(axis))

        # for ik_ctrl in ik_ctrls[1:]:
        #     if ik_ctrl != ik_ctrls[-2]:
        #         ik_ctrl.get_group(group_exts[0]).set_parent(ik_root_ctrl)

        # Added rigging of whether to lock the pelvis and chest orientation
        blendColor_nodes = []
        lock_cns_list = [fk_cns_list[0], fk_cns_list[-1]]
        lock_ik_ctrls = [ik_root_ctrl, ik_end_ctrl]
        for cns, ik_ctrl in zip(lock_cns_list, lock_ik_ctrls):
            name = NodeName(cns, desc='LOCK', ext='DPMAT')
            lock_demat = Node.create('decomposeMatrix', name=name)
            ik_ctrl.worldMatrix >> lock_demat.inputMatrix

            # ori_rot_plug is [motionPath.rotate]
            ori_rot_plug = cns.list_connections(attr='rotate',
                                                source=True,
                                                destination=False,
                                                plugs=True) or [None]

            bcs_node = BlendColors.create(cns,
                                          input01=lock_demat.outputRotate,
                                          input02=ori_rot_plug[0],
                                          output=cns.rotate)

            blendColor_nodes.append(bcs_node)

        self.ctrl_leaf_parent = self.fk_ctrls[-1]

        # Add Custom Attributes
        self.add_custom_attrs(blendColor=blendColor_nodes,
                              sdk_nodes=[self.fk_ctrls[0].sdk_node,
                                         self.fk_ctrls[-1].sdk_node])
        self.maxsquash_attr.value = 0.0

        for fk_cns_loc, fk_offset_pos in zip(fk_cns_list, fk_offset_list):
            self.add_constraint(
                'parent', fk_cns_loc, fk_offset_pos, maintainOffset=True)
        # sort out
        name = NodeName(self.limb_root.value, desc='NBCRV', ext='GRP')
        curve_grp = Node.create('transform', name=name)
        curve_grp.set_parent(self.limb_root.value)
        curve_grp.visibility.value = False
        curve_slv.get_parent().set_parent(curve_grp)
        curve_mst.get_parent().set_parent(curve_grp)

        input_loc_grp.set_parent(self.ws_root)
        fk_cns_list[0].set_parent(self.ws_root)
        ref_grp.set_parent(self.ws_root)
        inter_ref.set_parent(self.ws_root)
        betweent_grp.set_parent(self.ws_root)
        ik_ctrls_grp.set_parent(self.ctrl_root)

        if self.central_ik.value:
            v1 = tan_ik_ctrls[0].get_translation(space='world', as_tuple=False)
            v2 = tan_ik_ctrls[-1].get_translation(
                space='world', as_tuple=False)
            central_pos = mmath.vector_linear_interpolate(v1, v2, 0.5)
            r1 = tan_ik_ctrls[0].get_rotation(space='world', as_tuple=False)
            r2 = tan_ik_ctrls[-1].get_rotation(space='world', as_tuple=False)
            central_rot = mmath.vector_linear_interpolate(r1, r2, 0.5)

            central_rot = mmath.radians2degrees(central_rot)

            # local_scale *= 1.5
            central_ctrl = self.add_ctrl(
                name=NodeName(tan_ik_ctrls[0], num=None, desc='CENTRAL'),
                ext='IKCTRL',
                shape='sphereCurve',
                color=(0.5, 0.5, 1),
                scale=(local_scale, local_scale, local_scale),
                group_exts=group_exts)
            central_ctrl.lock('rsv')
            ik_ctrls.append(central_ctrl)

            central_ctrl.get_group(
                group_exts[0]).set_translation(
                central_pos,
                space='world')
            central_ctrl.get_group(
                group_exts[0]).set_rotation(
                central_rot,
                space='world')

            # central_ctrl.translate >> tan_ik_ctrls[0].get_group(group_exts[1]).translate
            # central_ctrl.translate >> tan_ik_ctrls[-1].get_group(group_exts[1]).translate
            tan_ik_ctrls_count = len(tan_ik_ctrls)
            mid_index = tan_ik_ctrls_count // 2
            for tan_index in range(tan_ik_ctrls_count):
                dist = abs(tan_index - mid_index)
                if dist == 0:
                    central_ctrl.translate >> tan_ik_ctrls[tan_index].get_group(group_exts[1]).translate
                else:
                    _mul_node = MultiplyDivide.create(
                        name=NodeName(central_ctrl, desc='dist', num=tan_index, ext='MDNODE'))
                    _mul_node.operation.value = 1
                    central_ctrl.tx >> _mul_node.input1X
                    central_ctrl.ty >> _mul_node.input1Y
                    central_ctrl.tz >> _mul_node.input1Z

                    decay_factor = 0.3
                    decay_value = math.exp(-decay_factor * dist)

                    _mul_node.input2X.value = decay_value
                    _mul_node.input2Y.value = decay_value
                    _mul_node.input2Z.value = decay_value
                    _mul_node.output >> tan_ik_ctrls[tan_index].get_group(group_exts[1]).translate

            inter_matrix = InterpolateMatrix.create(central_ctrl.get_group(group_exts[1]),
                                                    objA=tan_ik_ctrls[0].get_group(group_exts[0]),
                                                    objB=tan_ik_ctrls[-1].get_group(group_exts[0]))
            inter_matrix.blend.value = 0.5
            mul_matrix = MultiplyMatrix.create(central_ctrl.get_group(group_exts[1]))
            inter_matrix.outputMatrix >> mul_matrix.matrixA
            central_ctrl.get_group(group_exts[1]).parentInverseMatrix >> mul_matrix.matrixB
            mul_matrix.create_decomposeMatrix(
                central_ctrl.get_group(group_exts[1]),
                translate=True,
                rotate=True,
                scale=True)
            central_ctrl.get_group(group_exts[0]).set_parent(ik_root_ctrl)

        ik_vis = self.add_limb_attr('bool', name='ik_vis',
                                    keyable=False, defaultValue=True)
        fk_vis = self.add_limb_attr('bool', name='fk_vis',
                                    keyable=False, defaultValue=True)

        tweak_vis = self.add_limb_attr('bool', name='tweak_vis',
                                       keyable=False, defaultValue=True)

        # lock ctrl attributes
        for ik_ctrl in ik_ctrls:
            shape = ik_ctrl.get_shapes(type_='lsController')
            if not shape:
                continue
            shape = shape[0]
            ik_vis >> shape.visibility

        for fk_ctrl in self.fk_ctrls:
            shape = fk_ctrl.get_shapes(type_='lsController')
            if not shape:
                continue
            shape = shape[0]
            fk_vis >> shape.visibility

        for ctrl in self.extra_ctrls:
            shape = ctrl.get_shapes(type_='lsController')
            if not shape:
                continue
            shape = shape[0]
            tweak_vis >> shape.visibility
            ctrl.plc_node.lock('all')

        cmds.delete(clean_nodes)

        self.ik_bottom_plug.value = ik_root_ctrl.get_group(group_exts[0])
        self.fk_chest_plug.value = self.fk_ctrls[-1]
        self.ik_tip_plug.value = ik_end_ctrl

        self.info("Finish spine setup")

    def set_follow_data(self):
        """set follow data"""
        assemble_limb = self.get_assemble_limb()
        if not assemble_limb:
            return

        group_exts = self.group_exts.value

        build_driven_locators = self.fk_ctrls
        build_skin_joints = self.joint_list
        rig_skeleton = self.rig_skeleton[0]

        num_driven_locator = len(build_driven_locators)
        num_rig_skeleton = len(rig_skeleton)
        if num_driven_locator == num_rig_skeleton:
            # add anchor
            for rig, skin, loc in zip(rig_skeleton, build_skin_joints, build_driven_locators):
                assemble_limb.assembly_follow.add_anchor(rig, skin, loc)
        top_ik_grp = self.ik_tip_plug.value.get_group(group_exts[2])

        self.follow_ref_objs = [top_ik_grp]
        if self.ik_bend_plug.value:
            self.follow_ref_objs.append(self.ik_bend_plug.value.get_group(group_exts[0]))

        self.follow_ref_attr_obj = self.limb_root.value.shape

        ik_refs = self.follow_refs.value
        if self.parent_joint.value != 'Root_M_RIGJNT':
            ik_refs.insert(0, 'Parent:{}'.format(self.parent_joint.value))

        for follow_ref_obj in self.follow_ref_objs:
            follow_ref_obj.unlock(attrs='all')
            data = {'obj': follow_ref_obj, 'attr_obj': self.follow_ref_attr_obj, 'refs_name': ik_refs}
            assemble_limb.assembly_follow.add_follow_constraint(constraint_attr='tr',
                                                                attr_name='follow_ref',
                                                                default_value=1,
                                                                **data)
