QWidget
{
    font-family : Comic Sans MS;
    background-color : rgb(27, 28, 30);
    color : rgb(202, 207, 210);
}
QLabel
{
    font-size : 15px;
    background-color : rgb(60, 80, 70);
    padding-top : 0px;
    padding-bottom : 0px;
    padding-left : 10px;
    padding-right : 10px;
    color : rgb(202, 207, 210);
}

QLabel:hover
{
    color : rgb(0, 255, 0);
}



QWidget#InterpolateIt
{
    background-color : rgb(12,1,1);
}

QWidget#UA_SetItemTool_Window
{
    background-color : rgb(12,1,1);
}

QWidget:disabled
{
    color : rgb(112, 117, 120);
}


QLineEdit
{
    border-style : none;
    font-size : 15px;
    background-color : rgb(50,50,50);
    border: 2px solid rgb(9, 10, 12);
    border-radius: 5px;
    padding-top : 0px;
    padding-bottom : 0px;
    padding-left : 10px;
    padding-right : 10px;
}

QPushButton
{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgb(53, 57, 60), stop:1 rgb(33, 34, 36));
    shadow : rgb(9, 10, 12);
    border: 2px solid rgb(9, 10, 12);
    border-radius: 5px;
    padding-top : 0px;
    padding-bottom : 0px;
    padding-left : 10px;
    padding-right : 10px;
    color : rgb(202, 207, 210);
    min-width: 22px;

    height: 20px;
}

QPushButton:disabled
{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgb(33, 37, 40), stop:1 rgb(13, 14, 16));
}

QPushButton:hover
{
    color : rgb(0, 255, 0);
}

QPushButton:pressed,
QPushButton:on
{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgb(20, 21, 23), stop:1 rgb(48, 49, 51));
    padding-top : 5px;
    color : rgb(0, 255, 0);
}

QPushButton#roundedButton
{
    border-radius: 20px;
}

QSlider
{
    height: 24;
}

QSlider::groove:horizontal
{
    border : 1px solid rgb(9, 10, 12);
    border-radius: 11px;
    background: rgb(19,  20,  22);
}

QSlider::handle:horizontal
{
    border : 2px solid rgb(9, 10, 12);
    border-radius: 11px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgb(53, 57, 60), stop:1 rgb(33, 34, 36));
    width: 18;
}

QSlider::handle:horizontal:disabled
{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgb(33, 37, 40), stop:1 rgb(13, 14, 16));
}

QSlider::handle:horizontal:hover,
QSlider::handle:horizontal:pressed
{
    border : 2px solid rgb(0, 255, 0);
}

QRadioButton
{
    background-color : rgb(27, 28, 30);
    padding-top : 5px;
    color : rgb(207, 207, 207);
    font-size : 12px;
    font-weight: bold;
}

QRadioButton:checked
{
    color : rgb(255, 150, 0);
}

QRadioButton::indicator::unchecked
{
    background-color : rgb(27, 88, 30);
}

QGroupBox
{
    background-color : rgb(37, 38, 37);
    padding-top : 5px;
    color : rgb(200, 200, 200);
    font-size : 12px;
    font-weight: bold;
}