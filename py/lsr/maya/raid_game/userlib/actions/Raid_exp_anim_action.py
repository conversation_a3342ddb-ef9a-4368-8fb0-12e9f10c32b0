# -*- coding: utf-8 -*-
# @Author: <PERSON>
# @Date:   2023-04-10 17:09:46
# @Last Modified by:   <PERSON>
# @Last Modified time: 2025-02-08 17:12:09

"""
Export Animation action of Raid_Game
"""
import os
import re

from maya import cmds
from maya import mel

from lsr.maya.nodezoo.node import Node
from lsr.maya.userlib.actions.exporter_animation import ExportAnimation
import lsr.protostar.core.parameter as pa
import lsr.maya.animtools.fbx_exporter.export_utils as utils
import lsr.protostar.core.exception as exp
import lsr.maya.rig.rig_global as rg
from lsr.fbxsdk.FBX_Scene import FBX_Class
from collections import Counter
from lsr.fbxsdk import FbxCommon

class Raid_exp_anim_action(ExportAnimation):
    """
    Raid Export Animation
    """

    _EXCEPTION_IGNORE = True

    # --- input parameters
    @pa.bool_param(default=True)
    def export_body(self):
        """If True, enable body animation export."""

    @pa.bool_param(default=True)
    def export_weapon(self):
        """If True, enable weaponanimation export."""

    @pa.bool_param(default=True)
    def export_prop(self):
        """If True, enable prop animation export."""

    @pa.bool_param(default=True)
    def export_face(self):
        """If True, enable face animation export."""

    @pa.bool_param(default=True)
    def switch_combined(self):
        """If True, export combined(body and weapon) at origin point."""

    @pa.bool_param(default=True)
    def switch_weapon_origin(self):
        """If True, export weapon at origin point."""

    @pa.bool_param(default=True)
    def switch_prop_origin(self):
        """If True, export weapon at origin point."""

    @pa.bool_param(default=True)
    def switch_output_mesh(self):
        """是否添加mesh一起输出fbx动画"""

    @pa.bool_param(default=True)
    def switch_output_dyjoint(self):
        """是否输出挂件骨骼动画"""

    @pa.bool_param(default=True)
    def switch_exp_current_scene(self):
        """是否只输出当前场景"""

    @pa.bool_param(default=True)
    def switch_frame_split(self):
        """是否以拆分帧的方式输出"""

    @pa.list_param(item_type='str')
    def exported_chr_bones(self):
        """exported chr bones list"""

    @pa.list_param(item_type='str')
    def exported_weapon_bones(self):
        """exported weapon bones list"""

    @pa.list_param(item_type='str')
    def exported_prop_bones(self):
        """exported prop bones list"""

    @pa.list_param(item_type='str')
    def exported_face_bones(self):
        """exported face bones list"""

    @pa.list_param(item_type='list')
    def frame_split_frame(self):
        """拆分帧的列表"""

    @staticmethod
    def save_file(save_as=True):
        if save_as:
            save_path = cmds.fileDialog2(fileMode=0, caption='Save Scene As',
                                         dialogStyle=2, fileFilter='Maya ASCII (*.ma);;Maya Binary (*.mb)')
            if save_path:
                ext = 'mayaAscii' if os.path.splitext(save_path[0])[-1].lower() == '.ma' else 'mayaBinary'
                cmds.file(rename=save_path[0])
                cmds.file(save=True, type=ext)
        else:
            cmds.file(save=True)

    def run(self):
        """
        Raid Anim Export run method
        # kwargs = {'combined': [['body_root','weapon_root']], 
        #           'single' : ['body_root','weapon_root']}

        """
        if self.switch_exp_current_scene.value:
            if cmds.file(query=True, modified=True):
                result = cmds.confirmDialog(title='提示',
                                            message='当前场景未保存，是否保存？',
                                            button=['save', 'save as..', 'cancel'],
                                            defaultButton='save')
                if result == 'cancel':
                    return
                elif result == 'save':
                    cur_file_name = cmds.file(query=True, sceneName=True)
                    if not cur_file_name:
                        self.save_file(save_as=True)
                    else:
                        self.save_file(save_as=False)
                else:  # save as..
                    self.save_file(save_as=True)

            cur_file_name = cmds.file(query=True, sceneName=True)
            self.file_name.value = cur_file_name
        else:
            super(Raid_exp_anim_action, self).run()
        # set export parameter
        cmds.refresh(suspend=True)
        self.setting = {}
        self.exported_chr_bones.value = self.get_chr_roots()
        self.exported_weapon_bones.value = self.get_weapon_roots()
        self.exported_prop_bones.value = self.get_prop_roots()
        self.exported_face_bones.value = self.get_face_roots()

        if self.switch_combined.value:
            if self.export_body.value and self.export_weapon.value:
                combined_values = []
                combined_values.extend(self.exported_chr_bones.value)
                combined_values.extend(self.exported_weapon_bones.value)
                self.setting['combined'] = [combined_values]

                single_values = []
                single_values.extend(self.exported_prop_bones.value)
                single_values.extend(self.exported_face_bones.value)
                self.setting['single'] = single_values

        if not self.switch_combined.value:
            single_values = []
            if self.export_body.value:
                single_values.extend(self.exported_chr_bones.value)
            if self.export_weapon.value:
                single_values.extend(self.exported_weapon_bones.value)
            if self.export_prop.value:
                single_values.extend(self.exported_prop_bones.value)
            if self.export_face.value:
                single_values.extend(self.exported_face_bones.value)
            else:
                single_values.append(None)
            
            self.setting['single'] = single_values
        
    def end(self):
        kwargs = self.setting
        self.export_anim(**kwargs)
        cmds.refresh(suspend=False)
        if not self.switch_exp_current_scene.value:
            cmds.file(force=True, new=True)
        else:
            # open file again
            cur_file_name = cmds.file(query=True, sceneName=True)
            cmds.file(cur_file_name, open=True, force=True, prompt=False)

    def export_anim(self, *args, **kwargs):
        '''
        except_bones_list 这是个列表套列表, 如果合并输出就[['root','weapon_root']], 如果单独输出就['root', 'weapon_root']
        以一个元素为一个单位选择输出,后面如果需要添加mesh一起输出根据开关和root来get输出的mesh
        Args:
            except_bones (_type_): _description_
        '''
        frame_data = self.frame_split_frame.value
        # set export parameter
        utils.anim_exp_sdk_parameter()
        mel.eval('FBXExportSkins -v true')

        cur_file_name = cmds.file(query=True, sceneName=True)
        if not cur_file_name:
            raise RuntimeError('Please save the file first.')
        base_name = os.path.basename(cur_file_name)
        dir_name = os.path.dirname(cur_file_name)
        if frame_data == [None, None]:
            min_time = cmds.playbackOptions(minTime=True, query=True)
            max_time = cmds.playbackOptions(maxTime=True, query=True)
        else:
            min_time = frame_data[0]
            max_time = frame_data[1]
        mel.eval('FBXExportBakeComplexStart -v {:d}'.format(int(min_time)))
        mel.eval('FBXExportBakeComplexEnd -v {:d}'.format(int(max_time)))

        {cmds.setAttr('%s.drawStyle' % k, 0) for k in cmds.ls(type='joint')}
        for mode, bones in kwargs.items():
            '''
            1. 当mode为combined时, bones是一个列表套列表, 里面的每个元素是一个列表, 里面是root节点
            2. 当mode为single时, bones是一个列表, 里面是root节点
            3. kwargs = {'combined': [['body_root','weapon_root']], 
                          'single' : ['body_root','weapon_root']}
            '''
            for bone in bones:
                if mode == 'combined':
                    root = bone[0]
                elif mode == 'single':
                    root = bone

                if not root:
                    continue
                root = Node(root)
            
                # re_file_path = pathlib.Path(cmds.referenceQuery(root, f=1))
                # prefix_name = re_file_path.stem
                prefix_name = os.path.basename(cmds.referenceQuery(root, f=1)).split('.')[0]

                if mode == 'combined':
                    exp_joints = []
                    for rt in bone:
                        exp_joints.extend(cmds.ls(rt, dag=True, type='joint'))
                    exp_joints = [Node(_jnt) for _jnt in exp_joints]
                elif mode == 'single':
                    exp_joints = cmds.ls(root, dag=True, type='joint')
                    exp_joints = [Node(_jnt) for _jnt in exp_joints]

                self.exp_objects = exp_joints
                if self.switch_output_mesh.value:
                    if mode == 'combined':
                        for rt in bone:
                            mesh_nodes = self.get_mesh_objects(rt)
                            self.exp_objects.append(mesh_nodes)
                    elif mode == 'single':
                        mesh_nodes = self.get_mesh_objects(root)
                        self.exp_objects.append(mesh_nodes)

                # select joints
                cmds.select(self.exp_objects, replace=True)
                if root in self.exported_chr_bones.value:
                    cmds.select(cmds.ls(type='HIKCharacterNode'), add=True)
                if not cmds.ls(sl=1):
                    cmds.error('No object selected')
                    continue
                utils.bake_anim_keys(node=exp_joints,
                                     min_time=min_time,
                                     max_time=max_time)
                
                ext = re.search(r'.*(\.\D+)', base_name).group(1)
                file_name = base_name.split('.')[0]
                exp_folder = '{0}/FBX_Anim/{1}'.format(dir_name, prefix_name)
                save_path = '{0}/{1}'.format(exp_folder, base_name.replace(ext, '.fbx'))

                #添加Head输出重新命名
                if 'Head' in prefix_name:
                    level_prefix = ''.join(prefix_name.split('_')[-3:])
                    facial_level_name = '{0}_{1}'.format(file_name, level_prefix)
                    exp_folder = '{0}/FBX_Anim/{1}'.format(dir_name, prefix_name)
                    save_path = '{0}/{1}'.format(exp_folder, '{0}{1}'.format(facial_level_name, '.fbx'))

                if not os.path.exists(exp_folder):
                    os.makedirs(exp_folder)

                if mode == 'combined':
                    for rt in bone:
                        self.origin_root(Node(rt))
                elif mode == 'single':
                    self.origin_root(root)

                if not frame_data == [None, None]:
                    temp_full_path = os.path.splitext(save_path)[0] + '_{}_{}'.format(frame_data[0], frame_data[1])
                    save_path = temp_full_path + '.fbx'

                mel.eval('FBXExport -f \"{0}\" -s'.format(save_path))

                if mode == 'combined':
                    if 'Head' in prefix_name:
                        self.facial_post_process_FBX(save_path, root, exp_joints)
                    else:
                        self.combined_post_process_FBX(save_path, root)

                elif mode == 'single':
                    if 'Head' in prefix_name:
                        self.facial_post_process_FBX(save_path, root, exp_joints)
                    else:
                        self.single_post_process_FBX(save_path, root)


    def origin_root(self, root):
        '''
        设置root值到0,0,0, 以便于导出

        Args:
            root (_type_): _description_
        '''
        if self.switch_weapon_origin.value:
            if root.name in self.exported_weapon_bones.value:
                utils.delete_key_freeze(root)
                cmds.setAttr(f'{root}.r', 90, 0, 180)

        elif self.switch_prop_origin.value:
            if root.name in self.exported_prop_bones.value:
                utils.delete_key_freeze(root)

        # TODO: 如果有表情的root, 需要在这里添加
        # if self.switch_face_origin.value:
        #     if root.name in self.exported_face_bones.value:
        #         utils.delete_key_freeze(root)

    def get_all_ancestor(self, node):
        parents = []
        while True:
            parent = cmds.listRelatives(node, parent=True)
            if not parent:
                break
            node = parent[0]
            parents.append(node)
        return parents

    def get_mesh_by_reference_objects(self, obj):
        '''
        get reference node objects by type
        Args:
            node_type (str): node type
        Returns:
            list(Node): 返回所有的参考节点
        '''
        mesh_ts = list()
        for mesh in cmds.ls(type='mesh'):
            mesh_ts.extend(cmds.listRelatives(mesh, parent=True))
        all_mesh_ts = list(set(mesh_ts))
        nodes = cmds.referenceQuery(obj, n=1)

        return [cmds.ls(k, long=1)[0] for k in nodes if k in all_mesh_ts]

    def get_common_ancestor(self, obj_list):
        '''
        获取选择物体节点在outliner中最近的公共祖先层

        Returns:
            _type_: _description_
        '''
        # 获取所有选中节点的父级节点列表
        parents_list = [self.get_all_ancestor(node) for node in obj_list]

        # 按层级排序
        parents_list.sort(key=len)

        # 从根节点向下遍历，查找第一个包含所有选中节点的节点
        common_ancestor = cmds.ls(assemblies=True)[0]
        for node in parents_list[0]:
            if all(node in parents for parents in parents_list):
                common_ancestor = node
                break

        return common_ancestor

    def get_most_ancestors_element(self, objs):
        '''
        返回有最多祖先的组件

        Args:
            objs (_type_): objs = cmds.ls(sl=True, long=True)

        Returns:
            _type_: _description_
        '''
        objs_split = []
        for obj in objs:
            aa = obj.split('|')[1:]
            objs_split.append(obj.split('|')[1:])
        count_dict = Counter([x[0] for x in objs_split])
        most_common = count_dict.most_common(1)[0][0]
        indices = [i for i, x in enumerate(objs_split) if x[0] == most_common]
        return [objs[k] for k in indices]

    def get_mesh_objects(self, obj):
        '''
        1. 通过任意物体获取当前参考物体所有mesh列表
        2. mesh不一定在同一个组下, 所以需要获取有最多公共祖先的mesh列表
        3. 通过最多公共祖先的mesh列表,来查找最近的公共祖先组
        4. 得到的结果就是需要输出的meshGroup
        Args:
            obj str(): 参考资产的任意物体
        '''
        meshs = self.get_mesh_by_reference_objects(obj)
        most_ancestors_meshs = self.get_most_ancestors_element(meshs)
        ex_meshs_group = self.get_common_ancestor(most_ancestors_meshs)
        return ex_meshs_group

    def get_chr_roots(self):
        '''
        get weapon root 
        Returns:
            list(Node): 获取所有的角色的root 骨骼root命名为*_root
        '''
        rig_roots = cmds.ls('RIG.{}'.format('rig_type'),
                            objectsOnly=True, recursive=True) or []
        chr_bone_nodes = []
        for rig_node in rig_roots:
            rg_node = rg.RigGlobal(rig_node)  # 这里rig_node必须要lsr Rig
            if rg_node.rig_type == 'body':
                chr_bone_nodes.append(rg_node.export_bone_grp)
        chr_bone_nodes = list(set(chr_bone_nodes))
        return chr_bone_nodes

    def get_face_roots(self):
        '''
        get weapon root 
        Returns:
            list(Node): 获取所有的face root
        '''
        rig_roots = cmds.ls('RIG.{}'.format('rig_type'),
                            objectsOnly=True, recursive=True) or []
        face_bone_nodes = []
        for rig_node in rig_roots:
            rg_node = rg.RigGlobal(rig_node)  # 这里rig_node必须要lsr Rig
            if rg_node.rig_type == 'face':
                face_bone_nodes.append(rg_node.export_bone_grp)
        return face_bone_nodes

    def get_weapon_roots(self):
        '''
        get weapon root 
        Returns:
            list(Node): 武器的root骨骼名为root 返回root下所有的骨骼
        '''
        weapon_roots = []
        for i in cmds.ls('*:Root', objectsOnly=True, recursive=True, exactType='joint'):
            if 'Weapon_Root' in cmds.listAttr(i):
                weapon_roots.append(Node(i))
        return weapon_roots

    def get_prop_roots(self):
        '''
        get weapon root 
        Returns:
            list(Node): 获取所有的prop的root 道具root命名为*_root
        '''
        rig_roots = cmds.ls('RIG.{}'.format('rig_type'),
                            objectsOnly=True, recursive=True) or []
        prop_bone_nodes = []
        for rig_node in rig_roots:
            rg_node = rg.RigGlobal(rig_node)  # 这里rig_node必须要lsr Rig
            if rg_node.rig_type == 'prop':
                prop_bone_nodes.append(rg_node.export_bone_grp)
        prop_bone_nodes = list(set(prop_bone_nodes))
        return prop_bone_nodes
    
    def single_post_process_FBX(self, save_path, root):
        '''
        单独输出的fbx后处理

        Args:
            save_path (_type_): _description_
            root (_type_): _description_
        '''
        remove_joints = self.get_remove_joints()
        fbx_file = FBX_Class(save_path)
        remove_PNode = []
        for i in remove_joints:
            remove_PNode.extend(cmds.ls(i, dag=True, et='joint'))
        remove_PNode = [fbx_file.get_node_by_name(bone) for bone in remove_PNode]
        # 添加判断 这里会获取到 BIND_SKELETON 这个组, 但是武器的root是个joint, 武器的root在outline最外面, 所以如果是joint类型这里不需要 unparent
        if not root.api_type_str == 'joint':
            nodes = [node.name for node in root.get_parent_hierarchy()]
            fbx_file.unparent_nodes(parent_nodes=nodes)
        if not self.switch_output_dyjoint.value:
            self.remove_animation_from_bones(remove_PNode)
            fbx_file.remove_nodes_by_names(remove_joints)
            
        fbx_file.remove_namespace()
        fbx_file.save_scene_file()

    def combined_post_process_FBX(self, save_path, root):
        '''
        角色和枪械合并输出的fbx后处理
        1. 删除namespace
        2. Processing FBX  把root parent到Weapon_ToWorld_Target_Chest骨骼下, 
        3. 删除Align_Weapon_Chest_ProgramUse, 
        4. 修改root为Align_Weapon_Chest_ProgramUse
        Args:
            save_path (_type_): _description_
            root (_type_): _description_
        '''
        remove_joints = self.get_remove_joints()
        fbx_file = FBX_Class(save_path)

        remove_PNode = []
        for i in remove_joints:
            remove_PNode.extend(cmds.ls(i, dag=True, et='joint'))
        remove_PNode = [fbx_file.get_node_by_name(bone) for bone in remove_PNode]

        nodes = [node.name for node in root.get_parent_hierarchy()]
        fbx_file.unparent_nodes(parent_nodes=nodes)
        if not self.switch_output_dyjoint.value:
            self.remove_animation_from_bones(remove_PNode)
            fbx_file.remove_nodes_by_names(remove_joints)
            
        fbx_file.remove_namespace()

        weapon_root = fbx_file.get_node_by_name('root')
        if weapon_root:
            Weapon_ProgramUse = fbx_file.get_node_by_name(
                'Weapon_ToWorld_Target_Chest')
            fbx_file.remove_nodes_by_names(['Align_Weapon_Chest_ProgramUse'])
            Weapon_ProgramUse.AddChild(weapon_root)
            weapon_root.SetName('Align_Weapon_Chest_ProgramUse')
        fbx_file.save_scene_file()

    def facial_post_process_FBX(self, save_path, root, exp_joints):
        '''处理表情动画的FBX 删除除了Root以外的所有骨骼

        :param save_path: _description_
        :type save_path: _type_
        :param root: _description_
        :type root: _type_
        '''        
        fbx_file = FBX_Class(save_path)
        nodes = [node.name for node in root.get_parent_hierarchy()]
        delete_nodes = [node.name for node in exp_joints[1:]]
        fbx_file.unparent_nodes(parent_nodes=nodes)
        fbx_file.remove_nodes_by_names(delete_nodes)
        fbx_file.remove_namespace()
        fbx_file.save_scene_file()
        self.remove_fbx_animation(save_path)

    def get_remove_joints(self):
        '''
        获取需要删除动画的骨骼
        '''
        base_remove_list = ['eye_r', 'eye_l', 'jaw', 'holster_SpineR',
                            'holster_SpineL', 'Backpack', 'holster_PelvisR', 'holster_PelvisL']
        remove_list = []
        for base_remove in base_remove_list:
            remove_list.extend(cmds.ls('*:%s' % base_remove))
        remove_list.extend(cmds.ls('*:*dyJnt*',dag=True, et='joint'))
        return remove_list

    def remove_fbx_animation(self, save_path):
        '''删除fbx中的t,r,s,v属性的动画
        :param pNode: fbx path
        :type pNode: _type_
        '''      
        fbx_file = FBX_Class(save_path)
        pNode = fbx_file.get_node_by_name('Root')
        anim_stack = pNode.GetScene().GetCurrentAnimationStack()
        anim_layer = anim_stack.GetMember(FbxCommon.FbxAnimLayer.ClassId)
        t_curve_node = pNode.LclTranslation.GetCurveNode(anim_layer)
        r_curve_node = pNode.LclRotation.GetCurveNode(anim_layer)
        s_curve_node = pNode.LclScaling.GetCurveNode(anim_layer)
        v_curve_node = pNode.Visibility.GetCurveNode(anim_layer)
        for curve_node in [t_curve_node, r_curve_node, s_curve_node, v_curve_node]:
            if curve_node:
                for i in range(3):
                    curve = curve_node.GetCurve(i)
                    if curve:
                        curve.KeyClear()
        fbx_file.save_scene_file()

    def remove_animation_from_bones(self, bones):
        '''删除fbx中的t,r,s,v属性的动画
        :param pNode: fbx path
        :type pNode: _type_
        '''      
        for bone in bones:
            anim_stack = bone.GetScene().GetCurrentAnimationStack()
            anim_layer = anim_stack.GetMember(FbxCommon.FbxAnimLayer.ClassId)
            t_curve_node = bone.LclTranslation.GetCurveNode(anim_layer)
            r_curve_node = bone.LclRotation.GetCurveNode(anim_layer)
            s_curve_node = bone.LclScaling.GetCurveNode(anim_layer)
            v_curve_node = bone.Visibility.GetCurveNode(anim_layer)
            for curve_node in [t_curve_node, r_curve_node, s_curve_node, v_curve_node]:
                if curve_node:
                    for i in range(3):
                        curve = curve_node.GetCurve(i)
                        if curve:
                            curve.KeyClear()