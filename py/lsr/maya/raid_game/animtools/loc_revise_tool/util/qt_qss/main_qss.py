#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date      : 2022-01-13 10:55
# Author    : <PERSON><PERSON><PERSON>
# Usage     : 
# Version   :
# Comment   :


# Import built-in modules

# Import third-party modules

# Import local modules


class SetQss(object):
    def __init__(self, parent):
        self._parent = parent
        # 设置UI控件样式
        self._qss = "" \
            "QMainWindow {background-color: #272727;}" \
            "QWidget {background-color: #323232;}" \
            "QListView {color: #FFA500;}" \
            "QCheckBox {color: #a6a6a6;}" \
            "QComboBox {color: #ffffff; border: 1px solid #444444; border-radius: 1px; " \
            "padding: 1px 18px 1px 3px; min-width: 6em;}" \
            "QComboBox QAbstractItemView {color: #ffffff; border: 1px solid #444444; " \
            "selection-background-color: #00b4ff;}" \
            "QToolButton {border: 1px; border-radius: 3px; height: 20px; width: 20px;}" \
            "QToolButton:hover {background-color: #13c2c2}" \
            "QLineEdit {" \
                "font-size: 12px;" \
                "font-weight: 400; " \
                "background-color: #525252; " \
                "color: #ffffff;" \
                "border-radius: 3px;" \
                "padding: 4, 4, 0, 0" \
            "}" \
            "QLabel {" \
                "font-size: 18px; " \
                "font-weight: 400; " \
                "color: #D3D3D3;" \
                "border-radius: 0px;" \
                "padding: 4, 4, 0, 0" \
            "}" \
            "QPushButton {" \
                "font-size: 16px;" \
                "font-weight: 500;" \
                "border-radius: 3%;" \
                "background-color: #ff8c00; " \
                "color: #d9d9d9;" \
                "padding: 1% 10%;" \
                "min-width:  40px;" \
                "min-height:  30px;" \
            "}" \
            "QPushButton::hover {background-color: #ff9e55;}" \
            "QGroupBox {" \
                "min-width:  40px;" \
                "min-height:  22px;" \
                "color:  #d9d9d9;" \
            "}"

    def set_stylesheet(self):
        """
        设置控件样式
            Returns:
        """
        self._parent.setStyleSheet(self._qss)


