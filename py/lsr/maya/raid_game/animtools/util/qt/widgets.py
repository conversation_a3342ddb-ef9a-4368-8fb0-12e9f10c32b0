#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Date      : 2020-10-30 17:40
# Author    : <PERSON><PERSON><PERSON>
# Usage     : 
# Version   :
# Comment   :


# Import built-in modules
import os

# Import third-party modules
from Qt import QtWidgets, QtCore

# Import local modules
from .general import make_layout
from .constant import QT_CONST


class LabeledWidgetBase(QtWidgets.QWidget):
    """
    It is quite common to see widgets prefixed with a label in the UI.

    This class brings together a label and child widget into a single container widget.

    The derived classes should implement the _create_widget() method to define
    the child widget type.
    """
    def __init__(self, label, label_width=None, widget_width=None, margins=None, parent=None):
        """
        :param str label:
        :param int label_width:
        :param int widget_width:
        :param list[int] margins: [left, top, right, bottom] margins
        :param QtWidget.QWidget parent: parent widget
        """
        super(LabeledWidgetBase, self).__init__(parent=parent)

        self._layout = make_layout(QT_CONST.horizontal, margins=margins, alignment=QT_CONST.left)
        self.setLayout(self._layout)
        self._parent = parent

        label = QtWidgets.QLabel(label)
        if label_width is not None:
            label.setFixedWidth(label_width)

        self._widget = self._create_widget()

        if widget_width:
            self._widget.setFixedWidth(widget_width)

        self._layout.addWidget(label)
        self._layout.addWidget(self._widget)

    def set_layout_margins(self, margins):
        self._layout.setContentsMargins(QtCore.QMargins(*margins))

    def set_alignment(self, alignment):
        QtWidgets.QLayoutItem.setAlignment(self._layout, alignment)

    def set_enabled(self, state):
        self._widget.setEnabled(state)

    def _create_widget(self):
        return NotImplementedError

    def __getattr__(self, *args):
        try:
            return self.__getattribute__(*args)
        except AttributeError:
            return self._widget.__getattribute__(*args)


class LabeledLineEdit(LabeledWidgetBase):
    """
    Implements a QLineEdit widget prefixed by a label
    """
    def __init__(self, label, label_width=None, margins=None, parent=None):
        super(LabeledLineEdit, self).__init__(
            label=label,
            label_width=label_width,
            margins=margins,
            parent=parent
        )

    def _create_widget(self):
        return QtWidgets.QLineEdit()


class LabeledCheckBox(LabeledWidgetBase):
    """
    Implements a QCheckBox widget prefixed by a label
    """
    def __init__(self, label, label_width=None, margins=None, parent=None):
        super(LabeledCheckBox, self).__init__(
            label=label,
            label_width=label_width,
            margins=margins,
            parent=parent
        )

    def _create_widget(self):
        return QtWidgets.QCheckBox()


class LabeledComboBox(LabeledWidgetBase):
    """
    Implements a QComboBox widget prefixed by a label
    """
    def __init__(self, label, label_width=None, combo_box_width=None, margins=None, parent=None):
        super(LabeledComboBox, self).__init__(
            label=label,
            label_width=label_width,
            widget_width=combo_box_width,
            margins=margins,
            parent=parent
        )

    def _create_widget(self):
        return QtWidgets.QComboBox(self._parent)


class FileWidget(QtWidgets.QWidget):
    path_changed = QtCore.Signal(str)

    def __init__(self, label, width=60, title=None, filter=None,
                 parent=None, open_mode='Open', extension=None):
        """

        :param label:
        :param width:
        :param title:
        :param filter:
        :param parent:
        :param open_mode: 'Open', 'Save' and 'Directory'
        :param extension:
        """
        super(FileWidget, self).__init__(parent=parent)
        self._main_layout = make_layout(QT_CONST.horizontal)
        self.setLayout(self._main_layout)

        self._title = title or open_mode
        self._file_filter = filter or ""
        self._open_mode = open_mode
        self._extension = extension

        self._file_path = QtWidgets.QLineEdit()
        self._file_button = QtWidgets.QPushButton()
        self._file_button.setFixedHeight(20)
        self._file_button.setIcon(
            QtWidgets.QFileIconProvider().icon(QtWidgets.QFileIconProvider.Folder)
        )

        label = QtWidgets.QLabel(label)
        label.setFixedWidth(width)
        self._main_layout.addWidget(label)
        self._main_layout.addWidget(self._file_path)
        self._main_layout.addWidget(self._file_button)

        self._file_button.clicked.connect(self._file_button_clicked)

        self._file_path.editingFinished.connect(self._editing_finished)

    def set_extension(self, extension):
        self._extension = extension

    def get_file_path(self):
        return self._file_path.text()

    def set_file_path(self, path):
        self._file_path.setText(path)

    def set_filter(self, filter):
        self._file_filter = filter

    def _sanitize_path(self, path):
        _, ext = os.path.splitext(path)
        if self._extension and path:
            if not ext:
                path += self._extension
            elif ext != self._extension:
                path = self._extension.join(path.rsplit(ext, 1))
        return path

    def _editing_finished(self):
        path = self._sanitize_path(self._file_path.text())
        if path != self._file_path.text():
            self._file_path.blockSignals(True)
            self._file_path.setText(path)
            self._file_path.blockSignals(False)
        self.path_changed.emit(path)

    def _file_button_clicked(self):
        if self._open_mode == 'Open':
            fn = QtWidgets.QFileDialog.getOpenFileName(
                self, self._title, "", self._file_filter
            )
        if self._open_mode == 'Save':
            fn = QtWidgets.QFileDialog.getSaveFileName(
                self, self._title, "", self._file_filter
            )
        if self._open_mode == 'Directory':
            fn = QtWidgets.QFileDialog.getExistingDirectory(
                self, self._title, self.get_file_path()
            )

        if fn and fn[0]:
            if self._open_mode == 'Directory':
                fp = self._sanitize_path(fn)
            else:
                fp = self._sanitize_path(fn[0])
            self._file_path.setText(fp)
            self.path_changed.emit(fp)


class PublishCommentDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super(PublishCommentDialog, self).__init__(parent=parent)

        self.setWindowTitle('Publish Comment')
        self.setMinimumWidth(300)

        layout = make_layout(QT_CONST.vertical, margins=[5, 5, 5, 5])
        self.setLayout(layout)

        label = QtWidgets.QLabel('Please add publish comment below:')
        self._comment_line_edit = QtWidgets.QLineEdit()
        layout.addWidget(label)
        layout.addWidget(self._comment_line_edit)

        button_layout = make_layout(QT_CONST.horizontal)
        self._ok = QtWidgets.QPushButton('OK')
        self._ok.setEnabled(False)
        cancel = QtWidgets.QPushButton('Cancel')
        button_layout.addWidget(self._ok)
        button_layout.addWidget(cancel)
        layout.addLayout(button_layout)

        self._comment_line_edit.textEdited.connect(self._on_comment_changed)
        self._ok.clicked.connect(self.accept)
        cancel.clicked.connect(self.reject)

        self._comment = ''

    def _on_comment_changed(self):
        self._comment = str(self._comment_line_edit.text()).strip()
        self._ok.setEnabled(len(self._comment) != 0 or False)

    @property
    def comment(self):
        return self._comment


class MessageBox(QtWidgets.QMessageBox):
    """
    This is a simplified version of the message box.
    You can use only fill in message_type, title and content parameters.
    If you need to return value, please use "exec_()".
    return value is button_names list index.
    ep: button_names = ['OK', 'Cancel']
        clicked 'OK'        return 0.
        clicked 'Cancel'    return 1.
    """
    def __init__(self, message_type, title, content,
                 detailed_text=None, button_names=None, parent=None):
        """

        :param str message_type: 'error', 'warning', 'question'
        :param str title:
        :param str content:
        :param str detailed_text:
        :param list[str] button_names:
        :param QtWidget.QWidget parent: parent widget
        """
        super(MessageBox, self).__init__(parent=parent)
        self._message_type = message_type
        self._title = title
        self._content = self._switch_content(content)
        self._button_names = button_names
        self._detailed_text = detailed_text
        self._button_handles = dict()

        self._init_ui()

    @property
    def button_handles(self):
        """

        :return: dict(button_name=button_handle)
        """
        return self._button_handles

    @staticmethod
    def _switch_content(content):
        return '<font size = 5> %s </font>' % content.replace('\n', '<br/>')

    def _init_ui(self):
        if self._message_type == 'error':
            self.setIcon(self.Critical)

        elif self._message_type == 'warning':
            self.setIcon(self.Warning)

        elif self._message_type == 'question':
            self.setIcon(self.Question)

        else:
            self.setIcon(self.Information)

        self.setWindowTitle(self._title)
        self.setText(self._content)

        if self._button_names:
            for button_name in self._button_names:
                self._button_handles[button_name] = self.addButton(button_name, self.AcceptRole)
        else:
            self.setStandardButtons(self.Ok | self.Cancel)

        if self._detailed_text:
            self.setDetailedText(self._detailed_text)

        self.setWindowFlags(self.windowFlags() | QtCore.Qt.WindowStaysOnTopHint)



