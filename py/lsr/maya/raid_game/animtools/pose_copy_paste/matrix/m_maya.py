#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Import built-in modules
import math

# Import third-party modules
from maya import OpenMaya as om
from maya import OpenMayaAnim as oma
from maya import cmds, mel

# Import local modules


class Matrix(object):
    """
    Maya矩阵操作
    """
    def __init__(self):
        self._index_order_map = {
            0: om.MTransformationMatrix.kXYZ,
            1: om.MTransformationMatrix.kYZX,
            2: om.MTransformationMatrix.kZXY,
            3: om.MTransformationMatrix.kXZY,
            4: om.MTransformationMatrix.kYXZ,
            5: om.MTransformationMatrix.kZYX
        }
        self._attr_list = ['tx', 'ty', 'tz', 'rx', 'ry', 'rz', 'sx', 'sy', 'sz']

    def get_matrix_by_list(self, matrix_list):
        """
        通过列表获取MMatrix
        :param
            matrix_list: list, 矩阵列表
        :return:
            om.MMatrix, 矩阵
        """
        utl = om.MScriptUtil()
        matrix = om.MMatrix()
        utl.createMatrixFromList(matrix_list, matrix)
        return matrix

    def get_matrix_list_by_node(self, node):
        """
        获取节点的MMatrix列表
        :param
            node: str, 节点名
        :return:
            list, [MMatrix(0, 0), MMatrix(0, 1), MMatrix(0, 2), MMatrix(0, 3),
            MMatrix(1, 0), MMatrix(1, 1), MMatrix(1, 2), MMatrix(1, 3),
            MMatrix(2, 0), MMatrix(2, 1), MMatrix(2, 2), MMatrix(2, 3),
            MMatrix(3, 0), MMatrix(3, 1), MMatrix(3, 2), MMatrix(3, 3)]
        """
        mMatrix = self.get_mMatrix(node)
        matrix_list = [
            mMatrix(i, j)
            for i in range(4)
            for j in range(4)
        ]
        return matrix_list

    def get_transform_by_matirx(self, matrix, rotate_order=0):
        """
        获取矩阵的位移、旋转、缩放
        :param
            matrix: om.MMatrix, 矩阵
            rotate_order: int, 旋转顺序
        :return:
            list, [tx, ty, tz, rx, ry, rz, sx, sy, sz]
        """
        order = self._index_order_map.get(rotate_order, om.MTransformationMatrix.kXYZ)
        t_matrix = om.MTransformationMatrix(matrix)
        t_matrix.reorderRotation(order)
        v_translate = t_matrix.getTranslation(4)
        tx = v_translate.x
        ty = v_translate.y
        tz = v_translate.z
        e_rotation = t_matrix.eulerRotation()
        rx = math.degrees(e_rotation.x)
        ry = math.degrees(e_rotation.y)
        rz = math.degrees(e_rotation.z)
        util = om.MScriptUtil()
        util.createFromDouble(0.0, 0.0, 0.0)
        ptr = util.asDoublePtr()
        t_matrix.getScale(ptr, om.MSpace.kWorld)
        sx = util.getDoubleArrayItem(ptr, 0)
        sy = util.getDoubleArrayItem(ptr, 1)
        sz = util.getDoubleArrayItem(ptr, 2)
        trans = [tx, ty, tz, rx, ry, rz, sx, sy, sz]
        return trans

    def get_mMatrix(self, node):
        """
        获取节点的MMatrix
        :param
            node: str, 节点名
        :return:
            om.MMatrix
        """
        mdag_path = self.get_mDagpath(node)
        world_matrix = mdag_path.inclusiveMatrix()
        return world_matrix

    def get_mDagpath(self, node):
        """
        获取节点的MDagPath
        :param
            node: str, 节点名
        :return:
            om.MDagPath
        """
        selection_list = om.MSelectionList()
        selection_list.add(node)
        path_node = om.MDagPath()
        selection_list.getDagPath(0, path_node)
        return path_node

    def get_parent_mMatrixInverse(self, node):
        """
        获取父节点的世界逆矩阵
        :param
            node: str, 节点名
        :return:
            om.MMatrix
        """
        mdag_path = self.get_mDagpath(node)
        world_matrix = mdag_path.exclusiveMatrixInverse()
        return world_matrix

    def set_node_transform(self, trans, node, is_key=False):
        """
        设置节点的transform属性
        :param
            trans: value list, [tx, ty, tz, rx, ry, rz, sx, sy, sz]
            node: str, 节点名
            is_key: bool, 是否设置关键帧
        :return:
            None
        """
        for attr, value in zip(self._attr_list, trans):
            node_attr = '{}.{}'.format(node, attr)
            if value is None:
                continue
            try:
                cmds.setAttr(node_attr, value)
                if is_key:
                    cmds.setKeyframe(node_attr)
            except:
                pass



