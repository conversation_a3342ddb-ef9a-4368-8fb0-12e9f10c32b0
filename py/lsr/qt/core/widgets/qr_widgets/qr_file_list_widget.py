from functools import partial

from lsr.qt.core.widgets.qr_widgets.qr_extension_widget import QRListWidget
from lsr.qt.core.widgets.qr_widgets.qr_list_context_file import QRListContextFile
from Qt import Qt<PERSON><PERSON>, QtWidgets, QtGui
from pathlib import Path

class QRFileListWidget(QtWidgets.QWidget):
    """
    QRFileListWidget is a custom widget for displaying and managing a list of files.
    It provides functionality for adding, deleting, and selecting files through a GUI interface.
    The widget supports drag and drop operations for files and directories.
    """

    def __init__(self, key_name):
        """
        Initialize the QRFileListWidget.

        Parameters:
            key_name (str): A unique identifier for the widget.
        """
        super(QRFileListWidget, self).__init__()
        self.key_name = key_name
        self.setAcceptDrops(True)

        ui_vl_main = QtWidgets.QVBoxLayout()
        ui_vl_main.setContentsMargins(0, 0, 0, 0)
        self.setLayout(ui_vl_main)

        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(True)

        # file list area
        ui_sa_file = QtWidgets.QScrollArea()
        ui_sa_file.setWidgetResizable(True)

        ui_sa_file.setContentsMargins(0, 0, 0, 0)
        ui_sa_file.setFrameShape(QtWidgets.QFrame.NoFrame)

        ui_widget = QtWidgets.QWidget()
        # self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 900, 600))
        ui_vl_file = QtWidgets.QVBoxLayout(ui_widget)
        ui_vl_file.setContentsMargins(0, 0, 0, 0)
        self.ui_lw_file = QRListWidget(key_name)
        self.ui_lw_file.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.ui_lw_file.setFont(font)
        self.ui_lw_file.setGeometry(QtCore.QRect(10, 10, 371, 400))
        self.ui_lw_file.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.ui_lw_file.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        ui_vl_file.addWidget(self.ui_lw_file)
        ui_sa_file.setWidget(ui_widget)

        # process file list button
        ui_hl_button = QtWidgets.QHBoxLayout()
        self.ui_bt_add = QtWidgets.QPushButton()
        self.ui_bt_add.setText('Add Files')
        ui_hl_button.addWidget(self.ui_bt_add)

        self.ui_bt_delete = QtWidgets.QPushButton()
        self.ui_bt_delete.setText('Delete Files')
        ui_hl_button.addWidget(self.ui_bt_delete)

        self.ui_bt_clear = QtWidgets.QPushButton()
        self.ui_bt_clear.setText('Clear List')
        ui_hl_button.addWidget(self.ui_bt_clear)

        ui_vl_main.addWidget(ui_sa_file)
        ui_vl_main.addLayout(ui_hl_button)

        self.pop_context_menu = QRListContextFile()

    def set_pop_context_menu(self, pop_context_menu: QRListContextFile):
        """
        Set the popup context menu for the list widget and connect button signals.

        Parameters:
            pop_context_menu (QRListContextFile): The context menu to be used.

        Returns:
            None
        """
        self.pop_context_menu = pop_context_menu
        self.pop_context_menu.set_list_widget(self.ui_lw_file)
        self.ui_bt_add.clicked.connect(partial(self.pop_context_menu.add_files_dialog))
        self.ui_bt_delete.clicked.connect(partial(self.pop_context_menu.delete_selected_file))
        self.ui_bt_clear.clicked.connect(partial(self.pop_context_menu.clear_all_file))

    def dragEnterEvent(self, event):
        """
        Handle drag enter events for drag and drop file operations.

        Parameters:
            event (QDragEnterEvent): The drag enter event.

        Returns:
            None
        """
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        """
        Handle drop events for drag and drop file operations.
        Filters files based on allowed extensions and adds them to the list widget.

        Parameters:
            event (QDropEvent): The drop event.

        Returns:
            None
        """
        drop_files = []
        for url in event.mimeData().urls():
            url = Path(url.toLocalFile())

            if url.is_file() and url.suffix.lower() in [suffix[1:] for suffix in QRListContextFile.FILE_FILTER]:
                drop_files.append(str(url))
            elif url.is_dir():
                for file in url.iterdir():
                    if file.is_file() and file.suffix.lower() in [suffix[1:] for suffix in
                                                                  QRListContextFile.FILE_FILTER]:
                        drop_files.append(str(file))

        if drop_files:
            all_urls = set(drop_files) - set(
                [self.ui_lw_file.item(i).text() for i in range(self.ui_lw_file.count())])
            if all_urls:
                for file in all_urls:
                    print(file)
                    self.ui_lw_file.add_item(file)

    def get_files(self):
        """
        Get all file items in the list widget.

        Returns:
            list: A list of file paths as strings.
        """
        return [self.ui_lw_file.item(i).text() for i in range(self.ui_lw_file.count())]

    def get_selected_files(self):
        """
        Get the selected file items in the list widget.

        Returns:
            list: A list of selected file paths as strings.
        """
        return [self.ui_lw_file.item(i).text() for i in range(self.ui_lw_file.count()) if
                self.ui_lw_file.item(i).isSelected()]


