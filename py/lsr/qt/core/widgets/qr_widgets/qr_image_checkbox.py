from functools import partial
from lsr.qt.core.widgets.qr_widgets.qr_widget_utils import get_icon_full_path
from lsr.qt.core.widgets.qr_widgets.qr_extension_widget import QRCheckBox
from lsr.python.core import compatible as compat
from Qt import QtCore, QtWidgets, QtGui


class QRImageCheckBox(QRCheckBox):
    """
    A custom checkbox that displays an image instead of the default check box.
    """

    _SELECTED_ICON = None
    _IMPORT_ICON = None
    _EXPORT_ICON = None
    _ENTER_ICON = None

    def __init__(self, *args, **kwargs):
        super(QRImageCheckBox,self).__init__(*args, **kwargs)

        self.qr_data = None

        self.w = self.size().width()
        self.h = self.size().height()

        self.background = None
        self.enter_pen = QtGui.QPen(QtCore.Qt.red, 3, QtCore.Qt.SolidLine)
        self.leave_pen = QtGui.QPen(QtCore.Qt.blue, 3, QtCore.Qt.SolidLine)

        self.current_pen = self.leave_pen

        self.font = QtGui.QFont()
        self.font.setPointSize(13)

        self.fill_color = QtGui.QColor(255, 255, 255, 100)

        self.is_checked_color = QtGui.QColor(0, 0, 255, 100)
        self.is_not_checked_color = QtGui.QColor(255, 255, 255, 0)

        self.title_rect = QtCore.QRect(0, 0, self.w - 1, 40)
        self.title_font_color = QtGui.QPen(QtCore.Qt.white, 3, QtCore.Qt.SolidLine)
        self.title_color = QtGui.QColor(0, 0, 0, 100)

        self.gradient = QtGui.QLinearGradient(0, 0, self.w - 1, 40)
        self.gradient.setColorAt(0, QtGui.QColor(0, 0, 0, 200))
        self.gradient.setColorAt(1, QtGui.QColor(0, 0, 0, 0))
        self.brush = QtGui.QBrush(self.gradient)

        self.is_mouse_enter = False

        self.ui_bt_import = QtWidgets.QPushButton()
        self.ui_bt_import.setFixedSize(30, 30)
        self.ui_bt_import.setIcon(self.import_icon)
        self.ui_bt_export = QtWidgets.QPushButton()
        self.ui_bt_export.setFixedSize(30, 30)
        self.ui_bt_export.setIcon(self.export_icon)
        self.ui_bt_enter = QtWidgets.QPushButton()
        self.ui_bt_enter.setFixedSize(30, 30)
        self.ui_bt_enter.setIcon(self.enter_icon)

        hl = QtWidgets.QHBoxLayout()
        hl.addStretch()
        hl.addWidget(self.ui_bt_import)
        hl.addWidget(self.ui_bt_export)
        hl.addWidget(self.ui_bt_enter)
        hl.addStretch(60)
        hl.setContentsMargins(140, 0, 0, 260)

        self.setLayout(hl)

    @compat.classproperty
    def import_icon(cls):
        """
        Get the import icon.
        args:
            None
        return:
            QtGui.QPixmap: The import icon.
        """
        if cls._IMPORT_ICON is None:
            cls._IMPORT_ICON = QtGui.QPixmap(get_icon_full_path('qr_button_import.png'))
        return cls._IMPORT_ICON

    @compat.classproperty
    def export_icon(cls):
        """
        Get the export icon.
        args:
            None
        return:
            QtGui.QPixmap: The export icon.
        """
        if cls._EXPORT_ICON is None:
            cls._EXPORT_ICON = QtGui.QPixmap(get_icon_full_path('qr_button_export.png'))
        return cls._EXPORT_ICON

    @compat.classproperty
    def enter_icon(cls):
        """
        Get the enter icon.
        args:
            None
        return:
            QtGui.QPixmap: The enter icon.
        """
        if cls._ENTER_ICON is None:
            cls._ENTER_ICON = QtGui.QPixmap(get_icon_full_path('qr_button_enter.png'))
        return cls._ENTER_ICON

    @compat.classproperty
    def selected_icon(cls):
        """
        Get the selected icon.
        args:
            None
        return:
            QtGui.QPixmap: The selected icon.
        """
        if cls._SELECTED_ICON is None:
            image = QtGui.QPixmap(get_icon_full_path('qr_checkbox_selected.png'))
            image = image.scaled(QtCore.QSize(30, 30), QtCore.Qt.KeepAspectRatio,
                                 QtCore.Qt.SmoothTransformation)

            mask = image.mask()
            image.fill(QtGui.QColor(255, 255, 255, 255))
            image.setMask(mask)

            cls._SELECTED_ICON = image

        return cls._SELECTED_ICON

    def set_qr_data(self, qr_data):
        """
        Set the qr data.
        args:
            qr_data: The qr data.
        return:
            None
        """
        self.qr_data = qr_data
        self.ui_bt_import.clicked.connect(partial(self.qr_data.import_data))
        self.ui_bt_export.clicked.connect(partial(self.qr_data.export_data))
        self.ui_bt_enter.clicked.connect(partial(self.qr_data.open_folder))

        self.background = self.qr_data.icon_background

    def get_qr_data(self):
        """
        Get the qr data.
        args:
            None
        return:
            The qr data.
        """
        return self.qr_data

    def paintEvent(self, event):
        """
        Paint event.
        """

        super().paintEvent(event)

        painter = QtGui.QPainter(self)

        self.w = self.size().width()
        self.h = self.size().height()

        if self.background is None:
            self.background = QtGui.QPixmap(self.size())
            self.background.fill(QtCore.Qt.transparent)

        painter.drawPixmap(0, 0, self.background)

        color = QtGui.QColor()
        color.setRed(255)
        painter.setPen(color)

        painter.setPen(self.current_pen)

        painter.setBrush(self.brush)
        painter.fillRect(self.title_rect, painter.brush())
        painter.setPen(self.title_font_color)
        painter.setFont(self.font)
        painter.drawText(self.title_rect, QtCore.Qt.AlignLeft, " " + self.text())

        if self.isChecked():
            if self.selected_icon is not None:
                painter.drawPixmap(self.w - 40, 4, self.selected_icon)

    def mousePressEvent(self, event):
        """
        Mouse press event.
        """
        super().mousePressEvent(event)
        self.update()

    def mouseReleaseEvent(self, event):
        """
        Mouse release event.
        """
        super().mouseReleaseEvent(event)
        self.setChecked(not self.isChecked())
        self.update()

    def enterEvent(self, event):
        """
        Enter event.
        """
        super().enterEvent(event)
        self.is_mouse_enter = True
        self.update()

    def leaveEvent(self, event):
        """
        Leave event.
        """
        super().leaveEvent(event)
        self.is_mouse_enter = False
        self.update()
