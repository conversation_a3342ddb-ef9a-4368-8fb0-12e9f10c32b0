from typing import Any, List

__all__: List[str]

def fft(a, n=..., axis=..., norm=...): ...
def ifft(a, n=..., axis=..., norm=...): ...
def rfft(a, n=..., axis=..., norm=...): ...
def irfft(a, n=..., axis=..., norm=...): ...
def hfft(a, n=..., axis=..., norm=...): ...
def ihfft(a, n=..., axis=..., norm=...): ...
def fftn(a, s=..., axes=..., norm=...): ...
def ifftn(a, s=..., axes=..., norm=...): ...
def rfftn(a, s=..., axes=..., norm=...): ...
def irfftn(a, s=..., axes=..., norm=...): ...
def fft2(a, s=..., axes=..., norm=...): ...
def ifft2(a, s=..., axes=..., norm=...): ...
def rfft2(a, s=..., axes=..., norm=...): ...
def irfft2(a, s=..., axes=..., norm=...): ...
def fftshift(x, axes=...): ...
def ifftshift(x, axes=...): ...
def fftfreq(n, d=...): ...
def rfftfreq(n, d=...): ...
