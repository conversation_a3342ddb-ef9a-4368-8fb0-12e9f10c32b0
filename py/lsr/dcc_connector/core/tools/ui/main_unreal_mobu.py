import os
import socket

from lsr.protostar.lib import Action<PERSON>ibrary as alib

from functools import reduce, partial

from lsr.python.core.compatible import cPickle

from lsr.qt.core import QtCore, QtWidgets
from lsr.qt.core.base_main_window import get_window_class
from lsr.dcc_connector.utils.socket_data import CommandMode, MayaMobuSocketData
from lsr.dcc_connector.core.tools.unreal_server import UnrealServer
from lsr.dcc_connector.core.tools.ui import VERSION, split_symbol, Mobu_CommandPort
from lsr.dcc_connector.utils.fbx_animation_copier import FbxAnimationCopier

import lsr.dcc_connector.core.tools.ui.widgets as widgets
import lsr.dcc_connector.core.tools.ui.socket_widget as socket_widget


# get the base main window class
base_class = get_window_class(app_name='LSR-Dcc Connector(Unreal to MotionBuilder) v{}'.format(VERSION))


class Unreal2Mobu_UI(base_class):
    """
    The main Unreal2Mobu_UI
    """

    _REUSE_SINGLETON = False
    _TRACK_SETTINGS = True

    def __init__(self):
        """ Creates and initializes this window. """
        self.__thread = None
        self.__cur_progress_max = 0
        self.__save_path = None
        super(Unreal2Mobu_UI, self).__init__()

        # self.refreshBtnCallback()
        self.create_connections()
        self.action_instance = self.get_action_class()

    def create_menu_bar(self, *args, **kwargs):
        """ Creates the menu bar. """
        bar = self.menuBar()
        menu = bar.addMenu('Project')
        action = menu.addAction('Save Project', None)
        action = menu.addAction('Load Project', None)

    def create_connections(self, *args, **kwargs):
        """
        Create connections
        Returns:

        """
        self.btn_widget.btn_refresh.clicked.connect(partial(self.refreshBtnCallback))
        self.btn_add.clicked.connect(partial(self.add_to_queue_btn_callback))

        self.btn_widget.btn_send.clicked.connect(partial(self.send_btn_callback))
        self.btn_widget.btn_rig_send.clicked.connect(partial(self.send_rig_callback))

    def save_settings(self):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(Unreal2Mobu_UI, self).save_settings()

        settings.beginGroup('fbx_data')
        settings.setValue('fbx_path', self.ue_widget.hik_rig_line_edit.text())
        settings.endGroup()

        settings.sync()
        return settings

    def load_settings(self):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(Unreal2Mobu_UI, self).load_settings()

        settings.beginGroup('fbx_data')
        self.ue_widget.hik_rig_line_edit.setText(settings.value('fbx_path', ''))
        settings.endGroup()

        return settings

    def setup_ui(self):
        """Creates UI elements."""
        self.create_menu_bar()

        self.centralwidget = QtWidgets.QWidget(self)
        vbox = QtWidgets.QVBoxLayout(self.centralwidget)
        vbox.setSpacing(3)
        vbox.setContentsMargins(5, 5, 5, 5)

        v_layout = QtWidgets.QVBoxLayout()

        upper_widget = QtWidgets.QWidget(self)
        up_h_box = QtWidgets.QHBoxLayout(upper_widget)

        self.splitter = QtWidgets.QSplitter(upper_widget)
        self.splitter.setOrientation(QtCore.Qt.Horizontal)

        self.ue_widget = widgets.Unreal_list_widget(self.splitter)
        self.mobu_widget = widgets.Mobu_list_widget(self.splitter)

        up_h_box.addWidget(self.splitter)

        add_widget = QtWidgets.QWidget(self)
        add_v_box = QtWidgets.QVBoxLayout(add_widget)
        space_01_item = QtWidgets.QSpacerItem(20, 40,
                                           QtWidgets.QSizePolicy.Minimum,
                                           QtWidgets.QSizePolicy.Expanding)
        add_v_box.addItem(space_01_item)
        self.btn_add = QtWidgets.QPushButton('Add To Queue', upper_widget)
        add_v_box.addWidget(self.btn_add)
        add_widget.setLayout(add_v_box)
        space_02_item = QtWidgets.QSpacerItem(20, 40,
                                              QtWidgets.QSizePolicy.Minimum,
                                              QtWidgets.QSizePolicy.Expanding)
        add_v_box.addItem(space_02_item)

        self.splitter.addWidget(add_widget)

        self.map_widget = widgets.Map_list_widget(self.splitter)
        v_layout.addWidget(upper_widget, stretch=10)

        line = QtWidgets.QFrame(self)
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        v_layout.addWidget(line, stretch=1)

        # Down Widget
        down_widget = QtWidgets.QWidget(self)
        down_h_box = QtWidgets.QHBoxLayout(down_widget)
        self.dn_splitter = QtWidgets.QSplitter(upper_widget)
        self.dn_splitter.setOrientation(QtCore.Qt.Horizontal)

        down_h_box.addWidget(self.dn_splitter)

        # SocketWidget
        s_widget = socket_widget.SocketWidget(sever_type=UnrealServer)
        self.dn_splitter.addWidget(s_widget)

        # Buttons Widget
        self.btn_widget = widgets.ClientButtonWidget(self, sever_type=UnrealServer)
        self.dn_splitter.addWidget(self.btn_widget)
        self.btn_widget.port_input.setText(str(Mobu_CommandPort))
        self.btn_widget.btn_send.setText('Animation >>> Send To MotionBuilder')
        self.btn_widget.btn_rig_send.setText('Rig >>> Send To MotionBuilder')

        v_layout.addWidget(down_widget, stretch=3)

        vbox.addLayout(v_layout)

        self.setCentralWidget(self.centralwidget)

    def get_action_class(self, *args, **kwargs):
        """
        This function is used to get the action class.

        Returns:
            class: The action class.
        """
        alib.refresh()
        dcc = os.getenv("PROTOSTAR_CONTEXT_FILTER")
        try:
            dcc_action_class = alib._ACTION_DICT["lsrDC_%s" % dcc]["DccRetargetApply"]
            self.action_instance = dcc_action_class()
            return self.action_instance
        except Exception as e:
            print("[DccRetargetApply] : %s" % e.__repr__())
            return None

    def hideEvent(self, event):
        """ Save settings before hiding. """
        self.closeEvent(event)

    def refreshBtnCallback(self, *args, **kwargs):
        """
        Refresh Button Callback

        Returns:

        """
        self.mobu_widget.MobuListWidget.clear()
        self.refresh_unreal_character_list()
        self.refresh_mobu_character_list()
        self.map_widget.mapListWidget.clear()

    def add_to_queue_btn_callback(self, *args, **kwargs):
        """
        Add To Queue Button Callback

        Returns:
            None
        """
        anim_seq = self.ue_widget.UEListWidget.selectedItems()
        mobu_char = self.mobu_widget.MobuListWidget.selectedItems()

        if len(anim_seq) == 0:
            raise ValueError('Unreal anim has not been selected!')

        if len(mobu_char) == 0:
            raise ValueError('MotionBuilder Character has not been selected!')

        added_text = '{}{}{}'.format(anim_seq[0].text(),
                                     split_symbol,
                                     mobu_char[0].text()
                                     )

        anim_obj = anim_seq[0].data(QtCore.Qt.UserRole)

        if self.is_mapping_existing_in_queue(added_text):
            print('The Mapping has already been added to the Queue.')
            return

        item = QtWidgets.QListWidgetItem(added_text)
        item.setData(QtCore.Qt.UserRole, anim_obj)
        self.map_widget.mapListWidget.addItem(item)

    def send_rig_callback(self, *args, **kwargs):
        """
        Send Rig Callback

        Args:
            *args ():
            **kwargs ():

        Returns:

        """
        print('Send Rig To MotionBuilder Button Clicked.')

    def send_btn_callback(self, *args, **kwargs):
        """
        Send Button Callback

        Returns:

        """
        hik_path = self.ue_widget.hik_rig_line_edit.text()
        if not os.path.exists(hik_path):
            raise ValueError('Hik Rig Path is not exist.')

        mapping_list = []
        row_count = self.map_widget.mapListWidget.count()
        for i in range(row_count):
            item = self.map_widget.mapListWidget.item(i)
            item_text = self.map_widget.mapListWidget.item(i).text()
            sp = item_text.split(split_symbol)
            asset_data = item.data(QtCore.Qt.UserRole)
            if len(sp) > 0:
                anim_seq = sp[0]
                mobu_char = sp[1]

                mapping_list.append([anim_seq, mobu_char, asset_data])

        print('Send To MotionBuilder Button Clicked.')

        # Send to MotionBuilder
        if len(mapping_list) == 0:
            raise ValueError('No retarget mapping was added to queue.')

        from lsr.unreal5 import umds

        for map_item in mapping_list:
            u_obj = map_item[2].get_asset()
            exp_folder = os.path.dirname(hik_path).replace('\\', '/')
            umds.FBXExport(objects=[u_obj], export_path=exp_folder, export_mesh=False)
            exp_path = '{0}/{1}.fbx'.format(exp_folder, u_obj.get_name()).replace('\\', '/')
            if os.path.exists(exp_path):
                print('Export Hik Rig Success.')
                copier = FbxAnimationCopier(animated_fbx_path=exp_path,
                                            static_fbx_path=hik_path,
                                            output_fbx_path=exp_path, ue_mode=True)

                copier.run()
            else:
                raise ValueError('Export Hik Rig Failed.')

            socket_data = MayaMobuSocketData()
            socket_data.commandType = CommandMode.export_cmd

            anim_seq = map_item[0].encode('utf-8')
            socket_data.characterName = anim_seq
            print('Anim Seq:' + map_item[0])

            mobu_char = map_item[1].encode('utf-8')
            socket_data.targetCharacter = mobu_char
            print('Mobu Character:' + map_item[1])

            socket_data = self.set_character_data_to_command(anim_seq, socket_data)
            try:
                socket_data.importFBXPath = exp_path
                socket_data.take_name = os.path.basename(exp_path)

                socket_data.frameRate = copier.frame_rate
                socket_data.frameStart = copier.start_frame
                socket_data.frameEnd = copier.end_frame

            except Exception as e:
                print('Export Unreal Animation Failed.')
                print(e)
                return

            print('Begin To Send Data To MotionBuilder.')

            # Socket
            m_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            port = self.btn_widget.port_input.text()
            try:
                m_socket.connect(('localhost', int(port)))
                serialized_obj = cPickle.dumps(socket_data)
                m_socket.sendall(serialized_obj)

                # print('socket_data :')
                res = cPickle.loads(serialized_obj)
                # print(res)

                # print('fbx_temp_path :')
                data = m_socket.recv(1024)
                fbx_temp_path = cPickle.loads(data) or []
                # print(fbx_temp_path)

                if os.path.exists(fbx_temp_path):
                    os.remove(fbx_temp_path)
                    # print(fbx_temp_path)

            except Exception as e:
                print('Send to MotionBuilder Fail:')
                print(e)

            m_socket.close()
            print('Socket Connection closed.')

    def refresh_mobu_character_list(self, *args, **kwargs):
        """
        Refresh Motion Builder Character List

        Returns:

        """
        self.mobu_widget.MobuListWidget.clear()

        socket_data = MayaMobuSocketData()
        socket_data.commandType = CommandMode.import_cmd
        recv_hik_list = []

        # Socket Setting
        m_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        port = self.btn_widget.port_input.text()
        try:
            m_socket.connect(('localhost', int(port)))
            serialized_obj = cPickle.dumps(socket_data)
            m_socket.sendall(serialized_obj)
            print('Begin To Receive Data From MotionBuilder.')
            data = m_socket.recv(1024)
            recv_hik_list = cPickle.loads(data) or []
            # print('recv_hik_list:')
            # print(recv_hik_list)
        except Exception as e:
            print('Connect to Mobu failed.')
            print(e)

        m_socket.close()
        print('Socket Connection closed.')

        if len(recv_hik_list) > 0:
            for mobu_char in recv_hik_list:
                item = QtWidgets.QListWidgetItem(mobu_char)
                self.mobu_widget.MobuListWidget.addItem(item)

    def refresh_unreal_character_list(self, *args, **kwargs):
        """
        Refresh Maya Character List

        Returns:

        """
        pass

    def is_mapping_existing_in_queue(self, itemText, *args, **kwargs):
        """
        Check if the added mapping already exists in the queue.
        Is Mapping Existing In Queue

        Args:
            itemText (str): The item text.

        Returns:
            bool: True if the mapping exists in the queue, False otherwise.
        """
        out = self.map_widget.mapListWidget.findItems(itemText, QtCore.Qt.MatchExactly)
        res = len(out) > 0
        return res

    def set_character_data_to_command(self, char_name, command, *args, **kwargs):
        """
        Set Character Data To Command

        Args:
            char_name (str): The name of the character.
            command (MayaMobuSocketData): The MayaMobuSocketData object.

        Returns:
            MayaMobuSocketData: The MayaMobuSocketData object.
        """
        this_command = command
        char_name = char_name.decode('utf-8')

        split_list = char_name.split(":")[:-1]

        if len(split_list) > 1:
            name_space = reduce(lambda x, y: x + ':' + y, split_list)
        elif len(split_list) == 1:
            name_space = split_list[0]
        else:
            name_space = ''

        this_command.maya_namespace = name_space

        return this_command
