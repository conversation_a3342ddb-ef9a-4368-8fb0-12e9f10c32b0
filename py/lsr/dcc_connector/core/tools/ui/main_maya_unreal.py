import os
import socket
import json

from lsr.protostar.lib import Action<PERSON><PERSON><PERSON> as alib

from functools import reduce, partial

from lsr.python.core.compatible import cPickle

from lsr.qt.core import QtCore, QtWidgets
from lsr.qt.core.base_main_window import get_window_class
from lsr.dcc_connector.utils.socket_data import MayaMobuSocketData, CommandMode
from lsr.dcc_connector.core.tools.maya_server import MayaServer
from lsr.dcc_connector.core.tools.ui import VERSION, split_symbol
import lsr.dcc_connector.core.tools.ui.widgets as widgets
import lsr.dcc_connector.core.tools.ui.socket_widget as socket_widget
from lsr.dcc_connector.utils.utils_action_operator import get_action_class


# get the base main window class
base_class = get_window_class(app_name='LSR-Dcc Connector(Maya to Unreal) v{}'.format(VERSION))


class MayaSendToUnrealUI(base_class):
    """
    The main MayaSendToUnrealUI
    """

    _REUSE_SINGLETON = False
    _TRACK_SETTINGS = True
    _ClinePort = 13442
    _UnrealPort = 13441

    def __init__(self):
        """ Creates and initializes this window. """
        self.__thread = None
        self.__cur_progress_max = 0
        self.__save_path = None
        super(MayaSendToUnrealUI, self).__init__()

        self.create_connections()
        action_class = get_action_class('DccRetargetApply')
        self.action_instance = action_class()
        self.cline_server = MayaServer('localhost', port=self._ClinePort)
        self.cline_server.start_classical()

    def create_menu_bar(self, *args, **kwargs):
        """ Creates the menu bar. """
        bar = self.menuBar()
        menu = bar.addMenu('Project')
        action = menu.addAction('Save Project', None)
        action = menu.addAction('Load Project', None)

    def create_connections(self, *args, **kwargs):
        """
        Create connections
        Returns:

        """
        self.btn_widget.btn_refresh.clicked.connect(partial(self.refreshBtnCallback))
        self.btn_add.clicked.connect(partial(self.add_to_queue_btn_callback))

        self.btn_widget.btn_send.clicked.connect(partial(self.send_btn_callback))
        self.btn_widget.btn_rig_send.clicked.connect(partial(self.send_rig_callback))

    def setup_ui(self):
        """Creates UI elements."""
        self.create_menu_bar()

        self.centralwidget = QtWidgets.QWidget(self)
        vbox = QtWidgets.QVBoxLayout(self.centralwidget)
        vbox.setSpacing(3)
        vbox.setContentsMargins(5, 5, 5, 5)

        v_layout = QtWidgets.QVBoxLayout()

        upper_widget = QtWidgets.QWidget(self)
        up_h_box = QtWidgets.QHBoxLayout(upper_widget)

        self.splitter = QtWidgets.QSplitter(upper_widget)
        self.splitter.setOrientation(QtCore.Qt.Horizontal)

        self.maya_widget = widgets.Maya_list_widget(self.splitter)
        self.ue_widget = widgets.Unreal_list_widget(self.splitter, is_send_mode=True)

        up_h_box.addWidget(self.splitter)

        add_widget = QtWidgets.QWidget(self)
        add_v_box = QtWidgets.QVBoxLayout(add_widget)
        space_01_item = QtWidgets.QSpacerItem(20, 40,
                                           QtWidgets.QSizePolicy.Minimum,
                                           QtWidgets.QSizePolicy.Expanding)
        add_v_box.addItem(space_01_item)
        self.btn_add = QtWidgets.QPushButton('Add To Queue', upper_widget)
        add_v_box.addWidget(self.btn_add)
        add_widget.setLayout(add_v_box)
        space_02_item = QtWidgets.QSpacerItem(20, 40,
                                              QtWidgets.QSizePolicy.Minimum,
                                              QtWidgets.QSizePolicy.Expanding)
        add_v_box.addItem(space_02_item)

        self.splitter.addWidget(add_widget)

        self.map_widget = widgets.Map_list_widget(self.splitter)
        v_layout.addWidget(upper_widget, stretch=10)

        line = QtWidgets.QFrame(self)
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        v_layout.addWidget(line, stretch=1)

        # Down Widget
        down_widget = QtWidgets.QWidget(self)
        down_h_box = QtWidgets.QHBoxLayout(down_widget)
        self.dn_splitter = QtWidgets.QSplitter(upper_widget)
        self.dn_splitter.setOrientation(QtCore.Qt.Horizontal)

        down_h_box.addWidget(self.dn_splitter)

        # SocketWidget
        s_widget = socket_widget.SocketWidget(sever_type=MayaServer)
        self.dn_splitter.addWidget(s_widget)

        # Buttons Widget
        self.btn_widget = widgets.ClientButtonWidget(self, sever_type=MayaServer, maya_mode=True)
        self.dn_splitter.addWidget(self.btn_widget)
        self.btn_widget.btn_send.setText('Animation >>> Send To Unreal')
        self.btn_widget.btn_rig_send.setText('Rig >>> Send To Unreal')

        v_layout.addWidget(down_widget, stretch=3)

        vbox.addLayout(v_layout)

        self.setCentralWidget(self.centralwidget)

    def save_settings(self):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(MayaSendToUnrealUI, self).save_settings()

        settings.beginGroup('fbx_data')
        settings.setValue('fbx_path', self.ue_widget.hik_rig_line_edit.text())
        settings.endGroup()

        settings.sync()
        return settings

    def load_settings(self):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(MayaSendToUnrealUI, self).load_settings()

        settings.beginGroup('fbx_data')
        self.ue_widget.hik_rig_line_edit.setText(settings.value('fbx_path', ''))
        settings.endGroup()

        return settings

    def hideEvent(self, event):
        """ Save settings before hiding. """
        self.closeEvent(event)

    def refreshBtnCallback(self, *args, **kwargs):
        """
        Refresh Button Callback

        Returns:

        """
        self.refresh_maya_character_list()
        self.refresh_unreal_character_list()
        self.map_widget.mapListWidget.clear()

    def add_to_queue_btn_callback(self, *args, **kwargs):
        """
        Add To Queue Button Callback

        Returns:
            None
        """
        maya_char = self.maya_widget.MayaListWidget.selectedItems()
        ue_char = self.ue_widget.UEListWidget.selectedItems()

        if len(maya_char) == 0:
            raise ValueError('Maya Character has not been selected!')

        if len(ue_char) == 0:
            raise ValueError('Unreal Skeleton has not been selected!')

        added_text = '{}{}{}'.format(maya_char[0].text(), split_symbol, ue_char[0].text())

        if self.is_mapping_existing_in_queue(added_text):
            print('The Mapping has already been added to the Queue.')
            return

        item = QtWidgets.QListWidgetItem(added_text)
        self.map_widget.mapListWidget.addItem(item)

    def send_rig_callback(self, *args, **kwargs):
        """
        Send Rig Callback

        Args:
            *args ():
            **kwargs ():

        Returns:

        """
        print('Send Rig To Unreal Button Clicked.')

    def send_btn_callback(self, *args, **kwargs):
        """
        Send Button Callback

        Returns:

        """
        mapping_list = []
        use_take_name = self.btn_widget.retarget_option_widget.take_rbt.isChecked()
        use_file_name = self.btn_widget.retarget_option_widget.filename_rbt.isChecked()
        use_custom_name = self.btn_widget.retarget_option_widget.custom_rbt.isChecked()

        if use_custom_name:
            custom_name = self.btn_widget.retarget_option_widget.custom_line_edit.text()
        else:
            custom_name = ''

        retarget_mode = self.btn_widget.retarget_option_widget.retarget_cbt.isChecked()
        hik_path = self.ue_widget.hik_rig_line_edit.text()

        row_count = self.map_widget.mapListWidget.count()
        for i in range(row_count):
            item = self.map_widget.mapListWidget.item(i).text()
            sp = item.split(split_symbol)
            if len(sp) > 0:
                maya_char = sp[0]
                ue_char = sp[1]
                mapping_list.append([maya_char, ue_char])

        print('Send To Unreal Button Clicked.')

        # Send to Unreal
        if len(mapping_list) == 0:
            self.action_instance.show_message_box("No Retargeting Mapping has been added to the Queue.")
            return

        for map_item in mapping_list:
            maya_char = map_item[0]
            ue_char = map_item[1]
            name_space = self.set_character_data_to_command(maya_char)

            socket_data = {"action_name": "import_animation_by_character"}
            kwargs_data = {}

            # Export Character Animation
            try:
                exp_path = self.action_instance.export_character_anim(
                    maya_char,
                    use_file_name=use_file_name,
                    use_take_name=use_take_name,
                    custom_name=custom_name,
                    retarget_mode=retarget_mode,
                    hik_path=hik_path.replace('\\', '/')
                )
                kwargs_data['importFBXPath'] = exp_path
                kwargs_data['frameRate'] = self.action_instance.get_frame_rate()
                kwargs_data['frameStart'] = self.action_instance.get_start_frame()
                kwargs_data['frameEnd'] = self.action_instance.get_end_frame()
                kwargs_data['name_space'] = name_space
                kwargs_data['targetCharacter'] = ue_char

            except Exception as e:
                print('Export Character Animation Failed.')
                print(e)
                raise RuntimeError(e)

            print('Begin To Send Data To Unreal.')

            # Socket
            socket_data['data'] = kwargs_data
            self.send_data_to_unreal(socket_data)

    def send_data_to_unreal(self, data, *args, **kwargs):
        """
        Send Data To Unreal

        Args:
            data (dict): The data to send.
            *args ():
            **kwargs ():

        Returns:

        """
        m_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        if 'data' not in data:
            data['data'] = {}
        data['data']['port'] = self._ClinePort
        try:
            m_socket.connect(('127.0.0.1', self._UnrealPort))
            m_socket.send(json.dumps(data).encode())

        except Exception as e:
            print('Send to Unreal Fail:')
            print(e)

        m_socket.close()
        print('Socket Connection closed.')

    def refresh_unreal_character_list(self, *args, **kwargs):
        """
        Refresh Maya Character List

        Returns:

        """
        self.ue_widget.UEListWidget.clear()

        data = {
            "action_name": "get_character_list",
            "data":
                {
                    "port": self._ClinePort
                }
        }

        self.send_data_to_unreal(data)

        return

    def refresh_maya_character_list(self, *args, **kwargs):
        """
        Refresh Maya Character List

        Returns:

        """
        char_name_list = self.action_instance.get_character_list()
        self.maya_widget.MayaListWidget.clear()
        for char_name in char_name_list:
            item = QtWidgets.QListWidgetItem(char_name)
            self.maya_widget.MayaListWidget.addItem(item)

    def is_mapping_existing_in_queue(self, itemText, *args, **kwargs):
        """
        Check if the added mapping already exists in the queue.
        Is Mapping Existing In Queue

        Args:
            itemText (str): The item text.

        Returns:
            bool: True if the mapping exists in the queue, False otherwise.
        """
        out = self.map_widget.mapListWidget.findItems(itemText, QtCore.Qt.MatchExactly)
        res = len(out) > 0
        return res

    def set_character_data_to_command(self, char_name, *args, **kwargs):
        """
        Set Character Data To Command

        Args:
            char_name (str): The name of the character.

        Returns:
            str: The namespace of the character.
        """
        split_list = char_name.split(":")[:-1]

        if len(split_list) > 1:
            name_space = reduce(lambda x, y: x + ':' + y, split_list)
        elif len(split_list) == 1:
            name_space = split_list[0]
        else:
            name_space = ''

        print("Namespace of character is:{}".format(name_space))

        return name_space
