// qtouchdevice.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2021 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTouchDevice
{
%TypeHeaderCode
#include <qtouchdevice.h>
%End

public:
    enum DeviceType
    {
        TouchScreen,
        TouchPad,
    };

    enum CapabilityFlag
    {
        Position,
        Area,
        Pressure,
        Velocity,
        RawPositions,
        NormalizedPosition,
%If (Qt_5_5_0 -)
        MouseEmulation,
%End
    };

    typedef QFlags<QTouchDevice::CapabilityFlag> Capabilities;
    QTouchDevice();
    ~QTouchDevice();
    static QList<const QTouchDevice *> devices();
    QString name() const;
    QTouchDevice::DeviceType type() const;
    QTouchDevice::Capabilities capabilities() const;
    void setName(const QString &name);
    void setType(QTouchDevice::DeviceType devType);
    void setCapabilities(QTouchDevice::Capabilities caps);
%If (Qt_5_2_0 -)
    int maximumTouchPoints() const;
%End
%If (Qt_5_2_0 -)
    void setMaximumTouchPoints(int max);
%End
};

QFlags<QTouchDevice::CapabilityFlag> operator|(QTouchDevice::CapabilityFlag f1, QFlags<QTouchDevice::CapabilityFlag> f2);
