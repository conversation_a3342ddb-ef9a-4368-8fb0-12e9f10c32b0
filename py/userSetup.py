import os
import maya.cmds as cmds
import maya.utils

import lsr.python.pathlib_pkg
import lsr.python.enum_pkg
import lsr.python.fbxsdk_pkg
import lsr.python.numpy_pkg
import lsr.python.GitPython_pkg
import lsr.python.requests_pkg
import lsr.python.Qt_pkg

import lsr.protostar.constants as const


class MayaCoreInit(object):
    """
    Maya Core Init Class
    """

    def __init__(self):

        pass

    @staticmethod
    def run():
        """Run scripts"""
        os.environ[const.CONTEXT_FILTER_VAR] = 'maya'
        cmds.evalDeferred('import lsr.maya.startup.runner as runner;runner.initialize()')
        MayaCoreInit.set_option_vars()
        # MayaCoreInit.add_callbacks()
        MayaCoreInit.add_menus()

    @staticmethod
    def set_option_vars():
        """Set maya option vars"""
        cmds.optionVar(
            stringValue=[('vp2RenderingEngine', 'DirectX11'),
                         ('vp2RenderingEngineHold', 'DirectX11'),
                         ('workingUnitTime', 'ntsc'),
                         ('workingUnitTimeDefault', 'ntsc'),

                         ],
            floatValue=[
                ('playblastScale', 1),
                ('timeSliderPlaySpeed', 1),
                ('playbackMin', 1),
                ('playbackMinDefault', 1),
                ('playbackMinRange', 1),
                ('playbackMinRangeDefault', 1),
                ('playbackMax', 120),
                ('playbackMaxDefault', 120),
                ('playbackMaxRange', 120),
                ('playbackMaxRangeDefault', 120)
            ]
        )

        print('[LSR] Set Maya Core optionVars')

    @staticmethod
    def add_callbacks():
        """Add Maya Core callbacks"""
        from lsr.maya.startup import animlayer_watcher
        animlayer_watcher.initialize()

        print('[LSR] Add Maya Core callbacks')

    @staticmethod
    def add_menus():
        """Add Maya Menu"""
        print('[LSR] Add Maya Menus')


maya.utils.executeDeferred(MayaCoreInit.run)
