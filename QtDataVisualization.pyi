# This Python file uses the following encoding: utf-8
#############################################################################
##
## Copyright (C) 2020 The Qt Company Ltd.
## Contact: https://www.qt.io/licensing/
##
## This file is part of Qt for Python.
##
## $QT_BEGIN_LICENSE:LGPL$
## Commercial License Usage
## Licensees holding valid commercial Qt licenses may use this file in
## accordance with the commercial license agreement provided with the
## Software or, alternatively, in accordance with the terms contained in
## a written agreement between you and The Qt Company. For licensing terms
## and conditions see https://www.qt.io/terms-conditions. For further
## information use the contact form at https://www.qt.io/contact-us.
##
## GNU Lesser General Public License Usage
## Alternatively, this file may be used under the terms of the GNU Lesser
## General Public License version 3 as published by the Free Software
## Foundation and appearing in the file LICENSE.LGPL3 included in the
## packaging of this file. Please review the following information to
## ensure the GNU Lesser General Public License version 3 requirements
## will be met: https://www.gnu.org/licenses/lgpl-3.0.html.
##
## GNU General Public License Usage
## Alternatively, this file may be used under the terms of the GNU
## General Public License version 2.0 or (at your option) the GNU General
## Public license version 3 or any later version approved by the KDE Free
## Qt Foundation. The licenses are as published by the Free Software
## Foundation and appearing in the file LICENSE.GPL2 and LICENSE.GPL3
## included in the packaging of this file. Please review the following
## information to ensure the GNU General Public License requirements will
## be met: https://www.gnu.org/licenses/gpl-2.0.html and
## https://www.gnu.org/licenses/gpl-3.0.html.
##
## $QT_END_LICENSE$
##
#############################################################################

"""
This file contains the exact signatures for all functions in module
PySide2.QtDataVisualization, except for defaults which are replaced by "...".
"""

# Module PySide2.QtDataVisualization
import PySide2
try:
    import typing
except ImportError:
    from PySide2.support.signature import typing
from PySide2.support.signature.mapping import (
    Virtual, Missing, Invalid, Default, Instance)

class Object(object): pass

import shiboken2 as Shiboken
Shiboken.Object = Object

import PySide2.QtCore
import PySide2.QtGui
import PySide2.QtDataVisualization


class QtDataVisualization(Shiboken.Object):

    class Q3DBars(PySide2.QtDataVisualization.QAbstract3DGraph):

        def __init__(self, format:typing.Optional[PySide2.QtGui.QSurfaceFormat]=..., parent:typing.Optional[PySide2.QtGui.QWindow]=...): ...

        def addAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DAxis): ...
        def addSeries(self, series:PySide2.QtDataVisualization.QtDataVisualization.QBar3DSeries): ...
        def axes(self) -> typing.List: ...
        def barSpacing(self) -> PySide2.QtCore.QSizeF: ...
        def barThickness(self) -> float: ...
        def columnAxis(self) -> PySide2.QtDataVisualization.QtDataVisualization.QCategory3DAxis: ...
        def floorLevel(self) -> float: ...
        def insertSeries(self, index:int, series:PySide2.QtDataVisualization.QtDataVisualization.QBar3DSeries): ...
        def isBarSpacingRelative(self) -> bool: ...
        def isMultiSeriesUniform(self) -> bool: ...
        def primarySeries(self) -> PySide2.QtDataVisualization.QtDataVisualization.QBar3DSeries: ...
        def releaseAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DAxis): ...
        def removeSeries(self, series:PySide2.QtDataVisualization.QtDataVisualization.QBar3DSeries): ...
        def rowAxis(self) -> PySide2.QtDataVisualization.QtDataVisualization.QCategory3DAxis: ...
        def selectedSeries(self) -> PySide2.QtDataVisualization.QtDataVisualization.QBar3DSeries: ...
        def seriesList(self) -> typing.List: ...
        def setBarSpacing(self, spacing:PySide2.QtCore.QSizeF): ...
        def setBarSpacingRelative(self, relative:bool): ...
        def setBarThickness(self, thicknessRatio:float): ...
        def setColumnAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QCategory3DAxis): ...
        def setFloorLevel(self, level:float): ...
        def setMultiSeriesUniform(self, uniform:bool): ...
        def setPrimarySeries(self, series:PySide2.QtDataVisualization.QtDataVisualization.QBar3DSeries): ...
        def setRowAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QCategory3DAxis): ...
        def setValueAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def valueAxis(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...

    class Q3DCamera(PySide2.QtDataVisualization.Q3DObject):
        CameraPresetNone         : QtDataVisualization.Q3DCamera = ... # -0x1
        CameraPresetFrontLow     : QtDataVisualization.Q3DCamera = ... # 0x0
        CameraPresetFront        : QtDataVisualization.Q3DCamera = ... # 0x1
        CameraPresetFrontHigh    : QtDataVisualization.Q3DCamera = ... # 0x2
        CameraPresetLeftLow      : QtDataVisualization.Q3DCamera = ... # 0x3
        CameraPresetLeft         : QtDataVisualization.Q3DCamera = ... # 0x4
        CameraPresetLeftHigh     : QtDataVisualization.Q3DCamera = ... # 0x5
        CameraPresetRightLow     : QtDataVisualization.Q3DCamera = ... # 0x6
        CameraPresetRight        : QtDataVisualization.Q3DCamera = ... # 0x7
        CameraPresetRightHigh    : QtDataVisualization.Q3DCamera = ... # 0x8
        CameraPresetBehindLow    : QtDataVisualization.Q3DCamera = ... # 0x9
        CameraPresetBehind       : QtDataVisualization.Q3DCamera = ... # 0xa
        CameraPresetBehindHigh   : QtDataVisualization.Q3DCamera = ... # 0xb
        CameraPresetIsometricLeft: QtDataVisualization.Q3DCamera = ... # 0xc
        CameraPresetIsometricLeftHigh: QtDataVisualization.Q3DCamera = ... # 0xd
        CameraPresetIsometricRight: QtDataVisualization.Q3DCamera = ... # 0xe
        CameraPresetIsometricRightHigh: QtDataVisualization.Q3DCamera = ... # 0xf
        CameraPresetDirectlyAbove: QtDataVisualization.Q3DCamera = ... # 0x10
        CameraPresetDirectlyAboveCW45: QtDataVisualization.Q3DCamera = ... # 0x11
        CameraPresetDirectlyAboveCCW45: QtDataVisualization.Q3DCamera = ... # 0x12
        CameraPresetFrontBelow   : QtDataVisualization.Q3DCamera = ... # 0x13
        CameraPresetLeftBelow    : QtDataVisualization.Q3DCamera = ... # 0x14
        CameraPresetRightBelow   : QtDataVisualization.Q3DCamera = ... # 0x15
        CameraPresetBehindBelow  : QtDataVisualization.Q3DCamera = ... # 0x16
        CameraPresetDirectlyBelow: QtDataVisualization.Q3DCamera = ... # 0x17

        class CameraPreset(object):
            CameraPresetNone         : QtDataVisualization.Q3DCamera.CameraPreset = ... # -0x1
            CameraPresetFrontLow     : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x0
            CameraPresetFront        : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x1
            CameraPresetFrontHigh    : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x2
            CameraPresetLeftLow      : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x3
            CameraPresetLeft         : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x4
            CameraPresetLeftHigh     : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x5
            CameraPresetRightLow     : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x6
            CameraPresetRight        : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x7
            CameraPresetRightHigh    : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x8
            CameraPresetBehindLow    : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x9
            CameraPresetBehind       : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0xa
            CameraPresetBehindHigh   : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0xb
            CameraPresetIsometricLeft: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0xc
            CameraPresetIsometricLeftHigh: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0xd
            CameraPresetIsometricRight: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0xe
            CameraPresetIsometricRightHigh: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0xf
            CameraPresetDirectlyAbove: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x10
            CameraPresetDirectlyAboveCW45: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x11
            CameraPresetDirectlyAboveCCW45: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x12
            CameraPresetFrontBelow   : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x13
            CameraPresetLeftBelow    : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x14
            CameraPresetRightBelow   : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x15
            CameraPresetBehindBelow  : QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x16
            CameraPresetDirectlyBelow: QtDataVisualization.Q3DCamera.CameraPreset = ... # 0x17

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def cameraPreset(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DCamera.CameraPreset: ...
        def copyValuesFrom(self, source:PySide2.QtDataVisualization.QtDataVisualization.Q3DObject): ...
        def maxZoomLevel(self) -> float: ...
        def minZoomLevel(self) -> float: ...
        def setCameraPosition(self, horizontal:float, vertical:float, zoom:float=...): ...
        def setCameraPreset(self, preset:PySide2.QtDataVisualization.QtDataVisualization.Q3DCamera.CameraPreset): ...
        def setMaxZoomLevel(self, zoomLevel:float): ...
        def setMinZoomLevel(self, zoomLevel:float): ...
        def setTarget(self, target:PySide2.QtGui.QVector3D): ...
        def setWrapXRotation(self, isEnabled:bool): ...
        def setWrapYRotation(self, isEnabled:bool): ...
        def setXRotation(self, rotation:float): ...
        def setYRotation(self, rotation:float): ...
        def setZoomLevel(self, zoomLevel:float): ...
        def target(self) -> PySide2.QtGui.QVector3D: ...
        def wrapXRotation(self) -> bool: ...
        def wrapYRotation(self) -> bool: ...
        def xRotation(self) -> float: ...
        def yRotation(self) -> float: ...
        def zoomLevel(self) -> float: ...

    class Q3DInputHandler(PySide2.QtDataVisualization.QAbstract3DInputHandler):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def isRotationEnabled(self) -> bool: ...
        def isSelectionEnabled(self) -> bool: ...
        def isZoomAtTargetEnabled(self) -> bool: ...
        def isZoomEnabled(self) -> bool: ...
        def mouseMoveEvent(self, event:PySide2.QtGui.QMouseEvent, mousePos:PySide2.QtCore.QPoint): ...
        def mousePressEvent(self, event:PySide2.QtGui.QMouseEvent, mousePos:PySide2.QtCore.QPoint): ...
        def mouseReleaseEvent(self, event:PySide2.QtGui.QMouseEvent, mousePos:PySide2.QtCore.QPoint): ...
        def setRotationEnabled(self, enable:bool): ...
        def setSelectionEnabled(self, enable:bool): ...
        def setZoomAtTargetEnabled(self, enable:bool): ...
        def setZoomEnabled(self, enable:bool): ...
        def wheelEvent(self, event:PySide2.QtGui.QWheelEvent): ...

    class Q3DLight(PySide2.QtDataVisualization.Q3DObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def isAutoPosition(self) -> bool: ...
        def setAutoPosition(self, enabled:bool): ...

    class Q3DObject(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def copyValuesFrom(self, source:PySide2.QtDataVisualization.QtDataVisualization.Q3DObject): ...
        def isDirty(self) -> bool: ...
        def parentScene(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DScene: ...
        def position(self) -> PySide2.QtGui.QVector3D: ...
        def setDirty(self, dirty:bool): ...
        def setPosition(self, position:PySide2.QtGui.QVector3D): ...

    class Q3DScatter(PySide2.QtDataVisualization.QAbstract3DGraph):

        def __init__(self, format:typing.Optional[PySide2.QtGui.QSurfaceFormat]=..., parent:typing.Optional[PySide2.QtGui.QWindow]=...): ...

        def addAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def addSeries(self, series:PySide2.QtDataVisualization.QtDataVisualization.QScatter3DSeries): ...
        def axes(self) -> typing.List: ...
        def axisX(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...
        def axisY(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...
        def axisZ(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...
        def releaseAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def removeSeries(self, series:PySide2.QtDataVisualization.QtDataVisualization.QScatter3DSeries): ...
        def selectedSeries(self) -> PySide2.QtDataVisualization.QtDataVisualization.QScatter3DSeries: ...
        def seriesList(self) -> typing.List: ...
        def setAxisX(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def setAxisY(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def setAxisZ(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...

    class Q3DScene(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def activeCamera(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DCamera: ...
        def activeLight(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DLight: ...
        def devicePixelRatio(self) -> float: ...
        def graphPositionQuery(self) -> PySide2.QtCore.QPoint: ...
        @staticmethod
        def invalidSelectionPoint() -> PySide2.QtCore.QPoint: ...
        def isPointInPrimarySubView(self, point:PySide2.QtCore.QPoint) -> bool: ...
        def isPointInSecondarySubView(self, point:PySide2.QtCore.QPoint) -> bool: ...
        def isSecondarySubviewOnTop(self) -> bool: ...
        def isSlicingActive(self) -> bool: ...
        def primarySubViewport(self) -> PySide2.QtCore.QRect: ...
        def secondarySubViewport(self) -> PySide2.QtCore.QRect: ...
        def selectionQueryPosition(self) -> PySide2.QtCore.QPoint: ...
        def setActiveCamera(self, camera:PySide2.QtDataVisualization.QtDataVisualization.Q3DCamera): ...
        def setActiveLight(self, light:PySide2.QtDataVisualization.QtDataVisualization.Q3DLight): ...
        def setDevicePixelRatio(self, pixelRatio:float): ...
        def setGraphPositionQuery(self, point:PySide2.QtCore.QPoint): ...
        def setPrimarySubViewport(self, primarySubViewport:PySide2.QtCore.QRect): ...
        def setSecondarySubViewport(self, secondarySubViewport:PySide2.QtCore.QRect): ...
        def setSecondarySubviewOnTop(self, isSecondaryOnTop:bool): ...
        def setSelectionQueryPosition(self, point:PySide2.QtCore.QPoint): ...
        def setSlicingActive(self, isSlicing:bool): ...
        def viewport(self) -> PySide2.QtCore.QRect: ...

    class Q3DSurface(PySide2.QtDataVisualization.QAbstract3DGraph):

        def __init__(self, format:typing.Optional[PySide2.QtGui.QSurfaceFormat]=..., parent:typing.Optional[PySide2.QtGui.QWindow]=...): ...

        def addAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def addSeries(self, series:PySide2.QtDataVisualization.QtDataVisualization.QSurface3DSeries): ...
        def axes(self) -> typing.List: ...
        def axisX(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...
        def axisY(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...
        def axisZ(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...
        def flipHorizontalGrid(self) -> bool: ...
        def releaseAxis(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def removeSeries(self, series:PySide2.QtDataVisualization.QtDataVisualization.QSurface3DSeries): ...
        def selectedSeries(self) -> PySide2.QtDataVisualization.QtDataVisualization.QSurface3DSeries: ...
        def seriesList(self) -> typing.List: ...
        def setAxisX(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def setAxisY(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def setAxisZ(self, axis:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis): ...
        def setFlipHorizontalGrid(self, flip:bool): ...

    class Q3DTheme(PySide2.QtCore.QObject):
        ColorStyleUniform        : QtDataVisualization.Q3DTheme = ... # 0x0
        ThemeQt                  : QtDataVisualization.Q3DTheme = ... # 0x0
        ColorStyleObjectGradient : QtDataVisualization.Q3DTheme = ... # 0x1
        ThemePrimaryColors       : QtDataVisualization.Q3DTheme = ... # 0x1
        ColorStyleRangeGradient  : QtDataVisualization.Q3DTheme = ... # 0x2
        ThemeDigia               : QtDataVisualization.Q3DTheme = ... # 0x2
        ThemeStoneMoss           : QtDataVisualization.Q3DTheme = ... # 0x3
        ThemeArmyBlue            : QtDataVisualization.Q3DTheme = ... # 0x4
        ThemeRetro               : QtDataVisualization.Q3DTheme = ... # 0x5
        ThemeEbony               : QtDataVisualization.Q3DTheme = ... # 0x6
        ThemeIsabelle            : QtDataVisualization.Q3DTheme = ... # 0x7
        ThemeUserDefined         : QtDataVisualization.Q3DTheme = ... # 0x8

        class ColorStyle(object):
            ColorStyleUniform        : QtDataVisualization.Q3DTheme.ColorStyle = ... # 0x0
            ColorStyleObjectGradient : QtDataVisualization.Q3DTheme.ColorStyle = ... # 0x1
            ColorStyleRangeGradient  : QtDataVisualization.Q3DTheme.ColorStyle = ... # 0x2

        class Theme(object):
            ThemeQt                  : QtDataVisualization.Q3DTheme.Theme = ... # 0x0
            ThemePrimaryColors       : QtDataVisualization.Q3DTheme.Theme = ... # 0x1
            ThemeDigia               : QtDataVisualization.Q3DTheme.Theme = ... # 0x2
            ThemeStoneMoss           : QtDataVisualization.Q3DTheme.Theme = ... # 0x3
            ThemeArmyBlue            : QtDataVisualization.Q3DTheme.Theme = ... # 0x4
            ThemeRetro               : QtDataVisualization.Q3DTheme.Theme = ... # 0x5
            ThemeEbony               : QtDataVisualization.Q3DTheme.Theme = ... # 0x6
            ThemeIsabelle            : QtDataVisualization.Q3DTheme.Theme = ... # 0x7
            ThemeUserDefined         : QtDataVisualization.Q3DTheme.Theme = ... # 0x8

        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, themeType:PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme.Theme, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def ambientLightStrength(self) -> float: ...
        def backgroundColor(self) -> PySide2.QtGui.QColor: ...
        def baseColors(self) -> typing.List: ...
        def baseGradients(self) -> typing.List: ...
        def colorStyle(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme.ColorStyle: ...
        def font(self) -> PySide2.QtGui.QFont: ...
        def gridLineColor(self) -> PySide2.QtGui.QColor: ...
        def highlightLightStrength(self) -> float: ...
        def isBackgroundEnabled(self) -> bool: ...
        def isGridEnabled(self) -> bool: ...
        def isLabelBackgroundEnabled(self) -> bool: ...
        def isLabelBorderEnabled(self) -> bool: ...
        def labelBackgroundColor(self) -> PySide2.QtGui.QColor: ...
        def labelTextColor(self) -> PySide2.QtGui.QColor: ...
        def lightColor(self) -> PySide2.QtGui.QColor: ...
        def lightStrength(self) -> float: ...
        def multiHighlightColor(self) -> PySide2.QtGui.QColor: ...
        def multiHighlightGradient(self) -> PySide2.QtGui.QLinearGradient: ...
        def setAmbientLightStrength(self, strength:float): ...
        def setBackgroundColor(self, color:PySide2.QtGui.QColor): ...
        def setBackgroundEnabled(self, enabled:bool): ...
        def setBaseColors(self, colors:typing.Sequence): ...
        def setBaseGradients(self, gradients:typing.Sequence): ...
        def setColorStyle(self, style:PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme.ColorStyle): ...
        def setFont(self, font:PySide2.QtGui.QFont): ...
        def setGridEnabled(self, enabled:bool): ...
        def setGridLineColor(self, color:PySide2.QtGui.QColor): ...
        def setHighlightLightStrength(self, strength:float): ...
        def setLabelBackgroundColor(self, color:PySide2.QtGui.QColor): ...
        def setLabelBackgroundEnabled(self, enabled:bool): ...
        def setLabelBorderEnabled(self, enabled:bool): ...
        def setLabelTextColor(self, color:PySide2.QtGui.QColor): ...
        def setLightColor(self, color:PySide2.QtGui.QColor): ...
        def setLightStrength(self, strength:float): ...
        def setMultiHighlightColor(self, color:PySide2.QtGui.QColor): ...
        def setMultiHighlightGradient(self, gradient:PySide2.QtGui.QLinearGradient): ...
        def setSingleHighlightColor(self, color:PySide2.QtGui.QColor): ...
        def setSingleHighlightGradient(self, gradient:PySide2.QtGui.QLinearGradient): ...
        def setType(self, themeType:PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme.Theme): ...
        def setWindowColor(self, color:PySide2.QtGui.QColor): ...
        def singleHighlightColor(self) -> PySide2.QtGui.QColor: ...
        def singleHighlightGradient(self) -> PySide2.QtGui.QLinearGradient: ...
        def type(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme.Theme: ...
        def windowColor(self) -> PySide2.QtGui.QColor: ...

    class QAbstract3DAxis(PySide2.QtCore.QObject):
        AxisOrientationNone      : QtDataVisualization.QAbstract3DAxis = ... # 0x0
        AxisTypeNone             : QtDataVisualization.QAbstract3DAxis = ... # 0x0
        AxisOrientationX         : QtDataVisualization.QAbstract3DAxis = ... # 0x1
        AxisTypeCategory         : QtDataVisualization.QAbstract3DAxis = ... # 0x1
        AxisOrientationY         : QtDataVisualization.QAbstract3DAxis = ... # 0x2
        AxisTypeValue            : QtDataVisualization.QAbstract3DAxis = ... # 0x2
        AxisOrientationZ         : QtDataVisualization.QAbstract3DAxis = ... # 0x4

        class AxisOrientation(object):
            AxisOrientationNone      : QtDataVisualization.QAbstract3DAxis.AxisOrientation = ... # 0x0
            AxisOrientationX         : QtDataVisualization.QAbstract3DAxis.AxisOrientation = ... # 0x1
            AxisOrientationY         : QtDataVisualization.QAbstract3DAxis.AxisOrientation = ... # 0x2
            AxisOrientationZ         : QtDataVisualization.QAbstract3DAxis.AxisOrientation = ... # 0x4

        class AxisType(object):
            AxisTypeNone             : QtDataVisualization.QAbstract3DAxis.AxisType = ... # 0x0
            AxisTypeCategory         : QtDataVisualization.QAbstract3DAxis.AxisType = ... # 0x1
            AxisTypeValue            : QtDataVisualization.QAbstract3DAxis.AxisType = ... # 0x2
        def isAutoAdjustRange(self) -> bool: ...
        def isTitleFixed(self) -> bool: ...
        def isTitleVisible(self) -> bool: ...
        def labelAutoRotation(self) -> float: ...
        def labels(self) -> typing.List: ...
        def max(self) -> float: ...
        def min(self) -> float: ...
        def orientation(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DAxis.AxisOrientation: ...
        def setAutoAdjustRange(self, autoAdjust:bool): ...
        def setLabelAutoRotation(self, angle:float): ...
        def setLabels(self, labels:typing.Sequence): ...
        def setMax(self, max:float): ...
        def setMin(self, min:float): ...
        def setRange(self, min:float, max:float): ...
        def setTitle(self, title:str): ...
        def setTitleFixed(self, fixed:bool): ...
        def setTitleVisible(self, visible:bool): ...
        def title(self) -> str: ...
        def type(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DAxis.AxisType: ...

    class QAbstract3DGraph(PySide2.QtGui.QWindow):
        ElementNone              : QtDataVisualization.QAbstract3DGraph = ... # 0x0
        OptimizationDefault      : QtDataVisualization.QAbstract3DGraph = ... # 0x0
        SelectionNone            : QtDataVisualization.QAbstract3DGraph = ... # 0x0
        ShadowQualityNone        : QtDataVisualization.QAbstract3DGraph = ... # 0x0
        ElementSeries            : QtDataVisualization.QAbstract3DGraph = ... # 0x1
        OptimizationStatic       : QtDataVisualization.QAbstract3DGraph = ... # 0x1
        SelectionItem            : QtDataVisualization.QAbstract3DGraph = ... # 0x1
        ShadowQualityLow         : QtDataVisualization.QAbstract3DGraph = ... # 0x1
        ElementAxisXLabel        : QtDataVisualization.QAbstract3DGraph = ... # 0x2
        SelectionRow             : QtDataVisualization.QAbstract3DGraph = ... # 0x2
        ShadowQualityMedium      : QtDataVisualization.QAbstract3DGraph = ... # 0x2
        ElementAxisYLabel        : QtDataVisualization.QAbstract3DGraph = ... # 0x3
        SelectionItemAndRow      : QtDataVisualization.QAbstract3DGraph = ... # 0x3
        ShadowQualityHigh        : QtDataVisualization.QAbstract3DGraph = ... # 0x3
        ElementAxisZLabel        : QtDataVisualization.QAbstract3DGraph = ... # 0x4
        SelectionColumn          : QtDataVisualization.QAbstract3DGraph = ... # 0x4
        ShadowQualitySoftLow     : QtDataVisualization.QAbstract3DGraph = ... # 0x4
        ElementCustomItem        : QtDataVisualization.QAbstract3DGraph = ... # 0x5
        SelectionItemAndColumn   : QtDataVisualization.QAbstract3DGraph = ... # 0x5
        ShadowQualitySoftMedium  : QtDataVisualization.QAbstract3DGraph = ... # 0x5
        SelectionRowAndColumn    : QtDataVisualization.QAbstract3DGraph = ... # 0x6
        ShadowQualitySoftHigh    : QtDataVisualization.QAbstract3DGraph = ... # 0x6
        SelectionItemRowAndColumn: QtDataVisualization.QAbstract3DGraph = ... # 0x7
        SelectionSlice           : QtDataVisualization.QAbstract3DGraph = ... # 0x8
        SelectionMultiSeries     : QtDataVisualization.QAbstract3DGraph = ... # 0x10

        class ElementType(object):
            ElementNone              : QtDataVisualization.QAbstract3DGraph.ElementType = ... # 0x0
            ElementSeries            : QtDataVisualization.QAbstract3DGraph.ElementType = ... # 0x1
            ElementAxisXLabel        : QtDataVisualization.QAbstract3DGraph.ElementType = ... # 0x2
            ElementAxisYLabel        : QtDataVisualization.QAbstract3DGraph.ElementType = ... # 0x3
            ElementAxisZLabel        : QtDataVisualization.QAbstract3DGraph.ElementType = ... # 0x4
            ElementCustomItem        : QtDataVisualization.QAbstract3DGraph.ElementType = ... # 0x5

        class OptimizationHint(object):
            OptimizationDefault      : QtDataVisualization.QAbstract3DGraph.OptimizationHint = ... # 0x0
            OptimizationStatic       : QtDataVisualization.QAbstract3DGraph.OptimizationHint = ... # 0x1

        class OptimizationHints(object): ...

        class SelectionFlag(object):
            SelectionNone            : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x0
            SelectionItem            : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x1
            SelectionRow             : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x2
            SelectionItemAndRow      : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x3
            SelectionColumn          : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x4
            SelectionItemAndColumn   : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x5
            SelectionRowAndColumn    : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x6
            SelectionItemRowAndColumn: QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x7
            SelectionSlice           : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x8
            SelectionMultiSeries     : QtDataVisualization.QAbstract3DGraph.SelectionFlag = ... # 0x10

        class SelectionFlags(object): ...

        class ShadowQuality(object):
            ShadowQualityNone        : QtDataVisualization.QAbstract3DGraph.ShadowQuality = ... # 0x0
            ShadowQualityLow         : QtDataVisualization.QAbstract3DGraph.ShadowQuality = ... # 0x1
            ShadowQualityMedium      : QtDataVisualization.QAbstract3DGraph.ShadowQuality = ... # 0x2
            ShadowQualityHigh        : QtDataVisualization.QAbstract3DGraph.ShadowQuality = ... # 0x3
            ShadowQualitySoftLow     : QtDataVisualization.QAbstract3DGraph.ShadowQuality = ... # 0x4
            ShadowQualitySoftMedium  : QtDataVisualization.QAbstract3DGraph.ShadowQuality = ... # 0x5
            ShadowQualitySoftHigh    : QtDataVisualization.QAbstract3DGraph.ShadowQuality = ... # 0x6
        def activeInputHandler(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DInputHandler: ...
        def activeTheme(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme: ...
        def addCustomItem(self, item:PySide2.QtDataVisualization.QtDataVisualization.QCustom3DItem) -> int: ...
        def addInputHandler(self, inputHandler:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DInputHandler): ...
        def addTheme(self, theme:PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme): ...
        def aspectRatio(self) -> float: ...
        def clearSelection(self): ...
        def currentFps(self) -> float: ...
        def customItems(self) -> typing.List: ...
        def event(self, event:PySide2.QtCore.QEvent) -> bool: ...
        def exposeEvent(self, event:PySide2.QtGui.QExposeEvent): ...
        def hasContext(self) -> bool: ...
        def horizontalAspectRatio(self) -> float: ...
        def inputHandlers(self) -> typing.List: ...
        def isOrthoProjection(self) -> bool: ...
        def isPolar(self) -> bool: ...
        def isReflection(self) -> bool: ...
        def locale(self) -> PySide2.QtCore.QLocale: ...
        def margin(self) -> float: ...
        def measureFps(self) -> bool: ...
        def mouseDoubleClickEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def mouseMoveEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def mousePressEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def mouseReleaseEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def optimizationHints(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DGraph.OptimizationHints: ...
        def queriedGraphPosition(self) -> PySide2.QtGui.QVector3D: ...
        def radialLabelOffset(self) -> float: ...
        def reflectivity(self) -> float: ...
        def releaseCustomItem(self, item:PySide2.QtDataVisualization.QtDataVisualization.QCustom3DItem): ...
        def releaseInputHandler(self, inputHandler:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DInputHandler): ...
        def releaseTheme(self, theme:PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme): ...
        def removeCustomItem(self, item:PySide2.QtDataVisualization.QtDataVisualization.QCustom3DItem): ...
        def removeCustomItemAt(self, position:PySide2.QtGui.QVector3D): ...
        def removeCustomItems(self): ...
        def renderToImage(self, msaaSamples:int=..., imageSize:PySide2.QtCore.QSize=...) -> PySide2.QtGui.QImage: ...
        def resizeEvent(self, event:PySide2.QtGui.QResizeEvent): ...
        def scene(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DScene: ...
        def selectedAxis(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DAxis: ...
        def selectedCustomItem(self) -> PySide2.QtDataVisualization.QtDataVisualization.QCustom3DItem: ...
        def selectedCustomItemIndex(self) -> int: ...
        def selectedElement(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DGraph.ElementType: ...
        def selectedLabelIndex(self) -> int: ...
        def selectionMode(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DGraph.SelectionFlags: ...
        def setActiveInputHandler(self, inputHandler:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DInputHandler): ...
        def setActiveTheme(self, theme:PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme): ...
        def setAspectRatio(self, ratio:float): ...
        def setHorizontalAspectRatio(self, ratio:float): ...
        def setLocale(self, locale:PySide2.QtCore.QLocale): ...
        def setMargin(self, margin:float): ...
        def setMeasureFps(self, enable:bool): ...
        def setOptimizationHints(self, hints:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DGraph.OptimizationHints): ...
        def setOrthoProjection(self, enable:bool): ...
        def setPolar(self, enable:bool): ...
        def setRadialLabelOffset(self, offset:float): ...
        def setReflection(self, enable:bool): ...
        def setReflectivity(self, reflectivity:float): ...
        def setSelectionMode(self, mode:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DGraph.SelectionFlags): ...
        def setShadowQuality(self, quality:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DGraph.ShadowQuality): ...
        def shadowQuality(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DGraph.ShadowQuality: ...
        def shadowsSupported(self) -> bool: ...
        def themes(self) -> typing.List: ...
        def touchEvent(self, event:PySide2.QtGui.QTouchEvent): ...
        def wheelEvent(self, event:PySide2.QtGui.QWheelEvent): ...

    class QAbstract3DInputHandler(PySide2.QtCore.QObject):
        InputViewNone            : QtDataVisualization.QAbstract3DInputHandler = ... # 0x0
        InputViewOnPrimary       : QtDataVisualization.QAbstract3DInputHandler = ... # 0x1
        InputViewOnSecondary     : QtDataVisualization.QAbstract3DInputHandler = ... # 0x2

        class InputView(object):
            InputViewNone            : QtDataVisualization.QAbstract3DInputHandler.InputView = ... # 0x0
            InputViewOnPrimary       : QtDataVisualization.QAbstract3DInputHandler.InputView = ... # 0x1
            InputViewOnSecondary     : QtDataVisualization.QAbstract3DInputHandler.InputView = ... # 0x2

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def inputPosition(self) -> PySide2.QtCore.QPoint: ...
        def inputView(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DInputHandler.InputView: ...
        def mouseDoubleClickEvent(self, event:PySide2.QtGui.QMouseEvent): ...
        def mouseMoveEvent(self, event:PySide2.QtGui.QMouseEvent, mousePos:PySide2.QtCore.QPoint): ...
        def mousePressEvent(self, event:PySide2.QtGui.QMouseEvent, mousePos:PySide2.QtCore.QPoint): ...
        def mouseReleaseEvent(self, event:PySide2.QtGui.QMouseEvent, mousePos:PySide2.QtCore.QPoint): ...
        def prevDistance(self) -> int: ...
        def previousInputPos(self) -> PySide2.QtCore.QPoint: ...
        def scene(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DScene: ...
        def setInputPosition(self, position:PySide2.QtCore.QPoint): ...
        def setInputView(self, inputView:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DInputHandler.InputView): ...
        def setPrevDistance(self, distance:int): ...
        def setPreviousInputPos(self, position:PySide2.QtCore.QPoint): ...
        def setScene(self, scene:PySide2.QtDataVisualization.QtDataVisualization.Q3DScene): ...
        def touchEvent(self, event:PySide2.QtGui.QTouchEvent): ...
        def wheelEvent(self, event:PySide2.QtGui.QWheelEvent): ...

    class QAbstract3DSeries(PySide2.QtCore.QObject):
        MeshUserDefined          : QtDataVisualization.QAbstract3DSeries = ... # 0x0
        SeriesTypeNone           : QtDataVisualization.QAbstract3DSeries = ... # 0x0
        MeshBar                  : QtDataVisualization.QAbstract3DSeries = ... # 0x1
        SeriesTypeBar            : QtDataVisualization.QAbstract3DSeries = ... # 0x1
        MeshCube                 : QtDataVisualization.QAbstract3DSeries = ... # 0x2
        SeriesTypeScatter        : QtDataVisualization.QAbstract3DSeries = ... # 0x2
        MeshPyramid              : QtDataVisualization.QAbstract3DSeries = ... # 0x3
        MeshCone                 : QtDataVisualization.QAbstract3DSeries = ... # 0x4
        SeriesTypeSurface        : QtDataVisualization.QAbstract3DSeries = ... # 0x4
        MeshCylinder             : QtDataVisualization.QAbstract3DSeries = ... # 0x5
        MeshBevelBar             : QtDataVisualization.QAbstract3DSeries = ... # 0x6
        MeshBevelCube            : QtDataVisualization.QAbstract3DSeries = ... # 0x7
        MeshSphere               : QtDataVisualization.QAbstract3DSeries = ... # 0x8
        MeshMinimal              : QtDataVisualization.QAbstract3DSeries = ... # 0x9
        MeshArrow                : QtDataVisualization.QAbstract3DSeries = ... # 0xa
        MeshPoint                : QtDataVisualization.QAbstract3DSeries = ... # 0xb

        class Mesh(object):
            MeshUserDefined          : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x0
            MeshBar                  : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x1
            MeshCube                 : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x2
            MeshPyramid              : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x3
            MeshCone                 : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x4
            MeshCylinder             : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x5
            MeshBevelBar             : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x6
            MeshBevelCube            : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x7
            MeshSphere               : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x8
            MeshMinimal              : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0x9
            MeshArrow                : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0xa
            MeshPoint                : QtDataVisualization.QAbstract3DSeries.Mesh = ... # 0xb

        class SeriesType(object):
            SeriesTypeNone           : QtDataVisualization.QAbstract3DSeries.SeriesType = ... # 0x0
            SeriesTypeBar            : QtDataVisualization.QAbstract3DSeries.SeriesType = ... # 0x1
            SeriesTypeScatter        : QtDataVisualization.QAbstract3DSeries.SeriesType = ... # 0x2
            SeriesTypeSurface        : QtDataVisualization.QAbstract3DSeries.SeriesType = ... # 0x4
        def baseColor(self) -> PySide2.QtGui.QColor: ...
        def baseGradient(self) -> PySide2.QtGui.QLinearGradient: ...
        def colorStyle(self) -> PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme.ColorStyle: ...
        def isItemLabelVisible(self) -> bool: ...
        def isMeshSmooth(self) -> bool: ...
        def isVisible(self) -> bool: ...
        def itemLabel(self) -> str: ...
        def itemLabelFormat(self) -> str: ...
        def mesh(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DSeries.Mesh: ...
        def meshRotation(self) -> PySide2.QtGui.QQuaternion: ...
        def multiHighlightColor(self) -> PySide2.QtGui.QColor: ...
        def multiHighlightGradient(self) -> PySide2.QtGui.QLinearGradient: ...
        def name(self) -> str: ...
        def setBaseColor(self, color:PySide2.QtGui.QColor): ...
        def setBaseGradient(self, gradient:PySide2.QtGui.QLinearGradient): ...
        def setColorStyle(self, style:PySide2.QtDataVisualization.QtDataVisualization.Q3DTheme.ColorStyle): ...
        def setItemLabelFormat(self, format:str): ...
        def setItemLabelVisible(self, visible:bool): ...
        def setMesh(self, mesh:PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DSeries.Mesh): ...
        def setMeshAxisAndAngle(self, axis:PySide2.QtGui.QVector3D, angle:float): ...
        def setMeshRotation(self, rotation:PySide2.QtGui.QQuaternion): ...
        def setMeshSmooth(self, enable:bool): ...
        def setMultiHighlightColor(self, color:PySide2.QtGui.QColor): ...
        def setMultiHighlightGradient(self, gradient:PySide2.QtGui.QLinearGradient): ...
        def setName(self, name:str): ...
        def setSingleHighlightColor(self, color:PySide2.QtGui.QColor): ...
        def setSingleHighlightGradient(self, gradient:PySide2.QtGui.QLinearGradient): ...
        def setUserDefinedMesh(self, fileName:str): ...
        def setVisible(self, visible:bool): ...
        def singleHighlightColor(self) -> PySide2.QtGui.QColor: ...
        def singleHighlightGradient(self) -> PySide2.QtGui.QLinearGradient: ...
        def type(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstract3DSeries.SeriesType: ...
        def userDefinedMesh(self) -> str: ...

    class QAbstractDataProxy(PySide2.QtCore.QObject):
        DataTypeNone             : QtDataVisualization.QAbstractDataProxy = ... # 0x0
        DataTypeBar              : QtDataVisualization.QAbstractDataProxy = ... # 0x1
        DataTypeScatter          : QtDataVisualization.QAbstractDataProxy = ... # 0x2
        DataTypeSurface          : QtDataVisualization.QAbstractDataProxy = ... # 0x4

        class DataType(object):
            DataTypeNone             : QtDataVisualization.QAbstractDataProxy.DataType = ... # 0x0
            DataTypeBar              : QtDataVisualization.QAbstractDataProxy.DataType = ... # 0x1
            DataTypeScatter          : QtDataVisualization.QAbstractDataProxy.DataType = ... # 0x2
            DataTypeSurface          : QtDataVisualization.QAbstractDataProxy.DataType = ... # 0x4
        def type(self) -> PySide2.QtDataVisualization.QtDataVisualization.QAbstractDataProxy.DataType: ...

    class QBar3DSeries(PySide2.QtDataVisualization.QAbstract3DSeries):

        @typing.overload
        def __init__(self, dataProxy:PySide2.QtDataVisualization.QtDataVisualization.QBarDataProxy, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def dataProxy(self) -> PySide2.QtDataVisualization.QtDataVisualization.QBarDataProxy: ...
        @staticmethod
        def invalidSelectionPosition() -> PySide2.QtCore.QPoint: ...
        def meshAngle(self) -> float: ...
        def selectedBar(self) -> PySide2.QtCore.QPoint: ...
        def setDataProxy(self, proxy:PySide2.QtDataVisualization.QtDataVisualization.QBarDataProxy): ...
        def setMeshAngle(self, angle:float): ...
        def setSelectedBar(self, position:PySide2.QtCore.QPoint): ...

    class QBarDataItem(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, other:PySide2.QtDataVisualization.QtDataVisualization.QBarDataItem): ...
        @typing.overload
        def __init__(self, value:float): ...
        @typing.overload
        def __init__(self, value:float, angle:float): ...

        def __copy__(self): ...
        def createExtraData(self): ...
        def rotation(self) -> float: ...
        def setRotation(self, angle:float): ...
        def setValue(self, val:float): ...
        def value(self) -> float: ...

    class QBarDataProxy(PySide2.QtDataVisualization.QAbstractDataProxy):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        @typing.overload
        def addRow(self, row:typing.List) -> int: ...
        @typing.overload
        def addRow(self, row:typing.List, label:str) -> int: ...
        @typing.overload
        def addRows(self, rows:typing.List) -> int: ...
        @typing.overload
        def addRows(self, rows:typing.List, labels:typing.Sequence) -> int: ...
        def array(self) -> typing.List: ...
        def columnLabels(self) -> typing.List: ...
        @typing.overload
        def insertRow(self, rowIndex:int, row:typing.List): ...
        @typing.overload
        def insertRow(self, rowIndex:int, row:typing.List, label:str): ...
        @typing.overload
        def insertRows(self, rowIndex:int, rows:typing.List): ...
        @typing.overload
        def insertRows(self, rowIndex:int, rows:typing.List, labels:typing.Sequence): ...
        @typing.overload
        def itemAt(self, position:PySide2.QtCore.QPoint) -> PySide2.QtDataVisualization.QtDataVisualization.QBarDataItem: ...
        @typing.overload
        def itemAt(self, rowIndex:int, columnIndex:int) -> PySide2.QtDataVisualization.QtDataVisualization.QBarDataItem: ...
        def removeRows(self, rowIndex:int, removeCount:int, removeLabels:bool=...): ...
        @typing.overload
        def resetArray(self): ...
        @typing.overload
        def resetArray(self, newArray:typing.List): ...
        @typing.overload
        def resetArray(self, newArray:typing.List, rowLabels:typing.Sequence, columnLabels:typing.Sequence): ...
        def rowAt(self, rowIndex:int) -> typing.List: ...
        def rowCount(self) -> int: ...
        def rowLabels(self) -> typing.List: ...
        def series(self) -> PySide2.QtDataVisualization.QtDataVisualization.QBar3DSeries: ...
        def setColumnLabels(self, labels:typing.Sequence): ...
        @typing.overload
        def setItem(self, position:PySide2.QtCore.QPoint, item:PySide2.QtDataVisualization.QtDataVisualization.QBarDataItem): ...
        @typing.overload
        def setItem(self, rowIndex:int, columnIndex:int, item:PySide2.QtDataVisualization.QtDataVisualization.QBarDataItem): ...
        @typing.overload
        def setRow(self, rowIndex:int, row:typing.List): ...
        @typing.overload
        def setRow(self, rowIndex:int, row:typing.List, label:str): ...
        def setRowLabels(self, labels:typing.Sequence): ...
        @typing.overload
        def setRows(self, rowIndex:int, rows:typing.List): ...
        @typing.overload
        def setRows(self, rowIndex:int, rows:typing.List, labels:typing.Sequence): ...

    class QCategory3DAxis(PySide2.QtDataVisualization.QAbstract3DAxis):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def labels(self) -> typing.List: ...
        def setLabels(self, labels:typing.Sequence): ...

    class QCustom3DItem(PySide2.QtCore.QObject):

        @typing.overload
        def __init__(self, meshFile:str, position:PySide2.QtGui.QVector3D, scaling:PySide2.QtGui.QVector3D, rotation:PySide2.QtGui.QQuaternion, texture:PySide2.QtGui.QImage, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def isPositionAbsolute(self) -> bool: ...
        def isScalingAbsolute(self) -> bool: ...
        def isShadowCasting(self) -> bool: ...
        def isVisible(self) -> bool: ...
        def meshFile(self) -> str: ...
        def position(self) -> PySide2.QtGui.QVector3D: ...
        def rotation(self) -> PySide2.QtGui.QQuaternion: ...
        def scaling(self) -> PySide2.QtGui.QVector3D: ...
        def setMeshFile(self, meshFile:str): ...
        def setPosition(self, position:PySide2.QtGui.QVector3D): ...
        def setPositionAbsolute(self, positionAbsolute:bool): ...
        def setRotation(self, rotation:PySide2.QtGui.QQuaternion): ...
        def setRotationAxisAndAngle(self, axis:PySide2.QtGui.QVector3D, angle:float): ...
        def setScaling(self, scaling:PySide2.QtGui.QVector3D): ...
        def setScalingAbsolute(self, scalingAbsolute:bool): ...
        def setShadowCasting(self, enabled:bool): ...
        def setTextureFile(self, textureFile:str): ...
        def setTextureImage(self, textureImage:PySide2.QtGui.QImage): ...
        def setVisible(self, visible:bool): ...
        def textureFile(self) -> str: ...

    class QCustom3DLabel(PySide2.QtDataVisualization.QCustom3DItem):

        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, text:str, font:PySide2.QtGui.QFont, position:PySide2.QtGui.QVector3D, scaling:PySide2.QtGui.QVector3D, rotation:PySide2.QtGui.QQuaternion, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def backgroundColor(self) -> PySide2.QtGui.QColor: ...
        def font(self) -> PySide2.QtGui.QFont: ...
        def isBackgroundEnabled(self) -> bool: ...
        def isBorderEnabled(self) -> bool: ...
        def isFacingCamera(self) -> bool: ...
        def setBackgroundColor(self, color:PySide2.QtGui.QColor): ...
        def setBackgroundEnabled(self, enabled:bool): ...
        def setBorderEnabled(self, enabled:bool): ...
        def setFacingCamera(self, enabled:bool): ...
        def setFont(self, font:PySide2.QtGui.QFont): ...
        def setText(self, text:str): ...
        def setTextColor(self, color:PySide2.QtGui.QColor): ...
        def text(self) -> str: ...
        def textColor(self) -> PySide2.QtGui.QColor: ...

    class QCustom3DVolume(PySide2.QtDataVisualization.QCustom3DItem):

        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, position:PySide2.QtGui.QVector3D, scaling:PySide2.QtGui.QVector3D, rotation:PySide2.QtGui.QQuaternion, textureWidth:int, textureHeight:int, textureDepth:int, textureData:typing.List, textureFormat:PySide2.QtGui.QImage.Format, colorTable:typing.List, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def alphaMultiplier(self) -> float: ...
        def colorTable(self) -> typing.List: ...
        def createTextureData(self, images:typing.List) -> typing.List: ...
        def drawSliceFrames(self) -> bool: ...
        def drawSlices(self) -> bool: ...
        def preserveOpacity(self) -> bool: ...
        def renderSlice(self, axis:PySide2.QtCore.Qt.Axis, index:int) -> PySide2.QtGui.QImage: ...
        def setAlphaMultiplier(self, mult:float): ...
        def setColorTable(self, colors:typing.List): ...
        def setDrawSliceFrames(self, enable:bool): ...
        def setDrawSlices(self, enable:bool): ...
        def setPreserveOpacity(self, enable:bool): ...
        def setSliceFrameColor(self, color:PySide2.QtGui.QColor): ...
        def setSliceFrameGaps(self, values:PySide2.QtGui.QVector3D): ...
        def setSliceFrameThicknesses(self, values:PySide2.QtGui.QVector3D): ...
        def setSliceFrameWidths(self, values:PySide2.QtGui.QVector3D): ...
        def setSliceIndexX(self, value:int): ...
        def setSliceIndexY(self, value:int): ...
        def setSliceIndexZ(self, value:int): ...
        def setSliceIndices(self, x:int, y:int, z:int): ...
        @typing.overload
        def setSubTextureData(self, axis:PySide2.QtCore.Qt.Axis, index:int, data:bytes): ...
        @typing.overload
        def setSubTextureData(self, axis:PySide2.QtCore.Qt.Axis, index:int, image:PySide2.QtGui.QImage): ...
        def setTextureData(self, data:typing.List): ...
        def setTextureDepth(self, value:int): ...
        def setTextureDimensions(self, width:int, height:int, depth:int): ...
        def setTextureFormat(self, format:PySide2.QtGui.QImage.Format): ...
        def setTextureHeight(self, value:int): ...
        def setTextureWidth(self, value:int): ...
        def setUseHighDefShader(self, enable:bool): ...
        def sliceFrameColor(self) -> PySide2.QtGui.QColor: ...
        def sliceFrameGaps(self) -> PySide2.QtGui.QVector3D: ...
        def sliceFrameThicknesses(self) -> PySide2.QtGui.QVector3D: ...
        def sliceFrameWidths(self) -> PySide2.QtGui.QVector3D: ...
        def sliceIndexX(self) -> int: ...
        def sliceIndexY(self) -> int: ...
        def sliceIndexZ(self) -> int: ...
        def textureData(self) -> typing.List: ...
        def textureDataWidth(self) -> int: ...
        def textureDepth(self) -> int: ...
        def textureFormat(self) -> PySide2.QtGui.QImage.Format: ...
        def textureHeight(self) -> int: ...
        def textureWidth(self) -> int: ...
        def useHighDefShader(self) -> bool: ...

    class QHeightMapSurfaceDataProxy(PySide2.QtDataVisualization.QSurfaceDataProxy):

        @typing.overload
        def __init__(self, filename:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, image:PySide2.QtGui.QImage, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def heightMap(self) -> PySide2.QtGui.QImage: ...
        def heightMapFile(self) -> str: ...
        def maxXValue(self) -> float: ...
        def maxZValue(self) -> float: ...
        def minXValue(self) -> float: ...
        def minZValue(self) -> float: ...
        def setHeightMap(self, image:PySide2.QtGui.QImage): ...
        def setHeightMapFile(self, filename:str): ...
        def setMaxXValue(self, max:float): ...
        def setMaxZValue(self, max:float): ...
        def setMinXValue(self, min:float): ...
        def setMinZValue(self, min:float): ...
        def setValueRanges(self, minX:float, maxX:float, minZ:float, maxZ:float): ...

    class QItemModelBarDataProxy(PySide2.QtDataVisualization.QBarDataProxy):
        MMBFirst                 : QtDataVisualization.QItemModelBarDataProxy = ... # 0x0
        MMBLast                  : QtDataVisualization.QItemModelBarDataProxy = ... # 0x1
        MMBAverage               : QtDataVisualization.QItemModelBarDataProxy = ... # 0x2
        MMBCumulative            : QtDataVisualization.QItemModelBarDataProxy = ... # 0x3

        class MultiMatchBehavior(object):
            MMBFirst                 : QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x0
            MMBLast                  : QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x1
            MMBAverage               : QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x2
            MMBCumulative            : QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior = ... # 0x3

        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, valueRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, valueRole:str, rotationRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, valueRole:str, rotationRole:str, rowCategories:typing.Sequence, columnCategories:typing.Sequence, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, valueRole:str, rowCategories:typing.Sequence, columnCategories:typing.Sequence, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, valueRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def autoColumnCategories(self) -> bool: ...
        def autoRowCategories(self) -> bool: ...
        def columnCategories(self) -> typing.List: ...
        def columnCategoryIndex(self, category:str) -> int: ...
        def columnRole(self) -> str: ...
        def columnRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def columnRoleReplace(self) -> str: ...
        def itemModel(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def multiMatchBehavior(self) -> PySide2.QtDataVisualization.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior: ...
        def remap(self, rowRole:str, columnRole:str, valueRole:str, rotationRole:str, rowCategories:typing.Sequence, columnCategories:typing.Sequence): ...
        def rotationRole(self) -> str: ...
        def rotationRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def rotationRoleReplace(self) -> str: ...
        def rowCategories(self) -> typing.List: ...
        def rowCategoryIndex(self, category:str) -> int: ...
        def rowRole(self) -> str: ...
        def rowRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def rowRoleReplace(self) -> str: ...
        def setAutoColumnCategories(self, enable:bool): ...
        def setAutoRowCategories(self, enable:bool): ...
        def setColumnCategories(self, categories:typing.Sequence): ...
        def setColumnRole(self, role:str): ...
        def setColumnRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setColumnRoleReplace(self, replace:str): ...
        def setItemModel(self, itemModel:PySide2.QtCore.QAbstractItemModel): ...
        def setMultiMatchBehavior(self, behavior:PySide2.QtDataVisualization.QtDataVisualization.QItemModelBarDataProxy.MultiMatchBehavior): ...
        def setRotationRole(self, role:str): ...
        def setRotationRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setRotationRoleReplace(self, replace:str): ...
        def setRowCategories(self, categories:typing.Sequence): ...
        def setRowRole(self, role:str): ...
        def setRowRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setRowRoleReplace(self, replace:str): ...
        def setUseModelCategories(self, enable:bool): ...
        def setValueRole(self, role:str): ...
        def setValueRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setValueRoleReplace(self, replace:str): ...
        def useModelCategories(self) -> bool: ...
        def valueRole(self) -> str: ...
        def valueRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def valueRoleReplace(self) -> str: ...

    class QItemModelScatterDataProxy(PySide2.QtDataVisualization.QScatterDataProxy):

        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, xPosRole:str, yPosRole:str, zPosRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, xPosRole:str, yPosRole:str, zPosRole:str, rotationRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def itemModel(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def remap(self, xPosRole:str, yPosRole:str, zPosRole:str, rotationRole:str): ...
        def rotationRole(self) -> str: ...
        def rotationRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def rotationRoleReplace(self) -> str: ...
        def setItemModel(self, itemModel:PySide2.QtCore.QAbstractItemModel): ...
        def setRotationRole(self, role:str): ...
        def setRotationRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setRotationRoleReplace(self, replace:str): ...
        def setXPosRole(self, role:str): ...
        def setXPosRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setXPosRoleReplace(self, replace:str): ...
        def setYPosRole(self, role:str): ...
        def setYPosRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setYPosRoleReplace(self, replace:str): ...
        def setZPosRole(self, role:str): ...
        def setZPosRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setZPosRoleReplace(self, replace:str): ...
        def xPosRole(self) -> str: ...
        def xPosRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def xPosRoleReplace(self) -> str: ...
        def yPosRole(self) -> str: ...
        def yPosRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def yPosRoleReplace(self) -> str: ...
        def zPosRole(self) -> str: ...
        def zPosRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def zPosRoleReplace(self) -> str: ...

    class QItemModelSurfaceDataProxy(PySide2.QtDataVisualization.QSurfaceDataProxy):
        MMBFirst                 : QtDataVisualization.QItemModelSurfaceDataProxy = ... # 0x0
        MMBLast                  : QtDataVisualization.QItemModelSurfaceDataProxy = ... # 0x1
        MMBAverage               : QtDataVisualization.QItemModelSurfaceDataProxy = ... # 0x2
        MMBCumulativeY           : QtDataVisualization.QItemModelSurfaceDataProxy = ... # 0x3

        class MultiMatchBehavior(object):
            MMBFirst                 : QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x0
            MMBLast                  : QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x1
            MMBAverage               : QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x2
            MMBCumulativeY           : QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior = ... # 0x3

        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, xPosRole:str, yPosRole:str, zPosRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, xPosRole:str, yPosRole:str, zPosRole:str, rowCategories:typing.Sequence, columnCategories:typing.Sequence, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, yPosRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, rowRole:str, columnRole:str, yPosRole:str, rowCategories:typing.Sequence, columnCategories:typing.Sequence, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, itemModel:PySide2.QtCore.QAbstractItemModel, yPosRole:str, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def autoColumnCategories(self) -> bool: ...
        def autoRowCategories(self) -> bool: ...
        def columnCategories(self) -> typing.List: ...
        def columnCategoryIndex(self, category:str) -> int: ...
        def columnRole(self) -> str: ...
        def columnRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def columnRoleReplace(self) -> str: ...
        def itemModel(self) -> PySide2.QtCore.QAbstractItemModel: ...
        def multiMatchBehavior(self) -> PySide2.QtDataVisualization.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior: ...
        def remap(self, rowRole:str, columnRole:str, xPosRole:str, yPosRole:str, zPosRole:str, rowCategories:typing.Sequence, columnCategories:typing.Sequence): ...
        def rowCategories(self) -> typing.List: ...
        def rowCategoryIndex(self, category:str) -> int: ...
        def rowRole(self) -> str: ...
        def rowRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def rowRoleReplace(self) -> str: ...
        def setAutoColumnCategories(self, enable:bool): ...
        def setAutoRowCategories(self, enable:bool): ...
        def setColumnCategories(self, categories:typing.Sequence): ...
        def setColumnRole(self, role:str): ...
        def setColumnRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setColumnRoleReplace(self, replace:str): ...
        def setItemModel(self, itemModel:PySide2.QtCore.QAbstractItemModel): ...
        def setMultiMatchBehavior(self, behavior:PySide2.QtDataVisualization.QtDataVisualization.QItemModelSurfaceDataProxy.MultiMatchBehavior): ...
        def setRowCategories(self, categories:typing.Sequence): ...
        def setRowRole(self, role:str): ...
        def setRowRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setRowRoleReplace(self, replace:str): ...
        def setUseModelCategories(self, enable:bool): ...
        def setXPosRole(self, role:str): ...
        def setXPosRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setXPosRoleReplace(self, replace:str): ...
        def setYPosRole(self, role:str): ...
        def setYPosRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setYPosRoleReplace(self, replace:str): ...
        def setZPosRole(self, role:str): ...
        def setZPosRolePattern(self, pattern:PySide2.QtCore.QRegExp): ...
        def setZPosRoleReplace(self, replace:str): ...
        def useModelCategories(self) -> bool: ...
        def xPosRole(self) -> str: ...
        def xPosRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def xPosRoleReplace(self) -> str: ...
        def yPosRole(self) -> str: ...
        def yPosRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def yPosRoleReplace(self) -> str: ...
        def zPosRole(self) -> str: ...
        def zPosRolePattern(self) -> PySide2.QtCore.QRegExp: ...
        def zPosRoleReplace(self) -> str: ...

    class QLogValue3DAxisFormatter(PySide2.QtDataVisualization.QValue3DAxisFormatter):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def autoSubGrid(self) -> bool: ...
        def base(self) -> float: ...
        def createNewInstance(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxisFormatter: ...
        def populateCopy(self, copy:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxisFormatter): ...
        def positionAt(self, value:float) -> float: ...
        def recalculate(self): ...
        def setAutoSubGrid(self, enabled:bool): ...
        def setBase(self, base:float): ...
        def setShowEdgeLabels(self, enabled:bool): ...
        def showEdgeLabels(self) -> bool: ...
        def valueAt(self, position:float) -> float: ...

    class QScatter3DSeries(PySide2.QtDataVisualization.QAbstract3DSeries):

        @typing.overload
        def __init__(self, dataProxy:PySide2.QtDataVisualization.QtDataVisualization.QScatterDataProxy, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def dataProxy(self) -> PySide2.QtDataVisualization.QtDataVisualization.QScatterDataProxy: ...
        @staticmethod
        def invalidSelectionIndex() -> int: ...
        def itemSize(self) -> float: ...
        def selectedItem(self) -> int: ...
        def setDataProxy(self, proxy:PySide2.QtDataVisualization.QtDataVisualization.QScatterDataProxy): ...
        def setItemSize(self, size:float): ...
        def setSelectedItem(self, index:int): ...

    class QScatterDataItem(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, other:PySide2.QtDataVisualization.QtDataVisualization.QScatterDataItem): ...
        @typing.overload
        def __init__(self, position:PySide2.QtGui.QVector3D): ...
        @typing.overload
        def __init__(self, position:PySide2.QtGui.QVector3D, rotation:PySide2.QtGui.QQuaternion): ...

        def __copy__(self): ...
        def createExtraData(self): ...
        def position(self) -> PySide2.QtGui.QVector3D: ...
        def rotation(self) -> PySide2.QtGui.QQuaternion: ...
        def setPosition(self, pos:PySide2.QtGui.QVector3D): ...
        def setRotation(self, rot:PySide2.QtGui.QQuaternion): ...
        def setX(self, value:float): ...
        def setY(self, value:float): ...
        def setZ(self, value:float): ...
        def x(self) -> float: ...
        def y(self) -> float: ...
        def z(self) -> float: ...

    class QScatterDataProxy(PySide2.QtDataVisualization.QAbstractDataProxy):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def addItem(self, item:PySide2.QtDataVisualization.QtDataVisualization.QScatterDataItem) -> int: ...
        def addItems(self, items:typing.List) -> int: ...
        def array(self) -> typing.List: ...
        def insertItem(self, index:int, item:PySide2.QtDataVisualization.QtDataVisualization.QScatterDataItem): ...
        def insertItems(self, index:int, items:typing.List): ...
        def itemAt(self, index:int) -> PySide2.QtDataVisualization.QtDataVisualization.QScatterDataItem: ...
        def itemCount(self) -> int: ...
        def removeItems(self, index:int, removeCount:int): ...
        def resetArray(self, newArray:typing.List): ...
        def series(self) -> PySide2.QtDataVisualization.QtDataVisualization.QScatter3DSeries: ...
        def setItem(self, index:int, item:PySide2.QtDataVisualization.QtDataVisualization.QScatterDataItem): ...
        def setItems(self, index:int, items:typing.List): ...

    class QSurface3DSeries(PySide2.QtDataVisualization.QAbstract3DSeries):
        DrawWireframe            : QtDataVisualization.QSurface3DSeries = ... # 0x1
        DrawSurface              : QtDataVisualization.QSurface3DSeries = ... # 0x2
        DrawSurfaceAndWireframe  : QtDataVisualization.QSurface3DSeries = ... # 0x3

        class DrawFlag(object):
            DrawWireframe            : QtDataVisualization.QSurface3DSeries.DrawFlag = ... # 0x1
            DrawSurface              : QtDataVisualization.QSurface3DSeries.DrawFlag = ... # 0x2
            DrawSurfaceAndWireframe  : QtDataVisualization.QSurface3DSeries.DrawFlag = ... # 0x3

        class DrawFlags(object): ...

        @typing.overload
        def __init__(self, dataProxy:PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataProxy, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...
        @typing.overload
        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def dataProxy(self) -> PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataProxy: ...
        def drawMode(self) -> PySide2.QtDataVisualization.QtDataVisualization.QSurface3DSeries.DrawFlags: ...
        @staticmethod
        def invalidSelectionPosition() -> PySide2.QtCore.QPoint: ...
        def isFlatShadingEnabled(self) -> bool: ...
        def isFlatShadingSupported(self) -> bool: ...
        def selectedPoint(self) -> PySide2.QtCore.QPoint: ...
        def setDataProxy(self, proxy:PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataProxy): ...
        def setDrawMode(self, mode:PySide2.QtDataVisualization.QtDataVisualization.QSurface3DSeries.DrawFlags): ...
        def setFlatShadingEnabled(self, enabled:bool): ...
        def setSelectedPoint(self, position:PySide2.QtCore.QPoint): ...
        def setTexture(self, texture:PySide2.QtGui.QImage): ...
        def setTextureFile(self, filename:str): ...
        def texture(self) -> PySide2.QtGui.QImage: ...
        def textureFile(self) -> str: ...

    class QSurfaceDataItem(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, other:PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataItem): ...
        @typing.overload
        def __init__(self, position:PySide2.QtGui.QVector3D): ...

        def __copy__(self): ...
        def createExtraData(self): ...
        def position(self) -> PySide2.QtGui.QVector3D: ...
        def setPosition(self, pos:PySide2.QtGui.QVector3D): ...
        def setX(self, value:float): ...
        def setY(self, value:float): ...
        def setZ(self, value:float): ...
        def x(self) -> float: ...
        def y(self) -> float: ...
        def z(self) -> float: ...

    class QSurfaceDataProxy(PySide2.QtDataVisualization.QAbstractDataProxy):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def addRow(self, row:typing.List) -> int: ...
        def addRows(self, rows:typing.List) -> int: ...
        def array(self) -> typing.List: ...
        def columnCount(self) -> int: ...
        def insertRow(self, rowIndex:int, row:typing.List): ...
        def insertRows(self, rowIndex:int, rows:typing.List): ...
        @typing.overload
        def itemAt(self, position:PySide2.QtCore.QPoint) -> PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataItem: ...
        @typing.overload
        def itemAt(self, rowIndex:int, columnIndex:int) -> PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataItem: ...
        def removeRows(self, rowIndex:int, removeCount:int): ...
        def resetArray(self, newArray:typing.List): ...
        def rowCount(self) -> int: ...
        def series(self) -> PySide2.QtDataVisualization.QtDataVisualization.QSurface3DSeries: ...
        @typing.overload
        def setItem(self, position:PySide2.QtCore.QPoint, item:PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataItem): ...
        @typing.overload
        def setItem(self, rowIndex:int, columnIndex:int, item:PySide2.QtDataVisualization.QtDataVisualization.QSurfaceDataItem): ...
        def setRow(self, rowIndex:int, row:typing.List): ...
        def setRows(self, rowIndex:int, rows:typing.List): ...

    class QTouch3DInputHandler(PySide2.QtDataVisualization.Q3DInputHandler):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def touchEvent(self, event:PySide2.QtGui.QTouchEvent): ...

    class QValue3DAxis(PySide2.QtDataVisualization.QAbstract3DAxis):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def formatter(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxisFormatter: ...
        def labelFormat(self) -> str: ...
        def reversed(self) -> bool: ...
        def segmentCount(self) -> int: ...
        def setFormatter(self, formatter:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxisFormatter): ...
        def setLabelFormat(self, format:str): ...
        def setReversed(self, enable:bool): ...
        def setSegmentCount(self, count:int): ...
        def setSubSegmentCount(self, count:int): ...
        def subSegmentCount(self) -> int: ...

    class QValue3DAxisFormatter(PySide2.QtCore.QObject):

        def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

        def allowNegatives(self) -> bool: ...
        def allowZero(self) -> bool: ...
        def axis(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxis: ...
        def createNewInstance(self) -> PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxisFormatter: ...
        def gridPositions(self) -> typing.List: ...
        def labelPositions(self) -> typing.List: ...
        def labelStrings(self) -> typing.List: ...
        def locale(self) -> PySide2.QtCore.QLocale: ...
        def markDirty(self, labelsChange:bool=...): ...
        def populateCopy(self, copy:PySide2.QtDataVisualization.QtDataVisualization.QValue3DAxisFormatter): ...
        def positionAt(self, value:float) -> float: ...
        def recalculate(self): ...
        def setAllowNegatives(self, allow:bool): ...
        def setAllowZero(self, allow:bool): ...
        def setLocale(self, locale:PySide2.QtCore.QLocale): ...
        def stringForValue(self, value:float, format:str) -> str: ...
        def subGridPositions(self) -> typing.List: ...
        def valueAt(self, position:float) -> float: ...
    @typing.overload
    @staticmethod
    def qDefaultSurfaceFormat(antialias:bool) -> PySide2.QtGui.QSurfaceFormat: ...
    @typing.overload
    @staticmethod
    def qDefaultSurfaceFormat(antialias:bool=...) -> PySide2.QtGui.QSurfaceFormat: ...

# eof
