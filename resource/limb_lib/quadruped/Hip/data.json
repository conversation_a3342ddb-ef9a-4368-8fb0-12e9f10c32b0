{"Hip_M_Limb": {"shape_data": {"localPositionX": 0.0, "localPositionY": 0.0, "localPositionZ": 0.0, "localScaleX": 3.0, "localScaleY": 3.0, "localScaleZ": 3.0, "localRotateX": 0.0, "localRotateY": 0.0, "localRotateZ": 90.0, "colorR": 1.0, "colorG": 1.0, "colorB": 0.0, "textPositionX": 0.0, "textPositionY": 0.0, "textPositionZ": 0.0, "label": "", "lsRig": "", "shapeType": 9, "controllerType": 0, "xrayMode": false, "drawIt": true}, "attributes": [{"name": "group_exts", "value": "['PLC', 'SDK', 'OFFSET']"}, {"name": "limb_type", "value": "lsr:BipedHip"}, {"name": "enabled", "value": true}, {"name": "skeleton_type", "value": "single_joint"}, {"name": "input_skeleton", "value": "['Hip_M_RIGJNT']"}, {"name": "part", "value": "hip"}, {"name": "mirror", "value": false}, {"name": "side", "value": 2}], "creation": {"input_joint_data": {"start_joint": "*************-2AD1-4539-428190BD2CBB"}, "name": "<PERSON>_<PERSON>_Limb", "limb_type": "lsr:BipedHip", "limb_data": {"group_exts": "['PLC', 'SDK', 'OFFSET']", "enabled": true, "skeleton_type": "single_joint", "input_skeleton": "['Hip_M_RIGJNT']", "part": "hip", "mirror": false, "side": 2}, "worldMatrix": [0.0, 1.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 10.0, 0.0, 1.0], "uuid": "A5FCC75E-43F5-D6E0-C110-AA8EE307FDCA"}, "type": "LSRLimbTransform", "name": "<PERSON>_<PERSON>_Limb"}}