# This Python file uses the following encoding: utf-8
#############################################################################
##
## Copyright (C) 2020 The Qt Company Ltd.
## Contact: https://www.qt.io/licensing/
##
## This file is part of Qt for Python.
##
## $QT_BEGIN_LICENSE:LGPL$
## Commercial License Usage
## Licensees holding valid commercial Qt licenses may use this file in
## accordance with the commercial license agreement provided with the
## Software or, alternatively, in accordance with the terms contained in
## a written agreement between you and The Qt Company. For licensing terms
## and conditions see https://www.qt.io/terms-conditions. For further
## information use the contact form at https://www.qt.io/contact-us.
##
## GNU Lesser General Public License Usage
## Alternatively, this file may be used under the terms of the GNU Lesser
## General Public License version 3 as published by the Free Software
## Foundation and appearing in the file LICENSE.LGPL3 included in the
## packaging of this file. Please review the following information to
## ensure the GNU Lesser General Public License version 3 requirements
## will be met: https://www.gnu.org/licenses/lgpl-3.0.html.
##
## GNU General Public License Usage
## Alternatively, this file may be used under the terms of the GNU
## General Public License version 2.0 or (at your option) the GNU General
## Public license version 3 or any later version approved by the KDE Free
## Qt Foundation. The licenses are as published by the Free Software
## Foundation and appearing in the file LICENSE.GPL2 and LICENSE.GPL3
## included in the packaging of this file. Please review the following
## information to ensure the GNU General Public License requirements will
## be met: https://www.gnu.org/licenses/gpl-2.0.html and
## https://www.gnu.org/licenses/gpl-3.0.html.
##
## $QT_END_LICENSE$
##
#############################################################################

"""
This file contains the exact signatures for all functions in module
PySide2.QtQuick, except for defaults which are replaced by "...".
"""

# Module PySide2.QtQuick
import PySide2
try:
    import typing
except ImportError:
    from PySide2.support.signature import typing
from PySide2.support.signature.mapping import (
    Virtual, Missing, Invalid, Default, Instance)

class Object(object): pass

import shiboken2 as Shiboken
Shiboken.Object = Object

import PySide2.QtCore
import PySide2.QtGui
import PySide2.QtQml
import PySide2.QtQuick


class QQuickAsyncImageProvider(PySide2.QtQuick.QQuickImageProvider):

    def __init__(self): ...

    def requestImageResponse(self, id:str, requestedSize:PySide2.QtCore.QSize) -> PySide2.QtQuick.QQuickImageResponse: ...


class QQuickFramebufferObject(PySide2.QtQuick.QQuickItem):

    class Renderer(Shiboken.Object):

        def __init__(self): ...

        def createFramebufferObject(self, size:PySide2.QtCore.QSize) -> PySide2.QtGui.QOpenGLFramebufferObject: ...
        def framebufferObject(self) -> PySide2.QtGui.QOpenGLFramebufferObject: ...
        def invalidateFramebufferObject(self): ...
        def render(self): ...
        def synchronize(self, arg__1:PySide2.QtQuick.QQuickFramebufferObject): ...
        def update(self): ...

    def __init__(self, parent:typing.Optional[PySide2.QtQuick.QQuickItem]=...): ...

    def createRenderer(self) -> PySide2.QtQuick.QQuickFramebufferObject.Renderer: ...
    def geometryChanged(self, newGeometry:PySide2.QtCore.QRectF, oldGeometry:PySide2.QtCore.QRectF): ...
    def isTextureProvider(self) -> bool: ...
    def mirrorVertically(self) -> bool: ...
    def releaseResources(self): ...
    def setMirrorVertically(self, enable:bool): ...
    def setTextureFollowsItemSize(self, follows:bool): ...
    def textureFollowsItemSize(self) -> bool: ...
    def textureProvider(self) -> PySide2.QtQuick.QSGTextureProvider: ...
    def updatePaintNode(self, arg__1:PySide2.QtQuick.QSGNode, arg__2:PySide2.QtQuick.QQuickItem.UpdatePaintNodeData) -> PySide2.QtQuick.QSGNode: ...


class QQuickImageProvider(PySide2.QtQml.QQmlImageProviderBase):

    def __init__(self, type:PySide2.QtQml.QQmlImageProviderBase.ImageType, flags:PySide2.QtQml.QQmlImageProviderBase.Flags=...): ...

    def flags(self) -> PySide2.QtQml.QQmlImageProviderBase.Flags: ...
    def imageType(self) -> PySide2.QtQml.QQmlImageProviderBase.ImageType: ...
    def requestImage(self, id:str, size:PySide2.QtCore.QSize, requestedSize:PySide2.QtCore.QSize) -> PySide2.QtGui.QImage: ...
    def requestPixmap(self, id:str, size:PySide2.QtCore.QSize, requestedSize:PySide2.QtCore.QSize) -> PySide2.QtGui.QPixmap: ...
    def requestTexture(self, id:str, size:PySide2.QtCore.QSize, requestedSize:PySide2.QtCore.QSize) -> PySide2.QtQuick.QQuickTextureFactory: ...


class QQuickImageResponse(PySide2.QtCore.QObject):

    def __init__(self): ...

    def cancel(self): ...
    def errorString(self) -> str: ...
    def textureFactory(self) -> PySide2.QtQuick.QQuickTextureFactory: ...


class QQuickItem(PySide2.QtCore.QObject, PySide2.QtQml.QQmlParserStatus):
    ItemChildAddedChange     : QQuickItem = ... # 0x0
    TopLeft                  : QQuickItem = ... # 0x0
    ItemChildRemovedChange   : QQuickItem = ... # 0x1
    ItemClipsChildrenToShape : QQuickItem = ... # 0x1
    Top                      : QQuickItem = ... # 0x1
    ItemAcceptsInputMethod   : QQuickItem = ... # 0x2
    ItemSceneChange          : QQuickItem = ... # 0x2
    TopRight                 : QQuickItem = ... # 0x2
    ItemVisibleHasChanged    : QQuickItem = ... # 0x3
    Left                     : QQuickItem = ... # 0x3
    Center                   : QQuickItem = ... # 0x4
    ItemIsFocusScope         : QQuickItem = ... # 0x4
    ItemParentHasChanged     : QQuickItem = ... # 0x4
    ItemOpacityHasChanged    : QQuickItem = ... # 0x5
    Right                    : QQuickItem = ... # 0x5
    BottomLeft               : QQuickItem = ... # 0x6
    ItemActiveFocusHasChanged: QQuickItem = ... # 0x6
    Bottom                   : QQuickItem = ... # 0x7
    ItemRotationHasChanged   : QQuickItem = ... # 0x7
    BottomRight              : QQuickItem = ... # 0x8
    ItemAntialiasingHasChanged: QQuickItem = ... # 0x8
    ItemHasContents          : QQuickItem = ... # 0x8
    ItemDevicePixelRatioHasChanged: QQuickItem = ... # 0x9
    ItemEnabledHasChanged    : QQuickItem = ... # 0xa
    ItemAcceptsDrops         : QQuickItem = ... # 0x10

    class Flag(object):
        ItemClipsChildrenToShape : QQuickItem.Flag = ... # 0x1
        ItemAcceptsInputMethod   : QQuickItem.Flag = ... # 0x2
        ItemIsFocusScope         : QQuickItem.Flag = ... # 0x4
        ItemHasContents          : QQuickItem.Flag = ... # 0x8
        ItemAcceptsDrops         : QQuickItem.Flag = ... # 0x10

    class Flags(object): ...

    class ItemChange(object):
        ItemChildAddedChange     : QQuickItem.ItemChange = ... # 0x0
        ItemChildRemovedChange   : QQuickItem.ItemChange = ... # 0x1
        ItemSceneChange          : QQuickItem.ItemChange = ... # 0x2
        ItemVisibleHasChanged    : QQuickItem.ItemChange = ... # 0x3
        ItemParentHasChanged     : QQuickItem.ItemChange = ... # 0x4
        ItemOpacityHasChanged    : QQuickItem.ItemChange = ... # 0x5
        ItemActiveFocusHasChanged: QQuickItem.ItemChange = ... # 0x6
        ItemRotationHasChanged   : QQuickItem.ItemChange = ... # 0x7
        ItemAntialiasingHasChanged: QQuickItem.ItemChange = ... # 0x8
        ItemDevicePixelRatioHasChanged: QQuickItem.ItemChange = ... # 0x9
        ItemEnabledHasChanged    : QQuickItem.ItemChange = ... # 0xa

    class TransformOrigin(object):
        TopLeft                  : QQuickItem.TransformOrigin = ... # 0x0
        Top                      : QQuickItem.TransformOrigin = ... # 0x1
        TopRight                 : QQuickItem.TransformOrigin = ... # 0x2
        Left                     : QQuickItem.TransformOrigin = ... # 0x3
        Center                   : QQuickItem.TransformOrigin = ... # 0x4
        Right                    : QQuickItem.TransformOrigin = ... # 0x5
        BottomLeft               : QQuickItem.TransformOrigin = ... # 0x6
        Bottom                   : QQuickItem.TransformOrigin = ... # 0x7
        BottomRight              : QQuickItem.TransformOrigin = ... # 0x8

    class UpdatePaintNodeData(Shiboken.Object):
        def __copy__(self): ...

    def __init__(self, parent:typing.Optional[PySide2.QtQuick.QQuickItem]=...): ...

    def acceptHoverEvents(self) -> bool: ...
    def acceptTouchEvents(self) -> bool: ...
    def acceptedMouseButtons(self) -> PySide2.QtCore.Qt.MouseButtons: ...
    def activeFocusOnTab(self) -> bool: ...
    def antialiasing(self) -> bool: ...
    def baselineOffset(self) -> float: ...
    def boundingRect(self) -> PySide2.QtCore.QRectF: ...
    def childAt(self, x:float, y:float) -> PySide2.QtQuick.QQuickItem: ...
    def childItems(self) -> typing.List: ...
    def childMouseEventFilter(self, arg__1:PySide2.QtQuick.QQuickItem, arg__2:PySide2.QtCore.QEvent) -> bool: ...
    def childrenRect(self) -> PySide2.QtCore.QRectF: ...
    def classBegin(self): ...
    def clip(self) -> bool: ...
    def clipRect(self) -> PySide2.QtCore.QRectF: ...
    def componentComplete(self): ...
    def containmentMask(self) -> PySide2.QtCore.QObject: ...
    def contains(self, point:PySide2.QtCore.QPointF) -> bool: ...
    def cursor(self) -> PySide2.QtGui.QCursor: ...
    def dragEnterEvent(self, arg__1:PySide2.QtGui.QDragEnterEvent): ...
    def dragLeaveEvent(self, arg__1:PySide2.QtGui.QDragLeaveEvent): ...
    def dragMoveEvent(self, arg__1:PySide2.QtGui.QDragMoveEvent): ...
    def dropEvent(self, arg__1:PySide2.QtGui.QDropEvent): ...
    def event(self, arg__1:PySide2.QtCore.QEvent) -> bool: ...
    def filtersChildMouseEvents(self) -> bool: ...
    def flags(self) -> PySide2.QtQuick.QQuickItem.Flags: ...
    def focusInEvent(self, arg__1:PySide2.QtGui.QFocusEvent): ...
    def focusOutEvent(self, arg__1:PySide2.QtGui.QFocusEvent): ...
    @typing.overload
    def forceActiveFocus(self): ...
    @typing.overload
    def forceActiveFocus(self, reason:PySide2.QtCore.Qt.FocusReason): ...
    def geometryChanged(self, newGeometry:PySide2.QtCore.QRectF, oldGeometry:PySide2.QtCore.QRectF): ...
    def grabMouse(self): ...
    @typing.overload
    def grabToImage(self, callback:PySide2.QtQml.QJSValue, targetSize:PySide2.QtCore.QSize=...) -> bool: ...
    @typing.overload
    def grabToImage(self, targetSize:PySide2.QtCore.QSize=...) -> PySide2.QtQuick.QQuickItemGrabResult: ...
    def grabTouchPoints(self, ids:typing.List): ...
    def hasActiveFocus(self) -> bool: ...
    def hasFocus(self) -> bool: ...
    def height(self) -> float: ...
    def heightValid(self) -> bool: ...
    def hoverEnterEvent(self, event:PySide2.QtGui.QHoverEvent): ...
    def hoverLeaveEvent(self, event:PySide2.QtGui.QHoverEvent): ...
    def hoverMoveEvent(self, event:PySide2.QtGui.QHoverEvent): ...
    def implicitHeight(self) -> float: ...
    def implicitWidth(self) -> float: ...
    def inputMethodEvent(self, arg__1:PySide2.QtGui.QInputMethodEvent): ...
    def inputMethodQuery(self, query:PySide2.QtCore.Qt.InputMethodQuery) -> typing.Any: ...
    def isAncestorOf(self, child:PySide2.QtQuick.QQuickItem) -> bool: ...
    def isComponentComplete(self) -> bool: ...
    def isEnabled(self) -> bool: ...
    def isFocusScope(self) -> bool: ...
    def isTextureProvider(self) -> bool: ...
    def isUnderMouse(self) -> bool: ...
    def isVisible(self) -> bool: ...
    def itemTransform(self, arg__1:PySide2.QtQuick.QQuickItem) -> typing.Tuple: ...
    def keepMouseGrab(self) -> bool: ...
    def keepTouchGrab(self) -> bool: ...
    def keyPressEvent(self, event:PySide2.QtGui.QKeyEvent): ...
    def keyReleaseEvent(self, event:PySide2.QtGui.QKeyEvent): ...
    def mapFromGlobal(self, point:PySide2.QtCore.QPointF) -> PySide2.QtCore.QPointF: ...
    def mapFromItem(self, item:PySide2.QtQuick.QQuickItem, point:PySide2.QtCore.QPointF) -> PySide2.QtCore.QPointF: ...
    def mapFromScene(self, point:PySide2.QtCore.QPointF) -> PySide2.QtCore.QPointF: ...
    def mapRectFromItem(self, item:PySide2.QtQuick.QQuickItem, rect:PySide2.QtCore.QRectF) -> PySide2.QtCore.QRectF: ...
    def mapRectFromScene(self, rect:PySide2.QtCore.QRectF) -> PySide2.QtCore.QRectF: ...
    def mapRectToItem(self, item:PySide2.QtQuick.QQuickItem, rect:PySide2.QtCore.QRectF) -> PySide2.QtCore.QRectF: ...
    def mapRectToScene(self, rect:PySide2.QtCore.QRectF) -> PySide2.QtCore.QRectF: ...
    def mapToGlobal(self, point:PySide2.QtCore.QPointF) -> PySide2.QtCore.QPointF: ...
    def mapToItem(self, item:PySide2.QtQuick.QQuickItem, point:PySide2.QtCore.QPointF) -> PySide2.QtCore.QPointF: ...
    def mapToScene(self, point:PySide2.QtCore.QPointF) -> PySide2.QtCore.QPointF: ...
    def mouseDoubleClickEvent(self, event:PySide2.QtGui.QMouseEvent): ...
    def mouseMoveEvent(self, event:PySide2.QtGui.QMouseEvent): ...
    def mousePressEvent(self, event:PySide2.QtGui.QMouseEvent): ...
    def mouseReleaseEvent(self, event:PySide2.QtGui.QMouseEvent): ...
    def mouseUngrabEvent(self): ...
    def nextItemInFocusChain(self, forward:bool=...) -> PySide2.QtQuick.QQuickItem: ...
    def opacity(self) -> float: ...
    def parentItem(self) -> PySide2.QtQuick.QQuickItem: ...
    def polish(self): ...
    def position(self) -> PySide2.QtCore.QPointF: ...
    def releaseResources(self): ...
    def resetAntialiasing(self): ...
    def resetHeight(self): ...
    def resetWidth(self): ...
    def rotation(self) -> float: ...
    def scale(self) -> float: ...
    def scopedFocusItem(self) -> PySide2.QtQuick.QQuickItem: ...
    def setAcceptHoverEvents(self, enabled:bool): ...
    def setAcceptTouchEvents(self, accept:bool): ...
    def setAcceptedMouseButtons(self, buttons:PySide2.QtCore.Qt.MouseButtons): ...
    def setActiveFocusOnTab(self, arg__1:bool): ...
    def setAntialiasing(self, arg__1:bool): ...
    def setBaselineOffset(self, arg__1:float): ...
    def setClip(self, arg__1:bool): ...
    def setContainmentMask(self, mask:PySide2.QtCore.QObject): ...
    def setCursor(self, cursor:PySide2.QtGui.QCursor): ...
    def setEnabled(self, arg__1:bool): ...
    def setFiltersChildMouseEvents(self, filter:bool): ...
    def setFlag(self, flag:PySide2.QtQuick.QQuickItem.Flag, enabled:bool=...): ...
    def setFlags(self, flags:PySide2.QtQuick.QQuickItem.Flags): ...
    @typing.overload
    def setFocus(self, arg__1:bool): ...
    @typing.overload
    def setFocus(self, focus:bool, reason:PySide2.QtCore.Qt.FocusReason): ...
    def setHeight(self, arg__1:float): ...
    def setImplicitHeight(self, arg__1:float): ...
    def setImplicitSize(self, arg__1:float, arg__2:float): ...
    def setImplicitWidth(self, arg__1:float): ...
    def setKeepMouseGrab(self, arg__1:bool): ...
    def setKeepTouchGrab(self, arg__1:bool): ...
    def setOpacity(self, arg__1:float): ...
    def setParentItem(self, parent:PySide2.QtQuick.QQuickItem): ...
    def setPosition(self, arg__1:PySide2.QtCore.QPointF): ...
    def setRotation(self, arg__1:float): ...
    def setScale(self, arg__1:float): ...
    def setSize(self, size:PySide2.QtCore.QSizeF): ...
    def setSmooth(self, arg__1:bool): ...
    def setState(self, arg__1:str): ...
    def setTransformOrigin(self, arg__1:PySide2.QtQuick.QQuickItem.TransformOrigin): ...
    def setTransformOriginPoint(self, arg__1:PySide2.QtCore.QPointF): ...
    def setVisible(self, arg__1:bool): ...
    def setWidth(self, arg__1:float): ...
    def setX(self, arg__1:float): ...
    def setY(self, arg__1:float): ...
    def setZ(self, arg__1:float): ...
    def size(self) -> PySide2.QtCore.QSizeF: ...
    def smooth(self) -> bool: ...
    def stackAfter(self, arg__1:PySide2.QtQuick.QQuickItem): ...
    def stackBefore(self, arg__1:PySide2.QtQuick.QQuickItem): ...
    def state(self) -> str: ...
    def textureProvider(self) -> PySide2.QtQuick.QSGTextureProvider: ...
    def touchEvent(self, event:PySide2.QtGui.QTouchEvent): ...
    def touchUngrabEvent(self): ...
    def transformOrigin(self) -> PySide2.QtQuick.QQuickItem.TransformOrigin: ...
    def transformOriginPoint(self) -> PySide2.QtCore.QPointF: ...
    def ungrabMouse(self): ...
    def ungrabTouchPoints(self): ...
    def unsetCursor(self): ...
    def update(self): ...
    def updateInputMethod(self, queries:PySide2.QtCore.Qt.InputMethodQueries=...): ...
    def updatePaintNode(self, arg__1:PySide2.QtQuick.QSGNode, arg__2:PySide2.QtQuick.QQuickItem.UpdatePaintNodeData) -> PySide2.QtQuick.QSGNode: ...
    def updatePolish(self): ...
    def wheelEvent(self, event:PySide2.QtGui.QWheelEvent): ...
    def width(self) -> float: ...
    def widthValid(self) -> bool: ...
    def window(self) -> PySide2.QtQuick.QQuickWindow: ...
    def windowDeactivateEvent(self): ...
    def x(self) -> float: ...
    def y(self) -> float: ...
    def z(self) -> float: ...


class QQuickItemGrabResult(PySide2.QtCore.QObject):
    def event(self, arg__1:PySide2.QtCore.QEvent) -> bool: ...
    def image(self) -> PySide2.QtGui.QImage: ...
    def saveToFile(self, fileName:str) -> bool: ...
    def url(self) -> PySide2.QtCore.QUrl: ...


class QQuickPaintedItem(PySide2.QtQuick.QQuickItem):
    Image                    : QQuickPaintedItem = ... # 0x0
    FastFBOResizing          : QQuickPaintedItem = ... # 0x1
    FramebufferObject        : QQuickPaintedItem = ... # 0x1
    InvertedYFramebufferObject: QQuickPaintedItem = ... # 0x2

    class PerformanceHint(object):
        FastFBOResizing          : QQuickPaintedItem.PerformanceHint = ... # 0x1

    class PerformanceHints(object): ...

    class RenderTarget(object):
        Image                    : QQuickPaintedItem.RenderTarget = ... # 0x0
        FramebufferObject        : QQuickPaintedItem.RenderTarget = ... # 0x1
        InvertedYFramebufferObject: QQuickPaintedItem.RenderTarget = ... # 0x2

    def __init__(self, parent:typing.Optional[PySide2.QtQuick.QQuickItem]=...): ...

    def antialiasing(self) -> bool: ...
    def contentsBoundingRect(self) -> PySide2.QtCore.QRectF: ...
    def contentsScale(self) -> float: ...
    def contentsSize(self) -> PySide2.QtCore.QSize: ...
    def fillColor(self) -> PySide2.QtGui.QColor: ...
    def isTextureProvider(self) -> bool: ...
    def mipmap(self) -> bool: ...
    def opaquePainting(self) -> bool: ...
    def paint(self, painter:PySide2.QtGui.QPainter): ...
    def performanceHints(self) -> PySide2.QtQuick.QQuickPaintedItem.PerformanceHints: ...
    def releaseResources(self): ...
    def renderTarget(self) -> PySide2.QtQuick.QQuickPaintedItem.RenderTarget: ...
    def resetContentsSize(self): ...
    def setAntialiasing(self, enable:bool): ...
    def setContentsScale(self, arg__1:float): ...
    def setContentsSize(self, arg__1:PySide2.QtCore.QSize): ...
    def setFillColor(self, arg__1:PySide2.QtGui.QColor): ...
    def setMipmap(self, enable:bool): ...
    def setOpaquePainting(self, opaque:bool): ...
    def setPerformanceHint(self, hint:PySide2.QtQuick.QQuickPaintedItem.PerformanceHint, enabled:bool=...): ...
    def setPerformanceHints(self, hints:PySide2.QtQuick.QQuickPaintedItem.PerformanceHints): ...
    def setRenderTarget(self, target:PySide2.QtQuick.QQuickPaintedItem.RenderTarget): ...
    def setTextureSize(self, size:PySide2.QtCore.QSize): ...
    def textureProvider(self) -> PySide2.QtQuick.QSGTextureProvider: ...
    def textureSize(self) -> PySide2.QtCore.QSize: ...
    @typing.overload
    def update(self): ...
    @typing.overload
    def update(self, rect:PySide2.QtCore.QRect=...): ...
    def updatePaintNode(self, arg__1:PySide2.QtQuick.QSGNode, arg__2:PySide2.QtQuick.QQuickItem.UpdatePaintNodeData) -> PySide2.QtQuick.QSGNode: ...


class QQuickRenderControl(PySide2.QtCore.QObject):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def grab(self) -> PySide2.QtGui.QImage: ...
    def initialize(self, gl:PySide2.QtGui.QOpenGLContext): ...
    def invalidate(self): ...
    def polishItems(self): ...
    def prepareThread(self, targetThread:PySide2.QtCore.QThread): ...
    def render(self): ...
    def renderWindow(self, offset:PySide2.QtCore.QPoint) -> PySide2.QtGui.QWindow: ...
    @staticmethod
    def renderWindowFor(win:PySide2.QtQuick.QQuickWindow, offset:typing.Optional[PySide2.QtCore.QPoint]=...) -> PySide2.QtGui.QWindow: ...
    def sync(self) -> bool: ...


class QQuickTextDocument(PySide2.QtCore.QObject):

    def __init__(self, parent:PySide2.QtQuick.QQuickItem): ...

    def textDocument(self) -> PySide2.QtGui.QTextDocument: ...


class QQuickTextureFactory(PySide2.QtCore.QObject):

    def __init__(self): ...

    def createTexture(self, window:PySide2.QtQuick.QQuickWindow) -> PySide2.QtQuick.QSGTexture: ...
    def image(self) -> PySide2.QtGui.QImage: ...
    def textureByteCount(self) -> int: ...
    @staticmethod
    def textureFactoryForImage(image:PySide2.QtGui.QImage) -> PySide2.QtQuick.QQuickTextureFactory: ...
    def textureSize(self) -> PySide2.QtCore.QSize: ...


class QQuickTransform(PySide2.QtCore.QObject):

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def appendToItem(self, arg__1:PySide2.QtQuick.QQuickItem): ...
    def applyTo(self, matrix:PySide2.QtGui.QMatrix4x4): ...
    def prependToItem(self, arg__1:PySide2.QtQuick.QQuickItem): ...
    def update(self): ...


class QQuickView(PySide2.QtQuick.QQuickWindow):
    Null                     : QQuickView = ... # 0x0
    SizeViewToRootObject     : QQuickView = ... # 0x0
    Ready                    : QQuickView = ... # 0x1
    SizeRootObjectToView     : QQuickView = ... # 0x1
    Loading                  : QQuickView = ... # 0x2
    Error                    : QQuickView = ... # 0x3

    class ResizeMode(object):
        SizeViewToRootObject     : QQuickView.ResizeMode = ... # 0x0
        SizeRootObjectToView     : QQuickView.ResizeMode = ... # 0x1

    class Status(object):
        Null                     : QQuickView.Status = ... # 0x0
        Ready                    : QQuickView.Status = ... # 0x1
        Loading                  : QQuickView.Status = ... # 0x2
        Error                    : QQuickView.Status = ... # 0x3

    @typing.overload
    def __init__(self, engine:PySide2.QtQml.QQmlEngine, parent:PySide2.QtGui.QWindow): ...
    @typing.overload
    def __init__(self, parent:typing.Optional[PySide2.QtGui.QWindow]=...): ...
    @typing.overload
    def __init__(self, source:PySide2.QtCore.QUrl, parent:typing.Optional[PySide2.QtGui.QWindow]=...): ...
    @typing.overload
    def __init__(self, source:PySide2.QtCore.QUrl, renderControl:PySide2.QtQuick.QQuickRenderControl): ...

    def engine(self) -> PySide2.QtQml.QQmlEngine: ...
    def errors(self) -> typing.List: ...
    def initialSize(self) -> PySide2.QtCore.QSize: ...
    def keyPressEvent(self, arg__1:PySide2.QtGui.QKeyEvent): ...
    def keyReleaseEvent(self, arg__1:PySide2.QtGui.QKeyEvent): ...
    def mouseMoveEvent(self, arg__1:PySide2.QtGui.QMouseEvent): ...
    def mousePressEvent(self, arg__1:PySide2.QtGui.QMouseEvent): ...
    def mouseReleaseEvent(self, arg__1:PySide2.QtGui.QMouseEvent): ...
    def resizeEvent(self, arg__1:PySide2.QtGui.QResizeEvent): ...
    def resizeMode(self) -> PySide2.QtQuick.QQuickView.ResizeMode: ...
    def rootContext(self) -> PySide2.QtQml.QQmlContext: ...
    def rootObject(self) -> PySide2.QtQuick.QQuickItem: ...
    def setContent(self, url:PySide2.QtCore.QUrl, component:PySide2.QtQml.QQmlComponent, item:PySide2.QtCore.QObject): ...
    def setInitialProperties(self, initialProperties:typing.Dict): ...
    def setResizeMode(self, arg__1:PySide2.QtQuick.QQuickView.ResizeMode): ...
    def setSource(self, arg__1:PySide2.QtCore.QUrl): ...
    def sizeHint(self) -> PySide2.QtCore.QSize: ...
    def source(self) -> PySide2.QtCore.QUrl: ...
    def status(self) -> PySide2.QtQuick.QQuickView.Status: ...
    def timerEvent(self, arg__1:PySide2.QtCore.QTimerEvent): ...


class QQuickWindow(PySide2.QtGui.QWindow):
    BeforeSynchronizingStage : QQuickWindow = ... # 0x0
    NativeObjectTexture      : QQuickWindow = ... # 0x0
    QtTextRendering          : QQuickWindow = ... # 0x0
    AfterSynchronizingStage  : QQuickWindow = ... # 0x1
    ContextNotAvailable      : QQuickWindow = ... # 0x1
    NativeTextRendering      : QQuickWindow = ... # 0x1
    TextureHasAlphaChannel   : QQuickWindow = ... # 0x1
    BeforeRenderingStage     : QQuickWindow = ... # 0x2
    TextureHasMipmaps        : QQuickWindow = ... # 0x2
    AfterRenderingStage      : QQuickWindow = ... # 0x3
    AfterSwapStage           : QQuickWindow = ... # 0x4
    TextureOwnsGLTexture     : QQuickWindow = ... # 0x4
    NoStage                  : QQuickWindow = ... # 0x5
    TextureCanUseAtlas       : QQuickWindow = ... # 0x8
    TextureIsOpaque          : QQuickWindow = ... # 0x10

    class CreateTextureOption(object):
        TextureHasAlphaChannel   : QQuickWindow.CreateTextureOption = ... # 0x1
        TextureHasMipmaps        : QQuickWindow.CreateTextureOption = ... # 0x2
        TextureOwnsGLTexture     : QQuickWindow.CreateTextureOption = ... # 0x4
        TextureCanUseAtlas       : QQuickWindow.CreateTextureOption = ... # 0x8
        TextureIsOpaque          : QQuickWindow.CreateTextureOption = ... # 0x10

    class CreateTextureOptions(object): ...

    class NativeObjectType(object):
        NativeObjectTexture      : QQuickWindow.NativeObjectType = ... # 0x0

    class RenderStage(object):
        BeforeSynchronizingStage : QQuickWindow.RenderStage = ... # 0x0
        AfterSynchronizingStage  : QQuickWindow.RenderStage = ... # 0x1
        BeforeRenderingStage     : QQuickWindow.RenderStage = ... # 0x2
        AfterRenderingStage      : QQuickWindow.RenderStage = ... # 0x3
        AfterSwapStage           : QQuickWindow.RenderStage = ... # 0x4
        NoStage                  : QQuickWindow.RenderStage = ... # 0x5

    class SceneGraphError(object):
        ContextNotAvailable      : QQuickWindow.SceneGraphError = ... # 0x1

    class TextRenderType(object):
        QtTextRendering          : QQuickWindow.TextRenderType = ... # 0x0
        NativeTextRendering      : QQuickWindow.TextRenderType = ... # 0x1

    @typing.overload
    def __init__(self, parent:typing.Optional[PySide2.QtGui.QWindow]=...): ...
    @typing.overload
    def __init__(self, renderControl:PySide2.QtQuick.QQuickRenderControl): ...

    def accessibleRoot(self) -> PySide2.QtGui.QAccessibleInterface: ...
    def activeFocusItem(self) -> PySide2.QtQuick.QQuickItem: ...
    def beginExternalCommands(self): ...
    def clearBeforeRendering(self) -> bool: ...
    def color(self) -> PySide2.QtGui.QColor: ...
    def contentItem(self) -> PySide2.QtQuick.QQuickItem: ...
    def createTextureFromId(self, id:int, size:PySide2.QtCore.QSize, options:PySide2.QtQuick.QQuickWindow.CreateTextureOptions=...) -> PySide2.QtQuick.QSGTexture: ...
    @typing.overload
    def createTextureFromImage(self, image:PySide2.QtGui.QImage) -> PySide2.QtQuick.QSGTexture: ...
    @typing.overload
    def createTextureFromImage(self, image:PySide2.QtGui.QImage, options:PySide2.QtQuick.QQuickWindow.CreateTextureOptions) -> PySide2.QtQuick.QSGTexture: ...
    def createTextureFromNativeObject(self, type:PySide2.QtQuick.QQuickWindow.NativeObjectType, nativeObjectPtr:int, nativeLayout:int, size:PySide2.QtCore.QSize, options:PySide2.QtQuick.QQuickWindow.CreateTextureOptions=...) -> PySide2.QtQuick.QSGTexture: ...
    def effectiveDevicePixelRatio(self) -> float: ...
    def endExternalCommands(self): ...
    def event(self, arg__1:PySide2.QtCore.QEvent) -> bool: ...
    def exposeEvent(self, arg__1:PySide2.QtGui.QExposeEvent): ...
    def focusInEvent(self, arg__1:PySide2.QtGui.QFocusEvent): ...
    def focusObject(self) -> PySide2.QtCore.QObject: ...
    def focusOutEvent(self, arg__1:PySide2.QtGui.QFocusEvent): ...
    def grabWindow(self) -> PySide2.QtGui.QImage: ...
    @staticmethod
    def hasDefaultAlphaBuffer() -> bool: ...
    def hideEvent(self, arg__1:PySide2.QtGui.QHideEvent): ...
    def incubationController(self) -> PySide2.QtQml.QQmlIncubationController: ...
    def isPersistentOpenGLContext(self) -> bool: ...
    def isPersistentSceneGraph(self) -> bool: ...
    def isSceneGraphInitialized(self) -> bool: ...
    def keyPressEvent(self, arg__1:PySide2.QtGui.QKeyEvent): ...
    def keyReleaseEvent(self, arg__1:PySide2.QtGui.QKeyEvent): ...
    def mouseDoubleClickEvent(self, arg__1:PySide2.QtGui.QMouseEvent): ...
    def mouseGrabberItem(self) -> PySide2.QtQuick.QQuickItem: ...
    def mouseMoveEvent(self, arg__1:PySide2.QtGui.QMouseEvent): ...
    def mousePressEvent(self, arg__1:PySide2.QtGui.QMouseEvent): ...
    def mouseReleaseEvent(self, arg__1:PySide2.QtGui.QMouseEvent): ...
    def openglContext(self) -> PySide2.QtGui.QOpenGLContext: ...
    def releaseResources(self): ...
    def renderTarget(self) -> PySide2.QtGui.QOpenGLFramebufferObject: ...
    def renderTargetId(self) -> int: ...
    def renderTargetSize(self) -> PySide2.QtCore.QSize: ...
    def resetOpenGLState(self): ...
    def resizeEvent(self, arg__1:PySide2.QtGui.QResizeEvent): ...
    @staticmethod
    def sceneGraphBackend() -> str: ...
    def scheduleRenderJob(self, job:PySide2.QtCore.QRunnable, schedule:PySide2.QtQuick.QQuickWindow.RenderStage): ...
    def sendEvent(self, arg__1:PySide2.QtQuick.QQuickItem, arg__2:PySide2.QtCore.QEvent) -> bool: ...
    def setClearBeforeRendering(self, enabled:bool): ...
    def setColor(self, color:PySide2.QtGui.QColor): ...
    @staticmethod
    def setDefaultAlphaBuffer(useAlpha:bool): ...
    def setPersistentOpenGLContext(self, persistent:bool): ...
    def setPersistentSceneGraph(self, persistent:bool): ...
    @typing.overload
    def setRenderTarget(self, fbo:PySide2.QtGui.QOpenGLFramebufferObject): ...
    @typing.overload
    def setRenderTarget(self, fboId:int, size:PySide2.QtCore.QSize): ...
    @staticmethod
    def setSceneGraphBackend(backend:str): ...
    @staticmethod
    def setTextRenderType(renderType:PySide2.QtQuick.QQuickWindow.TextRenderType): ...
    def showEvent(self, arg__1:PySide2.QtGui.QShowEvent): ...
    def tabletEvent(self, arg__1:PySide2.QtGui.QTabletEvent): ...
    @staticmethod
    def textRenderType() -> PySide2.QtQuick.QQuickWindow.TextRenderType: ...
    def update(self): ...
    def wheelEvent(self, arg__1:PySide2.QtGui.QWheelEvent): ...


class QSGAbstractRenderer(PySide2.QtCore.QObject):
    ClearColorBuffer         : QSGAbstractRenderer = ... # 0x1
    MatrixTransformFlipY     : QSGAbstractRenderer = ... # 0x1
    ClearDepthBuffer         : QSGAbstractRenderer = ... # 0x2
    ClearStencilBuffer       : QSGAbstractRenderer = ... # 0x4

    class ClearMode(object): ...

    class ClearModeBit(object):
        ClearColorBuffer         : QSGAbstractRenderer.ClearModeBit = ... # 0x1
        ClearDepthBuffer         : QSGAbstractRenderer.ClearModeBit = ... # 0x2
        ClearStencilBuffer       : QSGAbstractRenderer.ClearModeBit = ... # 0x4

    class MatrixTransformFlag(object):
        MatrixTransformFlipY     : QSGAbstractRenderer.MatrixTransformFlag = ... # 0x1

    class MatrixTransformFlags(object): ...

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def clearColor(self) -> PySide2.QtGui.QColor: ...
    def clearMode(self) -> PySide2.QtQuick.QSGAbstractRenderer.ClearMode: ...
    def deviceRect(self) -> PySide2.QtCore.QRect: ...
    def nodeChanged(self, node:PySide2.QtQuick.QSGNode, state:PySide2.QtQuick.QSGNode.DirtyState): ...
    def projectionMatrix(self) -> PySide2.QtGui.QMatrix4x4: ...
    def projectionMatrixWithNativeNDC(self) -> PySide2.QtGui.QMatrix4x4: ...
    def renderScene(self, fboId:int=...): ...
    def setClearColor(self, color:PySide2.QtGui.QColor): ...
    def setClearMode(self, mode:PySide2.QtQuick.QSGAbstractRenderer.ClearMode): ...
    @typing.overload
    def setDeviceRect(self, rect:PySide2.QtCore.QRect): ...
    @typing.overload
    def setDeviceRect(self, size:PySide2.QtCore.QSize): ...
    def setProjectionMatrix(self, matrix:PySide2.QtGui.QMatrix4x4): ...
    @typing.overload
    def setProjectionMatrixToRect(self, rect:PySide2.QtCore.QRectF): ...
    @typing.overload
    def setProjectionMatrixToRect(self, rect:PySide2.QtCore.QRectF, flags:PySide2.QtQuick.QSGAbstractRenderer.MatrixTransformFlags): ...
    def setProjectionMatrixWithNativeNDC(self, matrix:PySide2.QtGui.QMatrix4x4): ...
    @typing.overload
    def setViewportRect(self, rect:PySide2.QtCore.QRect): ...
    @typing.overload
    def setViewportRect(self, size:PySide2.QtCore.QSize): ...
    def viewportRect(self) -> PySide2.QtCore.QRect: ...


class QSGBasicGeometryNode(PySide2.QtQuick.QSGNode):

    def __init__(self, type:PySide2.QtQuick.QSGNode.NodeType): ...

    def clipList(self) -> PySide2.QtQuick.QSGClipNode: ...
    def geometry(self) -> PySide2.QtQuick.QSGGeometry: ...
    def matrix(self) -> PySide2.QtGui.QMatrix4x4: ...
    def setGeometry(self, geometry:PySide2.QtQuick.QSGGeometry): ...
    def setRendererClipList(self, c:PySide2.QtQuick.QSGClipNode): ...
    def setRendererMatrix(self, m:PySide2.QtGui.QMatrix4x4): ...


class QSGClipNode(PySide2.QtQuick.QSGBasicGeometryNode):

    def __init__(self): ...

    def clipRect(self) -> PySide2.QtCore.QRectF: ...
    def isRectangular(self) -> bool: ...
    def setClipRect(self, arg__1:PySide2.QtCore.QRectF): ...
    def setIsRectangular(self, rectHint:bool): ...


class QSGDynamicTexture(PySide2.QtQuick.QSGTexture):

    def __init__(self): ...

    def updateTexture(self) -> bool: ...


class QSGEngine(PySide2.QtCore.QObject):
    TextureHasAlphaChannel   : QSGEngine = ... # 0x1
    TextureOwnsGLTexture     : QSGEngine = ... # 0x4
    TextureCanUseAtlas       : QSGEngine = ... # 0x8
    TextureIsOpaque          : QSGEngine = ... # 0x10

    class CreateTextureOption(object):
        TextureHasAlphaChannel   : QSGEngine.CreateTextureOption = ... # 0x1
        TextureOwnsGLTexture     : QSGEngine.CreateTextureOption = ... # 0x4
        TextureCanUseAtlas       : QSGEngine.CreateTextureOption = ... # 0x8
        TextureIsOpaque          : QSGEngine.CreateTextureOption = ... # 0x10

    class CreateTextureOptions(object): ...

    def __init__(self, parent:typing.Optional[PySide2.QtCore.QObject]=...): ...

    def createRenderer(self) -> PySide2.QtQuick.QSGAbstractRenderer: ...
    def createTextureFromId(self, id:int, size:PySide2.QtCore.QSize, options:PySide2.QtQuick.QSGEngine.CreateTextureOptions=...) -> PySide2.QtQuick.QSGTexture: ...
    def createTextureFromImage(self, image:PySide2.QtGui.QImage, options:PySide2.QtQuick.QSGEngine.CreateTextureOptions=...) -> PySide2.QtQuick.QSGTexture: ...
    def initialize(self, context:PySide2.QtGui.QOpenGLContext): ...
    def invalidate(self): ...


class QSGGeometry(Shiboken.Object):
    AlwaysUploadPattern      : QSGGeometry = ... # 0x0
    DrawPoints               : QSGGeometry = ... # 0x0
    UnknownAttribute         : QSGGeometry = ... # 0x0
    DrawLines                : QSGGeometry = ... # 0x1
    PositionAttribute        : QSGGeometry = ... # 0x1
    StreamPattern            : QSGGeometry = ... # 0x1
    ColorAttribute           : QSGGeometry = ... # 0x2
    DrawLineLoop             : QSGGeometry = ... # 0x2
    DynamicPattern           : QSGGeometry = ... # 0x2
    DrawLineStrip            : QSGGeometry = ... # 0x3
    StaticPattern            : QSGGeometry = ... # 0x3
    TexCoordAttribute        : QSGGeometry = ... # 0x3
    DrawTriangles            : QSGGeometry = ... # 0x4
    TexCoord1Attribute       : QSGGeometry = ... # 0x4
    DrawTriangleStrip        : QSGGeometry = ... # 0x5
    TexCoord2Attribute       : QSGGeometry = ... # 0x5
    DrawTriangleFan          : QSGGeometry = ... # 0x6
    ByteType                 : QSGGeometry = ... # 0x1400
    UnsignedByteType         : QSGGeometry = ... # 0x1401
    ShortType                : QSGGeometry = ... # 0x1402
    UnsignedShortType        : QSGGeometry = ... # 0x1403
    IntType                  : QSGGeometry = ... # 0x1404
    UnsignedIntType          : QSGGeometry = ... # 0x1405
    FloatType                : QSGGeometry = ... # 0x1406
    Bytes2Type               : QSGGeometry = ... # 0x1407
    Bytes3Type               : QSGGeometry = ... # 0x1408
    Bytes4Type               : QSGGeometry = ... # 0x1409
    DoubleType               : QSGGeometry = ... # 0x140a

    class Attribute(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, Attribute:PySide2.QtQuick.QSGGeometry.Attribute): ...

        def __copy__(self): ...
        @staticmethod
        def create(pos:int, tupleSize:int, primitiveType:int, isPosition:bool=...) -> PySide2.QtQuick.QSGGeometry.Attribute: ...
        @staticmethod
        def createWithAttributeType(pos:int, tupleSize:int, primitiveType:int, attributeType:PySide2.QtQuick.QSGGeometry.AttributeType) -> PySide2.QtQuick.QSGGeometry.Attribute: ...

    class AttributeSet(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, AttributeSet:PySide2.QtQuick.QSGGeometry.AttributeSet): ...

        def __copy__(self): ...

    class AttributeType(object):
        UnknownAttribute         : QSGGeometry.AttributeType = ... # 0x0
        PositionAttribute        : QSGGeometry.AttributeType = ... # 0x1
        ColorAttribute           : QSGGeometry.AttributeType = ... # 0x2
        TexCoordAttribute        : QSGGeometry.AttributeType = ... # 0x3
        TexCoord1Attribute       : QSGGeometry.AttributeType = ... # 0x4
        TexCoord2Attribute       : QSGGeometry.AttributeType = ... # 0x5

    class ColoredPoint2D(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, ColoredPoint2D:PySide2.QtQuick.QSGGeometry.ColoredPoint2D): ...

        def __copy__(self): ...
        def set(self, nx:float, ny:float, nr:int, ng:int, nb:int, na:int): ...

    class DataPattern(object):
        AlwaysUploadPattern      : QSGGeometry.DataPattern = ... # 0x0
        StreamPattern            : QSGGeometry.DataPattern = ... # 0x1
        DynamicPattern           : QSGGeometry.DataPattern = ... # 0x2
        StaticPattern            : QSGGeometry.DataPattern = ... # 0x3

    class DrawingMode(object):
        DrawPoints               : QSGGeometry.DrawingMode = ... # 0x0
        DrawLines                : QSGGeometry.DrawingMode = ... # 0x1
        DrawLineLoop             : QSGGeometry.DrawingMode = ... # 0x2
        DrawLineStrip            : QSGGeometry.DrawingMode = ... # 0x3
        DrawTriangles            : QSGGeometry.DrawingMode = ... # 0x4
        DrawTriangleStrip        : QSGGeometry.DrawingMode = ... # 0x5
        DrawTriangleFan          : QSGGeometry.DrawingMode = ... # 0x6

    class Point2D(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, Point2D:PySide2.QtQuick.QSGGeometry.Point2D): ...

        def __copy__(self): ...
        def set(self, nx:float, ny:float): ...

    class TexturedPoint2D(Shiboken.Object):

        @typing.overload
        def __init__(self): ...
        @typing.overload
        def __init__(self, TexturedPoint2D:PySide2.QtQuick.QSGGeometry.TexturedPoint2D): ...

        def __copy__(self): ...
        def set(self, nx:float, ny:float, ntx:float, nty:float): ...

    class Type(object):
        ByteType                 : QSGGeometry.Type = ... # 0x1400
        UnsignedByteType         : QSGGeometry.Type = ... # 0x1401
        ShortType                : QSGGeometry.Type = ... # 0x1402
        UnsignedShortType        : QSGGeometry.Type = ... # 0x1403
        IntType                  : QSGGeometry.Type = ... # 0x1404
        UnsignedIntType          : QSGGeometry.Type = ... # 0x1405
        FloatType                : QSGGeometry.Type = ... # 0x1406
        Bytes2Type               : QSGGeometry.Type = ... # 0x1407
        Bytes3Type               : QSGGeometry.Type = ... # 0x1408
        Bytes4Type               : QSGGeometry.Type = ... # 0x1409
        DoubleType               : QSGGeometry.Type = ... # 0x140a

    def __init__(self, attribs:PySide2.QtQuick.QSGGeometry.AttributeSet, vertexCount:int, indexCount:int=..., indexType:int=...): ...

    def allocate(self, vertexCount:int, indexCount:int=...): ...
    def attributeCount(self) -> int: ...
    def attributes(self) -> PySide2.QtQuick.QSGGeometry.Attribute: ...
    @staticmethod
    def defaultAttributes_ColoredPoint2D() -> PySide2.QtQuick.QSGGeometry.AttributeSet: ...
    @staticmethod
    def defaultAttributes_Point2D() -> PySide2.QtQuick.QSGGeometry.AttributeSet: ...
    @staticmethod
    def defaultAttributes_TexturedPoint2D() -> PySide2.QtQuick.QSGGeometry.AttributeSet: ...
    def drawingMode(self) -> int: ...
    def indexCount(self) -> int: ...
    def indexData(self) -> int: ...
    def indexDataAsUInt(self) -> typing.List: ...
    def indexDataAsUShort(self) -> typing.List: ...
    def indexDataPattern(self) -> PySide2.QtQuick.QSGGeometry.DataPattern: ...
    def indexType(self) -> int: ...
    def lineWidth(self) -> float: ...
    def markIndexDataDirty(self): ...
    def markVertexDataDirty(self): ...
    def setDrawingMode(self, mode:int): ...
    def setIndexDataPattern(self, p:PySide2.QtQuick.QSGGeometry.DataPattern): ...
    def setLineWidth(self, w:float): ...
    def setVertexDataPattern(self, p:PySide2.QtQuick.QSGGeometry.DataPattern): ...
    def sizeOfIndex(self) -> int: ...
    def sizeOfVertex(self) -> int: ...
    @staticmethod
    def updateColoredRectGeometry(g:PySide2.QtQuick.QSGGeometry, rect:PySide2.QtCore.QRectF): ...
    @staticmethod
    def updateRectGeometry(g:PySide2.QtQuick.QSGGeometry, rect:PySide2.QtCore.QRectF): ...
    @staticmethod
    def updateTexturedRectGeometry(g:PySide2.QtQuick.QSGGeometry, rect:PySide2.QtCore.QRectF, sourceRect:PySide2.QtCore.QRectF): ...
    def vertexCount(self) -> int: ...
    def vertexData(self) -> int: ...
    def vertexDataAsColoredPoint2D(self) -> PySide2.QtQuick.QSGGeometry.ColoredPoint2D: ...
    def vertexDataAsPoint2D(self) -> PySide2.QtQuick.QSGGeometry.Point2D: ...
    def vertexDataAsTexturedPoint2D(self) -> PySide2.QtQuick.QSGGeometry.TexturedPoint2D: ...
    def vertexDataPattern(self) -> PySide2.QtQuick.QSGGeometry.DataPattern: ...


class QSGGeometryNode(PySide2.QtQuick.QSGBasicGeometryNode):

    def __init__(self): ...

    def inheritedOpacity(self) -> float: ...
    def renderOrder(self) -> int: ...
    def setInheritedOpacity(self, opacity:float): ...
    def setRenderOrder(self, order:int): ...


class QSGMaterialType(Shiboken.Object):

    def __init__(self): ...


class QSGNode(Shiboken.Object):
    BasicNodeType            : QSGNode = ... # 0x0
    GeometryNodeType         : QSGNode = ... # 0x1
    OwnedByParent            : QSGNode = ... # 0x1
    DirtyUsePreprocess       : QSGNode = ... # 0x2
    TransformNodeType        : QSGNode = ... # 0x2
    UsePreprocess            : QSGNode = ... # 0x2
    ClipNodeType             : QSGNode = ... # 0x3
    OpacityNodeType          : QSGNode = ... # 0x4
    RootNodeType             : QSGNode = ... # 0x5
    RenderNodeType           : QSGNode = ... # 0x6
    DirtySubtreeBlocked      : QSGNode = ... # 0x80
    DirtyMatrix              : QSGNode = ... # 0x100
    DirtyNodeAdded           : QSGNode = ... # 0x400
    DirtyNodeRemoved         : QSGNode = ... # 0x800
    DirtyGeometry            : QSGNode = ... # 0x1000
    DirtyMaterial            : QSGNode = ... # 0x2000
    DirtyOpacity             : QSGNode = ... # 0x4000
    DirtyForceUpdate         : QSGNode = ... # 0x8000
    DirtyPropagationMask     : QSGNode = ... # 0xc500
    OwnsGeometry             : QSGNode = ... # 0x10000
    OwnsMaterial             : QSGNode = ... # 0x20000
    OwnsOpaqueMaterial       : QSGNode = ... # 0x40000
    IsVisitableNode          : QSGNode = ... # 0x1000000

    class DirtyState(object): ...

    class DirtyStateBit(object):
        DirtyUsePreprocess       : QSGNode.DirtyStateBit = ... # 0x2
        DirtySubtreeBlocked      : QSGNode.DirtyStateBit = ... # 0x80
        DirtyMatrix              : QSGNode.DirtyStateBit = ... # 0x100
        DirtyNodeAdded           : QSGNode.DirtyStateBit = ... # 0x400
        DirtyNodeRemoved         : QSGNode.DirtyStateBit = ... # 0x800
        DirtyGeometry            : QSGNode.DirtyStateBit = ... # 0x1000
        DirtyMaterial            : QSGNode.DirtyStateBit = ... # 0x2000
        DirtyOpacity             : QSGNode.DirtyStateBit = ... # 0x4000
        DirtyForceUpdate         : QSGNode.DirtyStateBit = ... # 0x8000
        DirtyPropagationMask     : QSGNode.DirtyStateBit = ... # 0xc500

    class Flag(object):
        OwnedByParent            : QSGNode.Flag = ... # 0x1
        UsePreprocess            : QSGNode.Flag = ... # 0x2
        OwnsGeometry             : QSGNode.Flag = ... # 0x10000
        OwnsMaterial             : QSGNode.Flag = ... # 0x20000
        OwnsOpaqueMaterial       : QSGNode.Flag = ... # 0x40000
        IsVisitableNode          : QSGNode.Flag = ... # 0x1000000

    class Flags(object): ...

    class NodeType(object):
        BasicNodeType            : QSGNode.NodeType = ... # 0x0
        GeometryNodeType         : QSGNode.NodeType = ... # 0x1
        TransformNodeType        : QSGNode.NodeType = ... # 0x2
        ClipNodeType             : QSGNode.NodeType = ... # 0x3
        OpacityNodeType          : QSGNode.NodeType = ... # 0x4
        RootNodeType             : QSGNode.NodeType = ... # 0x5
        RenderNodeType           : QSGNode.NodeType = ... # 0x6

    @typing.overload
    def __init__(self): ...
    @typing.overload
    def __init__(self, type:PySide2.QtQuick.QSGNode.NodeType): ...

    def appendChildNode(self, node:PySide2.QtQuick.QSGNode): ...
    def childAtIndex(self, i:int) -> PySide2.QtQuick.QSGNode: ...
    def childCount(self) -> int: ...
    def clearDirty(self): ...
    def dirtyState(self) -> PySide2.QtQuick.QSGNode.DirtyState: ...
    def firstChild(self) -> PySide2.QtQuick.QSGNode: ...
    def flags(self) -> PySide2.QtQuick.QSGNode.Flags: ...
    def insertChildNodeAfter(self, node:PySide2.QtQuick.QSGNode, after:PySide2.QtQuick.QSGNode): ...
    def insertChildNodeBefore(self, node:PySide2.QtQuick.QSGNode, before:PySide2.QtQuick.QSGNode): ...
    def isSubtreeBlocked(self) -> bool: ...
    def lastChild(self) -> PySide2.QtQuick.QSGNode: ...
    def markDirty(self, bits:PySide2.QtQuick.QSGNode.DirtyState): ...
    def nextSibling(self) -> PySide2.QtQuick.QSGNode: ...
    def parent(self) -> PySide2.QtQuick.QSGNode: ...
    def prependChildNode(self, node:PySide2.QtQuick.QSGNode): ...
    def preprocess(self): ...
    def previousSibling(self) -> PySide2.QtQuick.QSGNode: ...
    def removeAllChildNodes(self): ...
    def removeChildNode(self, node:PySide2.QtQuick.QSGNode): ...
    def reparentChildNodesTo(self, newParent:PySide2.QtQuick.QSGNode): ...
    def setFlag(self, arg__1:PySide2.QtQuick.QSGNode.Flag, arg__2:bool=...): ...
    def setFlags(self, arg__1:PySide2.QtQuick.QSGNode.Flags, arg__2:bool=...): ...
    def type(self) -> PySide2.QtQuick.QSGNode.NodeType: ...


class QSGOpacityNode(PySide2.QtQuick.QSGNode):

    def __init__(self): ...

    def combinedOpacity(self) -> float: ...
    def isSubtreeBlocked(self) -> bool: ...
    def opacity(self) -> float: ...
    def setCombinedOpacity(self, opacity:float): ...
    def setOpacity(self, opacity:float): ...


class QSGSimpleRectNode(PySide2.QtQuick.QSGGeometryNode):

    @typing.overload
    def __init__(self): ...
    @typing.overload
    def __init__(self, rect:PySide2.QtCore.QRectF, color:PySide2.QtGui.QColor): ...

    def color(self) -> PySide2.QtGui.QColor: ...
    def rect(self) -> PySide2.QtCore.QRectF: ...
    def setColor(self, color:PySide2.QtGui.QColor): ...
    @typing.overload
    def setRect(self, rect:PySide2.QtCore.QRectF): ...
    @typing.overload
    def setRect(self, x:float, y:float, w:float, h:float): ...


class QSGSimpleTextureNode(PySide2.QtQuick.QSGGeometryNode):
    NoTransform              : QSGSimpleTextureNode = ... # 0x0
    MirrorHorizontally       : QSGSimpleTextureNode = ... # 0x1
    MirrorVertically         : QSGSimpleTextureNode = ... # 0x2

    class TextureCoordinatesTransformFlag(object):
        NoTransform              : QSGSimpleTextureNode.TextureCoordinatesTransformFlag = ... # 0x0
        MirrorHorizontally       : QSGSimpleTextureNode.TextureCoordinatesTransformFlag = ... # 0x1
        MirrorVertically         : QSGSimpleTextureNode.TextureCoordinatesTransformFlag = ... # 0x2

    class TextureCoordinatesTransformMode(object): ...

    def __init__(self): ...

    def filtering(self) -> PySide2.QtQuick.QSGTexture.Filtering: ...
    def ownsTexture(self) -> bool: ...
    def rect(self) -> PySide2.QtCore.QRectF: ...
    def setFiltering(self, filtering:PySide2.QtQuick.QSGTexture.Filtering): ...
    def setOwnsTexture(self, owns:bool): ...
    @typing.overload
    def setRect(self, rect:PySide2.QtCore.QRectF): ...
    @typing.overload
    def setRect(self, x:float, y:float, w:float, h:float): ...
    @typing.overload
    def setSourceRect(self, r:PySide2.QtCore.QRectF): ...
    @typing.overload
    def setSourceRect(self, x:float, y:float, w:float, h:float): ...
    def setTexture(self, texture:PySide2.QtQuick.QSGTexture): ...
    def setTextureCoordinatesTransform(self, mode:PySide2.QtQuick.QSGSimpleTextureNode.TextureCoordinatesTransformMode): ...
    def sourceRect(self) -> PySide2.QtCore.QRectF: ...
    def texture(self) -> PySide2.QtQuick.QSGTexture: ...
    def textureCoordinatesTransform(self) -> PySide2.QtQuick.QSGSimpleTextureNode.TextureCoordinatesTransformMode: ...


class QSGTexture(PySide2.QtCore.QObject):
    AnisotropyNone           : QSGTexture = ... # 0x0
    None_                    : QSGTexture = ... # 0x0
    Repeat                   : QSGTexture = ... # 0x0
    Anisotropy2x             : QSGTexture = ... # 0x1
    ClampToEdge              : QSGTexture = ... # 0x1
    Nearest                  : QSGTexture = ... # 0x1
    Anisotropy4x             : QSGTexture = ... # 0x2
    Linear                   : QSGTexture = ... # 0x2
    MirroredRepeat           : QSGTexture = ... # 0x2
    Anisotropy8x             : QSGTexture = ... # 0x3
    Anisotropy16x            : QSGTexture = ... # 0x4

    class AnisotropyLevel(object):
        AnisotropyNone           : QSGTexture.AnisotropyLevel = ... # 0x0
        Anisotropy2x             : QSGTexture.AnisotropyLevel = ... # 0x1
        Anisotropy4x             : QSGTexture.AnisotropyLevel = ... # 0x2
        Anisotropy8x             : QSGTexture.AnisotropyLevel = ... # 0x3
        Anisotropy16x            : QSGTexture.AnisotropyLevel = ... # 0x4

    class Filtering(object):
        None_                    : QSGTexture.Filtering = ... # 0x0
        Nearest                  : QSGTexture.Filtering = ... # 0x1
        Linear                   : QSGTexture.Filtering = ... # 0x2

    class WrapMode(object):
        Repeat                   : QSGTexture.WrapMode = ... # 0x0
        ClampToEdge              : QSGTexture.WrapMode = ... # 0x1
        MirroredRepeat           : QSGTexture.WrapMode = ... # 0x2

    def __init__(self): ...

    def anisotropyLevel(self) -> PySide2.QtQuick.QSGTexture.AnisotropyLevel: ...
    def bind(self): ...
    def comparisonKey(self) -> int: ...
    def convertToNormalizedSourceRect(self, rect:PySide2.QtCore.QRectF) -> PySide2.QtCore.QRectF: ...
    def filtering(self) -> PySide2.QtQuick.QSGTexture.Filtering: ...
    def hasAlphaChannel(self) -> bool: ...
    def hasMipmaps(self) -> bool: ...
    def horizontalWrapMode(self) -> PySide2.QtQuick.QSGTexture.WrapMode: ...
    def isAtlasTexture(self) -> bool: ...
    def mipmapFiltering(self) -> PySide2.QtQuick.QSGTexture.Filtering: ...
    def normalizedTextureSubRect(self) -> PySide2.QtCore.QRectF: ...
    def removedFromAtlas(self) -> PySide2.QtQuick.QSGTexture: ...
    def setAnisotropyLevel(self, level:PySide2.QtQuick.QSGTexture.AnisotropyLevel): ...
    def setFiltering(self, filter:PySide2.QtQuick.QSGTexture.Filtering): ...
    def setHorizontalWrapMode(self, hwrap:PySide2.QtQuick.QSGTexture.WrapMode): ...
    def setMipmapFiltering(self, filter:PySide2.QtQuick.QSGTexture.Filtering): ...
    def setVerticalWrapMode(self, vwrap:PySide2.QtQuick.QSGTexture.WrapMode): ...
    def textureId(self) -> int: ...
    def textureSize(self) -> PySide2.QtCore.QSize: ...
    def updateBindOptions(self, force:bool=...): ...
    def verticalWrapMode(self) -> PySide2.QtQuick.QSGTexture.WrapMode: ...


class QSGTextureProvider(PySide2.QtCore.QObject):

    def __init__(self): ...

    def texture(self) -> PySide2.QtQuick.QSGTexture: ...


class QSGTransformNode(PySide2.QtQuick.QSGNode):

    def __init__(self): ...

    def combinedMatrix(self) -> PySide2.QtGui.QMatrix4x4: ...
    def matrix(self) -> PySide2.QtGui.QMatrix4x4: ...
    def setCombinedMatrix(self, matrix:PySide2.QtGui.QMatrix4x4): ...
    def setMatrix(self, matrix:PySide2.QtGui.QMatrix4x4): ...

# eof
